import datetime
from applications.extensions import db

class WebChatWebhookSettings(db.Model):
    __tablename__ = 'admin_webchat_settings'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键Id')
    webhook_url = db.Column(db.String(128), nullable=False, comment='webhook推送地址')

    def json(self):
        return {
            'id': self.id,
            'webhook_url': self.webhook_url
        }