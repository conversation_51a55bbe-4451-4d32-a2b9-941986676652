from applications.extensions import db

class DigitalHumanFaceBase(db.Model):
    __tablename__ = "digital_human_facebase"
    idNumber = db.Column(db.String(18), primary_key=True, comment="证件号")
    faceLibraryId = db.Column(db.String(11), nullable=False, primary_key=True, comment="人脸库ID")
    name = db.Column(db.String(32), nullable=False, primary_key=False, comment="姓名")
    faissId = db.Column(db.Integer, nullable=False, primary_key=False, comment="faiss向量库Id")
    remark = db.Column(db.String(128), nullable=True, primary_key=False, comment="备注")
    faceLibraryName = db.Column(db.String(50), nullable=False, primary_key=False, comment="人脸库名称")
    sex = db.Column(db.String(1), nullable=True, primary_key=False, default="2", comment="性别")
    phone= db.Column(db.String(11), nullable=True, primary_key=False, comment="电话")