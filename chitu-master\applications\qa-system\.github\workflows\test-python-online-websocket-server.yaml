name: Python online websocket server

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/test-python-online-websocket-server.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/python/**'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/test-python-online-websocket-server.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/python/**'
  workflow_dispatch:

concurrency:
  group: python-online-websocket-server-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  python_online_websocket_server:
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os }} ${{ matrix.python-version }} ${{ matrix.model_type }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.7", "3.8", "3.9", "3.10", "3.11"]
        model_type: ["transducer", "paraformer", "zipformer2-ctc"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-python-${{ matrix.python-version }}

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Python dependencies
        shell: bash
        run: |
          python3 -m pip install --upgrade pip numpy pypinyin sentencepiece

      - name: Install sherpa-onnx
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          python3 -m pip install --no-deps --verbose .
          python3 -m pip install websockets

      - name: Start server for zipformer2 CTC models
        if: matrix.model_type == 'zipformer2-ctc'
        shell: bash
        run: |
          curl -O -L https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2
          tar xvf sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2
          rm sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2

          python3 ./python-api-examples/streaming_server.py \
            --zipformer2-ctc ./sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13/ctc-epoch-20-avg-1-chunk-16-left-128.onnx \
            --tokens=./sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13/tokens.txt &
          echo "sleep 10 seconds to wait the server start"
          sleep 10

      - name: Start client for zipformer2 CTC models
        if: matrix.model_type == 'zipformer2-ctc'
        shell: bash
        run: |
          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13/test_wavs/DEV_T0000000000.wav

      - name: Start server for transducer models
        if: matrix.model_type == 'transducer'
        shell: bash
        run: |
          GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-streaming-zipformer-en-2023-06-26
          cd sherpa-onnx-streaming-zipformer-en-2023-06-26
          git lfs pull --include "*.onnx"
          cd ..

          python3 ./python-api-examples/streaming_server.py \
            --encoder ./sherpa-onnx-streaming-zipformer-en-2023-06-26/encoder-epoch-99-avg-1-chunk-16-left-128.onnx \
            --decoder ./sherpa-onnx-streaming-zipformer-en-2023-06-26/decoder-epoch-99-avg-1-chunk-16-left-128.onnx \
            --joiner ./sherpa-onnx-streaming-zipformer-en-2023-06-26/joiner-epoch-99-avg-1-chunk-16-left-128.onnx \
            --tokens ./sherpa-onnx-streaming-zipformer-en-2023-06-26/tokens.txt &
          echo "sleep 10 seconds to wait the server start"
          sleep 10

      - name: Start client for transducer models
        if: matrix.model_type == 'transducer'
        shell: bash
        run: |
          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-zipformer-en-2023-06-26/test_wavs/0.wav

      - name: Start server for paraformer models
        if: matrix.model_type == 'paraformer'
        shell: bash
        run: |
          GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-streaming-paraformer-bilingual-zh-en
          cd sherpa-onnx-streaming-paraformer-bilingual-zh-en
          git lfs pull --include "*.onnx"
          cd ..

          python3 ./python-api-examples/streaming_server.py \
            --tokens ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/tokens.txt \
            --paraformer-encoder ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/encoder.int8.onnx \
            --paraformer-decoder ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/decoder.int8.onnx &

          echo "sleep 10 seconds to wait the server start"
          sleep 10

      - name: Start client for paraformer models
        if: matrix.model_type == 'paraformer'
        shell: bash
        run: |
          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/test_wavs/0.wav

          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/test_wavs/1.wav

          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/test_wavs/2.wav

          python3 ./python-api-examples/online-websocket-client-decode-file.py \
            ./sherpa-onnx-streaming-paraformer-bilingual-zh-en/test_wavs/3.wav
