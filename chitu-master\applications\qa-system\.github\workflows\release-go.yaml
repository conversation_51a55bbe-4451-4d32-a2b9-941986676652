name: release-go

on:
  workflow_dispatch:

concurrency:
  group: release-go-${{ github.ref }}
  cancel-in-progress: true

jobs:
  release_go:
    name: Release go
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Add SSH key
        run: |
          mkdir -p ~/.ssh/
          cp scripts/go/ssh_config ~/.ssh/config
          echo "${{ secrets.MY_GITHUB_SSH_KEY }}" > ~/.ssh/github && chmod 600 ~/.ssh/github
          ssh github.com || true

      - name: Release
        shell: bash
        run: |
          cd scripts/go
          ./release.sh
