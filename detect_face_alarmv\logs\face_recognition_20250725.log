2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 开始加载人脸检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/retinaface_mob.rknn
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 开始加载人脸检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/retinaface_mob.rknn
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 人脸检测模型加载成功
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 测试检测模型...
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 模型测试成功：输出数量 3
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 开始加载人脸识别模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/face.rknn
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 开始加载人脸检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/retinaface_mob.rknn
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 人脸识别模型加载成功
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 开始加载人体检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/people.rknn
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 人体检测模型加载成功
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] FAISS索引加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/face_index.faiss
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] FAISS索引包含 1408 个人脸特征
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ID-姓名映射加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/id_name_map.pkl
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 包含 1408 个人员信息
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人员1: 索引ID=0, 人员ID=丁世均, 姓名=丁世均
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人员2: 索引ID=1, 人员ID=丁仲礼, 姓名=丁仲礼
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人员3: 索引ID=2, 人员ID=丁昂敏乌, 姓名=丁昂敏乌
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人员4: 索引ID=3, 人员ID=丁薛祥, 姓名=丁薛祥
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人员5: 索引ID=4, 人员ID=万立俊, 姓名=万立俊
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人脸数据库初始化完成，包含1408个人员
2025-07-25 14:33:03 - face_recognition - I - ==================================================
2025-07-25 14:33:03 - face_recognition - I - [FACE_DATABASE_STATUS] 人脸数据库状态报告
2025-07-25 14:33:03 - face_recognition - I - ==================================================
2025-07-25 14:33:03 - face_recognition - I - [FAISS] 索引状态: 已加载
2025-07-25 14:33:03 - face_recognition - I - [FAISS] 特征向量数量: 1408
2025-07-25 14:33:03 - face_recognition - I - [FAISS] 是否可用: True
2025-07-25 14:33:03 - face_recognition - I - [MAPPING] 人员映射数量: 1408
2025-07-25 14:33:03 - face_recognition - I - [MAPPING] 人员信息示例:
2025-07-25 14:33:03 - face_recognition - I -   - 索引0: 丁世均 (ID:丁世均, 库:default)
2025-07-25 14:33:03 - face_recognition - I -   - 索引1: 丁仲礼 (ID:丁仲礼, 库:default)
2025-07-25 14:33:03 - face_recognition - I -   - 索引2: 丁昂敏乌 (ID:丁昂敏乌, 库:default)
2025-07-25 14:33:03 - face_recognition - I - [MODELS] 检测模型: 已加载
2025-07-25 14:33:03 - face_recognition - I - [MODELS] 识别模型: 已加载
2025-07-25 14:33:03 - face_recognition - I - [STATUS] 人脸识别算法就绪
2025-07-25 14:33:03 - face_recognition - I - ==================================================
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 人脸识别算法初始化完成
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 输入尺寸: [640, 640]
2025-07-25 14:33:03 - face_recognition - I - 加载人脸识别推理模块
2025-07-25 14:33:03 - face_recognition - I - 加载算法图片前处理模块
2025-07-25 14:33:03 - face_recognition - I - 加载人脸识别后处理模块
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 人脸检测模型加载成功
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 测试检测模型...
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] ✅ 人脸检测模型加载成功
2025-07-25 14:33:03 - face_recognition - I - [FACE_INIT] 测试检测模型...
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 模型测试成功：输出数量 3
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 开始加载人脸识别模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/face.rknn
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 模型测试成功：输出数量 3
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 开始加载人脸识别模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/face.rknn
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 人脸识别模型加载成功
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 开始加载人体检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/people.rknn
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 人脸识别模型加载成功
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 开始加载人体检测模型: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/weights/people.rknn
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 人体检测模型加载成功
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] FAISS索引加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/face_index.faiss
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] FAISS索引包含 1408 个人脸特征
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ✅ 人体检测模型加载成功
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ID-姓名映射加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/id_name_map.pkl
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 包含 1408 个人员信息
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员1: 索引ID=0, 人员ID=丁世均, 姓名=丁世均
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员2: 索引ID=1, 人员ID=丁仲礼, 姓名=丁仲礼
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员3: 索引ID=2, 人员ID=丁昂敏乌, 姓名=丁昂敏乌
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员4: 索引ID=3, 人员ID=丁薛祥, 姓名=丁薛祥
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员5: 索引ID=4, 人员ID=万立俊, 姓名=万立俊
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人脸数据库初始化完成，包含1408个人员
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FACE_DATABASE_STATUS] 人脸数据库状态报告
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 索引状态: 已加载
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] FAISS索引加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/face_index.faiss
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 特征向量数量: 1408
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] FAISS索引包含 1408 个人脸特征
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 是否可用: True
2025-07-25 14:33:04 - face_recognition - I - [MAPPING] 人员映射数量: 1408
2025-07-25 14:33:04 - face_recognition - I - [MAPPING] 人员信息示例:
2025-07-25 14:33:04 - face_recognition - I -   - 索引0: 丁世均 (ID:丁世均, 库:default)
2025-07-25 14:33:04 - face_recognition - I -   - 索引1: 丁仲礼 (ID:丁仲礼, 库:default)
2025-07-25 14:33:04 - face_recognition - I -   - 索引2: 丁昂敏乌 (ID:丁昂敏乌, 库:default)
2025-07-25 14:33:04 - face_recognition - I - [MODELS] 检测模型: 已加载
2025-07-25 14:33:04 - face_recognition - I - [MODELS] 识别模型: 已加载
2025-07-25 14:33:04 - face_recognition - I - [STATUS] 人脸识别算法就绪
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人脸识别算法初始化完成
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 输入尺寸: [640, 640]
2025-07-25 14:33:04 - face_recognition - I - 加载人脸识别推理模块
2025-07-25 14:33:04 - face_recognition - I - 加载算法图片前处理模块
2025-07-25 14:33:04 - face_recognition - I - 加载人脸识别后处理模块
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] ID-姓名映射加载成功: /userdata/lgw/model_repository/15000/chitu2-model-framework/models/detect_face_alarm/v2_0_0/data/id_name_map.pkl
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 包含 1408 个人员信息
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员1: 索引ID=0, 人员ID=丁世均, 姓名=丁世均
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员2: 索引ID=1, 人员ID=丁仲礼, 姓名=丁仲礼
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员3: 索引ID=2, 人员ID=丁昂敏乌, 姓名=丁昂敏乌
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员4: 索引ID=3, 人员ID=丁薛祥, 姓名=丁薛祥
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人员5: 索引ID=4, 人员ID=万立俊, 姓名=万立俊
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人脸数据库初始化完成，包含1408个人员
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FACE_DATABASE_STATUS] 人脸数据库状态报告
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 索引状态: 已加载
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 特征向量数量: 1408
2025-07-25 14:33:04 - face_recognition - I - [FAISS] 是否可用: True
2025-07-25 14:33:04 - face_recognition - I - [MAPPING] 人员映射数量: 1408
2025-07-25 14:33:04 - face_recognition - I - [MAPPING] 人员信息示例:
2025-07-25 14:33:04 - face_recognition - I -   - 索引0: 丁世均 (ID:丁世均, 库:default)
2025-07-25 14:33:04 - face_recognition - I -   - 索引1: 丁仲礼 (ID:丁仲礼, 库:default)
2025-07-25 14:33:04 - face_recognition - I -   - 索引2: 丁昂敏乌 (ID:丁昂敏乌, 库:default)
2025-07-25 14:33:04 - face_recognition - I - [MODELS] 检测模型: 已加载
2025-07-25 14:33:04 - face_recognition - I - [MODELS] 识别模型: 已加载
2025-07-25 14:33:04 - face_recognition - I - [STATUS] 人脸识别算法就绪
2025-07-25 14:33:04 - face_recognition - I - ==================================================
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 人脸识别算法初始化完成
2025-07-25 14:33:04 - face_recognition - I - [FACE_INIT] 输入尺寸: [640, 640]
2025-07-25 14:33:04 - face_recognition - I - 加载人脸识别推理模块
2025-07-25 14:33:04 - face_recognition - I - 加载算法图片前处理模块
2025-07-25 14:33:04 - face_recognition - I - 加载人脸识别后处理模块
2025-07-25 14:33:40 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:40 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:40 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:41 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:41 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:41 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:42 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:42 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:42 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:42 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:42 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:42 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:42 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:43 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:43 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:43 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:43 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:43 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:43 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:43 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:43 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:43 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:44 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:44 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:44 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:44 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 是否触发报警: False
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:33:44 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:33:44 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:33:44 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:33:44 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:04 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:04 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:04 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:04 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:05 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:05 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:05 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:05 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:06 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:06 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:06 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:06 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:07 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:07 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:07 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 开始人脸识别推理...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 开始人脸检测...
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 使用RKNN模型进行人脸检测
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:07 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 是否触发报警: False
2025-07-25 14:34:07 - face_recognition - I - [FACE_DETECTION] 人脸检测完成，有效检测数量: 0
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 人脸检测完成，检测到 0 个人脸
2025-07-25 14:34:07 - face_recognition - I - [FACE_INFERENCE] 未检测到人脸，返回空列表
2025-07-25 14:34:07 - face_recognition - I - 原始检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 过滤后检测框数量: 0
2025-07-25 14:34:07 - face_recognition - I - 是否触发报警: False
