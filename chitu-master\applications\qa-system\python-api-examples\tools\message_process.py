import json
from tools.extract_table_info import get_sqlite_table_info
from tools.message_struct import (answer_push_json_message_struct,
                                  question_push_json_message_struct,
                                  command_word_push_json_message_struct,
                                  qa_empty_message_push_struct,
                                  face_exit_wake_up_push_struct,
                                  start_qa_service_push_struct,
                                  key_word_push_struct,
                                  exit_attendance_mode_push_struct)

#处理机器人发来的消息
async def robot_message_process(client,result):
    pear_path = "./../../../pear.db"
    basic_setting_table_name = "admin_digital_human_basic_setting"
    qa_pairs_table_name = "admin_digital_human_interactive"
    attendance_messageType = "attendanceMode"
    muteAssistantMode = "muteAssistantMode"
    muteUnattendedMode = "muteUnattendedMode"
    enterPublicity = "enterPublicity"
    exitPublicity = "exitPublicity"
    command_words_dict = json.loads(get_sqlite_table_info(pear_path,basic_setting_table_name)['command_words'].tolist()[0])
    attendance_word = command_words_dict[attendance_messageType]
    muteAssistant_word = command_words_dict[muteAssistantMode]
    """
    muteAssistantModeReplay = get_sqlite_table_info(pear_path, basic_setting_table_name)['muteAssistantModeReplay'][0]
    muteUnattendedModeReplay = get_sqlite_table_info(pear_path, basic_setting_table_name)['muteUnattendedModeReplay'][0]
    """
    muteUnattended_word = command_words_dict[muteUnattendedMode]
    enterPublicity_word = command_words_dict[enterPublicity]
    exitPublicity_word = command_words_dict[exitPublicity]
    exit_attendance_word = command_words_dict["exitAttendanceMode"]
    #print(muteAssistant_word,muteUnattended_word,muteAssistantModeReplay,muteAssistantModeReplay)
    del command_words_dict["attendanceMode"]
    del command_words_dict["muteAssistantMode"]
    del command_words_dict["muteUnattendedMode"]
    del command_words_dict["enterPublicity"]
    del command_words_dict["exitPublicity"]
    
    if client.get_propaganda_status() != "1" and client.get_attendanceStatus() != "1":
        for messageType,word in command_words_dict.items():
            if word in result:
                try:
                    await client.send_message(command_word_push_json_message_struct(messageType,result,word))
                    await client.send_message(qa_empty_message_push_struct(200,word))
                    #await client.change_status()
                    break
                except Exception as e:
                    pass
                    #logger.log_message('error', '命令词回复失败。')
                    
        if muteAssistant_word in result:
            try:
                await client.send_message(qa_empty_message_push_struct(200,"已进入助理模式。",2))
                await client.send_message(command_word_push_json_message_struct(muteAssistantMode,result,muteAssistant_word))
                #logger.log_message('info', '切换助理模式成功。')
            except:
                pass
                #logger.log_message('error', '切换助理模式失败。')
                
        elif muteUnattended_word in result:       
            try:
                await client.send_message(qa_empty_message_push_struct(200,"已进入无人值守模式。",1))
                await client.send_message(command_word_push_json_message_struct(muteUnattendedMode,result,muteUnattended_word))
                #logger.log_message('info', '切换无人值守模式成功。')
            except:
                pass
                #logger.log_message('error', '切换无人值守模式失败。')

        elif enterPublicity_word in result:
            await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))
            #await client.send_message(command_word_push_json_message_struct(enterPublicity,result,enterPublicity_word))
            await client.change_enter_propaganda_status()

        elif exitPublicity_word in result:
            await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
            #await client.send_message(command_word_push_json_message_struct(exitPublicity,result,exitPublicity_word))
            await client.change_exit_propaganda_status()  
            
        elif attendance_word in result:
            try:
                await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                await client.send_message(command_word_push_json_message_struct(attendance_messageType,result,attendance_word))
                await client.change_enterAttendanceStatus()
                #logger.log_message('info', '往赤兔发送打卡命令词发送成功。')
            except:
                pass
                #logger.log_message('error', '往赤兔发送打卡命令词发送失败。')          
        
    elif client.get_propaganda_status() == "1" and client.get_attendanceStatus() != "1":
        
        if enterPublicity_word in result:
            await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))
            #await client.send_message(command_word_push_json_message_struct(enterPublicity,result,enterPublicity_word))
            await client.change_enter_propaganda_status()

        elif exitPublicity_word in result:
            await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
            #await client.send_message(command_word_push_json_message_struct(exitPublicity,result,exitPublicity_word))
            await client.change_exit_propaganda_status()
                    
        elif attendance_word in result:
            try:
                await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                await client.send_message(command_word_push_json_message_struct(attendance_messageType,result,attendance_word))
                await client.change_enterAttendanceStatus()
                #logger.log_message('info', '往赤兔发送打卡命令词发送成功。')
            except:
                pass
                #logger.log_message('error', '往赤兔发送打卡命令词发送失败。')
                
    elif client.get_attendanceStatus() == "1":
        
        if exit_attendance_word in result:
            await client.send_message(qa_empty_message_push_struct(200,"已退出打卡模式"))
            await client.send_message(exit_attendance_mode_push_struct())
            await client.send_message(command_word_push_json_message_struct("exitAttendanceMode",result,exit_attendance_word))
            await client.change_exitAttendanceStatus()
            
        elif attendance_word in result:
            try:
                await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                await client.send_message(command_word_push_json_message_struct(attendance_messageType,result,attendance_word))
                await client.change_enterAttendanceStatus()
                #logger.log_message('info', '往赤兔发送打卡命令词发送成功。')
            except:
                pass
                #logger.log_message('error', '往赤兔发送打卡命令词发送失败。')
