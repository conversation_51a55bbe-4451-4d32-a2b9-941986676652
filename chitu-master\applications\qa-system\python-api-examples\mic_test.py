import sounddevice as sd

devices = sd.query_devices()
print(devices)
sd.default.device[0] = 1
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:
    
    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:

    samples, _ = s.read(1600)
    print(samples)
s.stop()
s.close()

"""
with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as s:
    while True:
        samples, _ = s.read(1600)
        print("1:",samples)
"""
