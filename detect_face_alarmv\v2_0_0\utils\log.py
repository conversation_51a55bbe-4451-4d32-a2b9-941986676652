"""
日志管理模块
提供统一的日志配置和管理功能
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


def setup_logger(name: str, log_level: str = "INFO", 
                log_file: Optional[str] = None) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_level: 日志级别
        log_file: 日志文件路径
    
    Returns:
        配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_default_log_file(module_name: str) -> str:
    """
    获取默认日志文件路径
    
    Args:
        module_name: 模块名称
    
    Returns:
        日志文件路径
    """
    # 获取当前脚本目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(current_dir))
    
    # 创建日志目录
    log_dir = os.path.join(parent_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"{module_name}_{timestamp}.log")
    
    return log_file


# 创建默认日志记录器
default_logger = setup_logger(
    "face_recognition_v2",
    log_file=get_default_log_file("face_recognition")
)


class Logger:
    """日志管理类，兼容玩手机算法格式"""

    def __init__(self, name="face_recognition", log_dir="logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = setup_logger(name, log_file=get_default_log_file(name))

    def debug(self, message):
        """调试日志"""
        self.logger.debug(message)

    def info(self, message):
        """信息日志"""
        self.logger.info(message)

    def warning(self, message):
        """警告日志"""
        self.logger.warning(message)

    def error(self, message):
        """错误日志"""
        self.logger.error(message)

    def critical(self, message):
        """严重错误日志"""
        self.logger.critical(message)


# 创建全局日志实例，兼容玩手机算法格式
mylog = Logger("face_recognition")
