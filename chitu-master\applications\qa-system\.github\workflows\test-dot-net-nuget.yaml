name: test-dot-net-nuget

on:
  workflow_dispatch:

  schedule:
    # minute (0-59)
    # hour (0-23)
    # day of the month (1-31)
    # month (1-12)
    # day of the week (0-6)
    # nightly build at 23:50 UTC time every day
    - cron: "50 23 * * *"

concurrency:
  group: test-dot-net-nuget
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test-dot-net-nuget:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 6.0.x

      - name: Check dotnet
        run: dotnet --info

      - name: Run tests
        shell: bash
        run: |
          .github/scripts/test-dot-net.sh

      - uses: actions/upload-artifact@v4
        with:
          name: dot-net-tts-generated-test-files-${{ matrix.os }}
          path: tts
