#!/usr/bin/env python3
"""
逐步调试人脸检测 - 从模型加载到最终结果
"""

import sys
import os
import cv2
import numpy as np
import base64
import time

def create_real_face_image():
    """创建包含真实人脸特征的测试图像"""
    # 创建一个更真实的人脸图像
    img = np.ones((480, 640, 3), dtype=np.uint8) * 200
    
    # 绘制更真实的人脸
    # 脸部轮廓
    cv2.ellipse(img, (320, 240), (80, 100), 0, 0, 360, (220, 200, 180), -1)
    
    # 眼睛
    cv2.ellipse(img, (290, 220), (15, 8), 0, 0, 360, (50, 50, 50), -1)
    cv2.ellipse(img, (350, 220), (15, 8), 0, 0, 360, (50, 50, 50), -1)
    
    # 眼球
    cv2.circle(img, (290, 220), 5, (0, 0, 0), -1)
    cv2.circle(img, (350, 220), 5, (0, 0, 0), -1)
    
    # 鼻子
    cv2.ellipse(img, (320, 240), (8, 15), 0, 0, 360, (200, 180, 160), -1)
    
    # 嘴巴
    cv2.ellipse(img, (320, 270), (20, 8), 0, 0, 360, (150, 100, 100), -1)
    
    # 添加一些噪声使其更真实
    noise = np.random.normal(0, 10, img.shape).astype(np.uint8)
    img = cv2.add(img, noise)
    
    return img

def step1_test_model_loading():
    """步骤1: 测试模型加载"""
    print("🔍 步骤1: 测试模型加载")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.utils_rknn.utils import load_model
        
        model_path = 'detect_face_alarm/v2_0_0/weights/retinaface_mob.rknn'
        
        print(f"  - 模型路径: {model_path}")
        print(f"  - 文件存在: {os.path.exists(model_path)}")
        
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path) / (1024 * 1024)
            print(f"  - 文件大小: {file_size:.2f} MB")
            
            # 尝试加载模型
            print("  - 开始加载模型...")
            model = load_model(model_path, 0)
            
            if model is not None:
                print("  ✅ 模型加载成功")
                return model, True
            else:
                print("  ❌ 模型加载失败")
                return None, False
        else:
            print("  ❌ 模型文件不存在")
            return None, False
            
    except Exception as e:
        print(f"  ❌ 模型加载异常: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def step2_test_image_preprocessing():
    """步骤2: 测试图像预处理"""
    print("\n🔍 步骤2: 测试图像预处理")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.utils_rknn.utils import procss_img
        
        # 创建测试图像
        test_img = create_real_face_image()
        print(f"  - 原始图像尺寸: {test_img.shape}")
        print(f"  - 原始图像类型: {test_img.dtype}")
        print(f"  - 原始图像范围: [{np.min(test_img)}, {np.max(test_img)}]")
        
        # 预处理
        print("  - 开始图像预处理...")
        processed_img, or_img = procss_img(test_img)
        
        print(f"  - 处理后图像尺寸: {processed_img.shape}")
        print(f"  - 处理后图像类型: {processed_img.dtype}")
        print(f"  - 处理后图像范围: [{np.min(processed_img):.3f}, {np.max(processed_img):.3f}]")
        print(f"  - 原始图像尺寸: {or_img.shape}")
        
        print("  ✅ 图像预处理成功")
        return test_img, processed_img, or_img, True
        
    except Exception as e:
        print(f"  ❌ 图像预处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, False

def step3_test_model_inference(model, processed_img):
    """步骤3: 测试模型推理"""
    print("\n🔍 步骤3: 测试模型推理")
    print("=" * 50)
    
    try:
        if model is None:
            print("  ❌ 模型未加载，跳过推理测试")
            return None, False
            
        print("  - 开始RKNN推理...")
        print(f"  - 输入形状: {processed_img.shape}")
        
        # 直接调用RKNN推理
        outputs = model.inference(inputs=[processed_img])
        
        print(f"  - 推理输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            print(f"  - 输出{i}形状: {np.array(output).shape}")
            print(f"  - 输出{i}类型: {type(output)}")
            print(f"  - 输出{i}范围: [{np.min(output):.6f}, {np.max(output):.6f}]")
        
        print("  ✅ RKNN推理成功")
        return outputs, True
        
    except Exception as e:
        print(f"  ❌ RKNN推理失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def step4_test_output_decoding(outputs):
    """步骤4: 测试输出解码"""
    print("\n🔍 步骤4: 测试输出解码")
    print("=" * 50)
    
    try:
        if outputs is None:
            print("  ❌ 推理输出为空，跳过解码测试")
            return None, False
            
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.utils_rknn.utils import _decode_outputs
        
        # 测试不同的置信度阈值
        thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
        
        for conf_thres in thresholds:
            print(f"\n  - 测试置信度阈值: {conf_thres}")
            
            try:
                boxes = _decode_outputs(outputs, conf_thres=conf_thres, iou_thres=0.3)
                print(f"    检测到 {len(boxes)} 个框")
                
                if len(boxes) > 0:
                    print(f"    框的形状: {np.array(boxes).shape}")
                    for i, box in enumerate(boxes[:3]):  # 只显示前3个
                        if len(box) >= 5:
                            x1, y1, x2, y2, conf = box[:5]
                            print(f"    框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")
                
            except Exception as e:
                print(f"    ❌ 阈值{conf_thres}解码失败: {e}")
        
        # 使用最低阈值进行最终测试
        final_boxes = _decode_outputs(outputs, conf_thres=0.1, iou_thres=0.3)
        print(f"\n  ✅ 解码测试完成，最终检测到 {len(final_boxes)} 个框")
        return final_boxes, True
        
    except Exception as e:
        print(f"  ❌ 输出解码失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def step5_test_complete_pipeline():
    """步骤5: 测试完整流程"""
    print("\n🔍 步骤5: 测试完整流程")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.utils_rknn.utils import predict_face, load_model
        
        # 加载模型
        model_path = 'detect_face_alarm/v2_0_0/weights/retinaface_mob.rknn'
        model = load_model(model_path, 0)
        
        if model is None:
            print("  ❌ 模型加载失败")
            return False
            
        # 创建测试图像
        test_img = create_real_face_image()
        
        # 测试不同的置信度阈值
        thresholds = [0.1, 0.3, 0.5, 0.7]
        
        for conf_thres in thresholds:
            print(f"\n  - 测试完整流程，置信度阈值: {conf_thres}")
            
            try:
                boxes = predict_face(conf_thres, 0.3, test_img, [640, 640], model)
                print(f"    检测结果: {len(boxes)} 个人脸")
                
                for i, box in enumerate(boxes[:3]):
                    if len(box) >= 5:
                        x1, y1, x2, y2, conf = box[:5]
                        print(f"    人脸{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")
                        
            except Exception as e:
                print(f"    ❌ 完整流程失败: {e}")
        
        print("  ✅ 完整流程测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step6_test_real_camera_image():
    """步骤6: 测试真实摄像头图像"""
    print("\n🔍 步骤6: 测试真实摄像头图像处理")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建推理实例
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        print(f"  - 检测模型状态: {'已加载' if rknn_inference.detection_model else '未加载'}")
        print(f"  - 置信度阈值: {rknn_inference.confidence}")
        print(f"  - NMS阈值: {rknn_inference.nms_threshold}")
        
        # 创建测试图像（模拟摄像头输入）
        test_img = create_real_face_image()
        gain = [1.0, 0, 0]  # 简单的gain参数
        
        print("  - 开始推理...")
        result = rknn_inference.run(test_img, gain, test_img.shape)
        
        print(f"  - 推理结果: {len(result)} 个检测框")
        
        for i, detection in enumerate(result[:3]):
            if len(detection) >= 5:
                x1, y1, x2, y2, conf = detection[:5]
                print(f"    检测框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")
        
        print("  ✅ 真实摄像头图像测试完成")
        return len(result) > 0
        
    except Exception as e:
        print(f"  ❌ 真实摄像头图像测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🚀 人脸检测逐步调试")
    print("=" * 70)
    
    # 执行所有步骤
    steps = [
        ("模型加载", step1_test_model_loading),
        ("图像预处理", step2_test_image_preprocessing),
        ("完整流程", step5_test_complete_pipeline),
        ("真实摄像头", step6_test_real_camera_image),
    ]
    
    results = {}
    model = None
    processed_img = None
    outputs = None
    
    for step_name, step_func in steps:
        try:
            print(f"\n{'='*20} {step_name} {'='*20}")
            
            if step_name == "模型加载":
                model, result = step_func()
                results[step_name] = result
            elif step_name == "图像预处理":
                test_img, processed_img, or_img, result = step_func()
                results[step_name] = result
                if result and model:
                    outputs, inference_result = step3_test_model_inference(model, processed_img)
                    if inference_result:
                        step4_test_output_decoding(outputs)
            else:
                result = step_func()
                results[step_name] = result
                
        except Exception as e:
            print(f"❌ {step_name}步骤异常: {e}")
            results[step_name] = False
    
    # 输出总结
    print("\n" + "=" * 70)
    print("调试总结")
    print("=" * 70)
    
    for step_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{step_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有步骤都通过，人脸检测应该正常工作！")
    else:
        print("⚠️ 部分步骤失败，需要进一步调试")

if __name__ == "__main__":
    main()
