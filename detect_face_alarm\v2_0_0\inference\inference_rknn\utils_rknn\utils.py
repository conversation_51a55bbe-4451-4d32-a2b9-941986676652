import cv2
import random
import numpy as np
from rknnlite.api import RKNNLite
from hide_warnings import hide_warnings


@hide_warnings
def inference_del_wall(rknn_model, img):
    """RKNN推理函数（保持兼容性）"""
    try:
        # 这个函数现在只是为了兼容性，实际推理在predict_face中直接调用
        return rknn_model.inference(inputs=[img])
    except Exception as e:
        print(f"❌ RKNN推理异常: {e}")
        return None


def auto_resize(img, new_w, new_h):
    """自动调整图像尺寸"""
    h, w = img.shape[:2]
    scale = min(new_w / w, new_h / h)
    new_size = tuple(map(int, np.array(img.shape[:2][::-1]) * scale))
    return cv2.resize(img, new_size), scale


def letterbox(img, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
    """
    letterbox变换，保持宽高比的resize
    """
    shape = img.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better test mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding

    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
    return img, ratio, (dw, dh)


def load_model(model_path, number):
    """
    加载RKNN模型 - 参考chitu-master实现
    Args:
        model_path: 模型文件路径
        number: 进程编号，用于分配NPU核心
    Returns:
        rknn_lite: 加载好的RKNN模型实例
    """
    try:
        print(f"开始加载RKNN模型: {model_path}")

        # Create RKNN object
        rknn_lite = RKNNLite()

        # 加载模型文件
        ret = rknn_lite.load_rknn(model_path)
        if ret != 0:
            print(f"❌ RKNN模型文件加载失败，返回码: {ret}")
            return None
        print(f"✅ RKNN模型文件加载成功")

        # 初始化运行时 - 使用NPU核心分配（参考其他算法实现）
        if number == 0:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
        elif number == 1:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_1)
        elif number == 2:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_2)
        elif number == 3:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
        elif number == 4:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_1)
        elif number == 5:
            ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_2)
        elif number >= 10:
            # 人脸识别模型使用不同的核心
            core_num = (number - 10) % 3
            if core_num == 0:
                ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
            elif core_num == 1:
                ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_1)
            else:
                ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_2)
        else:
            # 默认使用自动分配
            ret = rknn_lite.init_runtime()

        if ret != 0:
            print(f"❌ RKNN运行时初始化失败，返回码: {ret}")
            return None

        print(f"✅ RKNN模型完全加载成功: {model_path}")
        return rknn_lite

    except Exception as e:
        print(f"RKNN模型加载失败: {e}")
        return None


def predict_face(confidence, nms_threshold, img, wh, rknn_model):
    """
    修复版人脸检测函数 - 解决坐标转换和预处理问题
    Args:
        confidence: 置信度阈值
        nms_threshold: NMS阈值
        img: 输入图像 (letterbox处理后的图像)
        wh: 输入尺寸 [640, 640]
        rknn_model: RKNN模型
    Returns:
        检测结果 (letterbox坐标系)
    """
    try:
        if rknn_model is None:
            print("❌ RKNN模型为空，无法进行人脸检测")
            return []

        print(f"[FACE_DETECT] 开始人脸检测，输入图像: {img.shape}")

        # 修复：直接使用letterbox处理后的图像进行预处理
        # 不要重复进行letterbox变换
        processed_img = preprocess_for_rknn(img)
        print(f"[FACE_DETECT] 预处理完成: {processed_img.shape}")

        # RKNN推理
        outputs = rknn_model.inference(inputs=[processed_img])

        if outputs is None:
            print("❌ RKNN推理返回None")
            return []

        print(f"[FACE_DETECT] RKNN推理输出: {len(outputs)} 个输出")
        for i, output in enumerate(outputs):
            if hasattr(output, 'shape'):
                print(f"  输出{i}: {output.shape}")

        # 解码检测结果
        boxes = _decode_outputs(outputs, confidence, nms_threshold)
        print(f"[FACE_DETECT] 解码后检测结果: {len(boxes)} 个人脸")

        # 转换为标准格式 [x1, y1, x2, y2, confidence, class_id]
        # 注意：这里的坐标是letterbox坐标系，需要在后续转换为原始图像坐标
        detections = []
        if len(boxes) > 0:
            for i, box in enumerate(boxes):
                if len(box) >= 5:
                    x1, y1, x2, y2, conf = box[:5]

                    # 确保坐标在有效范围内 (letterbox图像尺寸)
                    x1 = max(0, min(x1, wh[0] - 1))
                    y1 = max(0, min(y1, wh[1] - 1))
                    x2 = max(0, min(x2, wh[0] - 1))
                    y2 = max(0, min(y2, wh[1] - 1))

                    # 确保框的有效性
                    if x2 > x1 and y2 > y1:
                        detections.append([float(x1), float(y1), float(x2), float(y2), float(conf), 0])
                        print(f"[FACE_DETECT] 检测框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")

        print(f"[FACE_DETECT] 最终检测结果: {len(detections)} 个人脸")
        return detections

    except Exception as e:
        print(f"❌ 人脸检测失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def preprocess_for_rknn(img):
    """
    修复版RKNN预处理函数 - 简化预处理流程
    Args:
        img: letterbox处理后的图像 [640, 640, 3]
    Returns:
        processed_img: RKNN模型输入格式 [1, 3, 640, 640]
    """
    try:
        # 确保输入是BGR格式（OpenCV默认）
        if len(img.shape) != 3 or img.shape[2] != 3:
            raise ValueError(f"输入图像格式错误: {img.shape}")

        # 转换为RGB（RetinaFace通常使用RGB）
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 转换为float32并归一化到[0,1]
        img_float = img_rgb.astype(np.float32) / 255.0

        # 减去均值（RetinaFace常用的预处理）
        # 注意：这里使用标准化的均值，而不是原来的(104, 117, 123)
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img_normalized = (img_float - mean) / std

        # 转换为NCHW格式：[H, W, C] -> [C, H, W] -> [1, C, H, W]
        img_transposed = np.transpose(img_normalized, (2, 0, 1))  # HWC -> CHW
        img_batch = np.expand_dims(img_transposed, axis=0)        # 添加batch维度

        return img_batch

    except Exception as e:
        print(f"❌ RKNN预处理失败: {e}")
        # 降级到简单预处理
        img_simple = img.astype(np.float32)
        img_simple = np.transpose(img_simple, (2, 0, 1))
        img_simple = np.expand_dims(img_simple, axis=0)
        return img_simple


def _parse_retinaface_output(outputs, confidence, nms_threshold, ratio, padding, original_shape):
    """
    解析RetinaFace RKNN模型输出
    Args:
        outputs: RKNN模型输出
        confidence: 置信度阈值
        nms_threshold: NMS阈值
        ratio: letterbox缩放比例
        padding: letterbox填充
        original_shape: 原始图像尺寸
    Returns:
        检测结果列表
    """
    try:
        if not outputs or len(outputs) == 0:
            print("RKNN输出为空")
            return []

        # RetinaFace通常有3个输出：bbox, conf, landmarks
        # 这里简化处理，主要处理bbox和conf
        print(f"RKNN输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            print(f"输出{i}形状: {output.shape if hasattr(output, 'shape') else type(output)}")

        # 由于不知道具体的RetinaFace输出格式，这里先返回模拟结果
        # 实际使用时需要根据你的模型输出格式进行解析
        print("警告: 使用模拟解析，请根据实际模型输出格式调整")

        # 模拟一些检测结果，但使用更合理的坐标
        detections = []
        height, width = original_shape[:2]

        # 生成1-2个合理位置的人脸框
        import random
        num_faces = random.randint(0, 2)

        for i in range(num_faces):
            # 生成更合理的人脸位置（通常在图像中央区域）
            center_x = width // 2 + random.randint(-width//4, width//4)
            center_y = height // 2 + random.randint(-height//4, height//4)

            # 人脸框大小（通常占图像的5%-15%）
            face_size = random.randint(min(width, height)//20, min(width, height)//7)

            x1 = max(0, center_x - face_size//2)
            y1 = max(0, center_y - face_size//2)
            x2 = min(width, center_x + face_size//2)
            y2 = min(height, center_y + face_size//2)

            conf = random.uniform(confidence + 0.1, 0.95)

            detections.append([x1, y1, x2, y2, conf, 0])
            print(f"模拟人脸{i+1}: ({x1}, {y1}, {x2}, {y2}), 置信度: {conf:.3f}")

        return detections

    except Exception as e:
        print(f"解析RetinaFace输出失败: {e}")
        return []


def _simulate_face_detection_output(img, confidence):
    """模拟人脸检测输出"""
    try:
        height, width = img.shape[:2]
        
        # 模拟检测结果
        num_faces = random.randint(1, 2)
        detections = []
        
        for i in range(num_faces):
            x1 = random.randint(0, width // 2)
            y1 = random.randint(0, height // 2)
            x2 = random.randint(x1 + 50, min(x1 + 200, width))
            y2 = random.randint(y1 + 50, min(y1 + 200, height))
            conf = random.uniform(confidence, 0.95)
            
            detections.append([x1, y1, x2, y2, conf, 0])  # 0表示人脸类别
        
        return detections
        
    except Exception as e:
        print(f"模拟人脸检测失败: {e}")
        return []


def get_face_boxes(detections, gain, labels, image_shape=None):
    """
    修复版检测框处理函数 - 正确的坐标转换
    Args:
        detections: 原始检测结果 (letterbox坐标系)
        gain: 缩放参数 (scale, dw, dh) 或 [scale_x, scale_y]
        labels: 标签列表
        image_shape: 原始图像形状 (h, w) 用于边界检查
    Returns:
        处理后的检测框列表 (原始图像坐标系)
    """
    try:
        if not detections:
            print("[GET_FACE_BOXES] 没有检测结果")
            return []

        face_boxes = []

        # 修复：正确解析gain参数
        if isinstance(gain, (list, tuple)) and len(gain) >= 2:
            if len(gain) == 2:
                # 简单缩放格式 [scale_x, scale_y]
                scale_x, scale_y = gain
                dw, dh = 0, 0
            else:
                # letterbox格式 [scale, dw, dh] 或 [(scale_x, scale_y), (dw, dh)]
                if isinstance(gain[0], (list, tuple)):
                    # 格式: [(scale_x, scale_y), (dw, dh)]
                    scale_x, scale_y = gain[0]
                    dw, dh = gain[1]
                else:
                    # 格式: [scale, dw, dh]
                    scale_x = scale_y = gain[0]
                    dw, dh = gain[1], gain[2]
        else:
            print(f"[GET_FACE_BOXES] 警告：gain参数格式异常: {gain}")
            scale_x = scale_y = 1.0
            dw = dh = 0

        print(f"[GET_FACE_BOXES] 坐标转换参数: scale_x={scale_x:.3f}, scale_y={scale_y:.3f}, dw={dw:.1f}, dh={dh:.1f}")

        for i, det in enumerate(detections):
            if len(det) >= 5:
                x1, y1, x2, y2, conf = det[:5]

                print(f"[GET_FACE_BOXES] 原始框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")

                # 修复：正确的坐标转换公式
                # 从letterbox坐标转换回原始图像坐标
                x1_orig = (x1 - dw) / scale_x
                y1_orig = (y1 - dh) / scale_y
                x2_orig = (x2 - dw) / scale_x
                y2_orig = (y2 - dh) / scale_y

                # 边界检查 - 确保坐标在有效范围内
                if image_shape is not None:
                    h, w = image_shape[:2]
                    x1_orig = max(0, min(x1_orig, w - 1))
                    y1_orig = max(0, min(y1_orig, h - 1))
                    x2_orig = max(0, min(x2_orig, w - 1))
                    y2_orig = max(0, min(y2_orig, h - 1))

                    # 确保框的有效性
                    if x2_orig <= x1_orig or y2_orig <= y1_orig:
                        print(f"[GET_FACE_BOXES] 跳过无效框{i+1}: [{x1_orig:.1f},{y1_orig:.1f},{x2_orig:.1f},{y2_orig:.1f}]")
                        continue

                # 确保坐标为整数
                x1_final = int(round(x1_orig))
                y1_final = int(round(y1_orig))
                x2_final = int(round(x2_orig))
                y2_final = int(round(y2_orig))

                # 添加类别标签
                class_name = "face" if labels and len(labels) > 0 else "face"

                face_boxes.append([x1_final, y1_final, x2_final, y2_final, float(conf), class_name])

                print(f"[GET_FACE_BOXES] 转换后框{i+1}: [{x1_final},{y1_final},{x2_final},{y2_final}], 置信度: {conf:.3f}")

        print(f"[GET_FACE_BOXES] 处理完成: {len(face_boxes)}/{len(detections)} 个有效检测框")
        return face_boxes

    except Exception as e:
        print(f"❌ 处理检测框失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def nms(boxes, scores, score_threshold, nms_threshold):
    """
    非极大值抑制
    Args:
        boxes: 检测框 [[x1, y1, x2, y2], ...]
        scores: 置信度分数
        score_threshold: 分数阈值
        nms_threshold: NMS阈值
    Returns:
        保留的索引
    """
    try:
        if len(boxes) == 0:
            return []
        
        # 转换为numpy数组
        boxes = np.array(boxes)
        scores = np.array(scores)
        
        # 过滤低分数的框
        valid_indices = scores > score_threshold
        boxes = boxes[valid_indices]
        scores = scores[valid_indices]
        
        if len(boxes) == 0:
            return []
        
        # 计算面积
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        
        # 按分数排序
        order = scores.argsort()[::-1]
        
        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)
            
            if order.size == 1:
                break
            
            # 计算IoU
            xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
            yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
            xx2 = np.minimum(boxes[i, 2], boxes[order[1:], 2])
            yy2 = np.minimum(boxes[i, 3], boxes[order[1:], 3])
            
            w = np.maximum(0, xx2 - xx1)
            h = np.maximum(0, yy2 - yy1)
            inter = w * h
            
            iou = inter / (areas[i] + areas[order[1:]] - inter)
            
            # 保留IoU小于阈值的框
            inds = np.where(iou <= nms_threshold)[0]
            order = order[inds + 1]
        
        return keep
        
    except Exception as e:
        print(f"NMS处理失败: {e}")
        return list(range(len(boxes)))


def procss_img(img_path):
    """
    完全按照参考实现的图像预处理函数 - 修复输入格式
    Args:
        img_path: 图像路径或图像数组
    Returns:
        img: 预处理后的图像（用于RKNN推理）[1, 3, 640, 640]
        or_img: 原始图像（用于坐标映射）
    """
    if isinstance(img_path, str):
        img = cv2.imread(img_path)
    else:
        img = img_path

    # 完全按照参考实现的步骤
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = letterbox_image(img, (IMG_SIZE, IMG_SIZE))
    or_img = np.array(img, np.uint8)
    or_img = cv2.cvtColor(or_img, cv2.COLOR_RGB2BGR)
    img = img.astype(dtype=np.float32)
    img -= np.array((104, 117, 123), np.float32)

    # 转换为NCHW格式：[H, W, C] -> [C, H, W] -> [1, C, H, W]
    img = np.transpose(img, (2, 0, 1))  # HWC -> CHW
    img = np.expand_dims(img, axis=0)   # 添加batch维度 -> NCHW

    return img, or_img


def letterbox_image(image, size):
    """完全按照参考实现的letterbox函数"""
    ih, iw, _ = np.shape(image)
    w, h = size
    scale = min(w/iw, h/ih)
    nw = int(iw * scale)
    nh = int(ih * scale)

    image = cv2.resize(image, (nw, nh))
    new_image = np.ones([size[1], size[0], 3]) * 128
    new_image[(h-nh)//2:nh+(h-nh)//2, (w-nw)//2:nw+(w-nw)//2] = image
    return new_image


# 全局常量（按照参考实现）
IMG_SIZE = 640

# RetinaFace配置（完全按照参考实现）
cfg_mnet = {
    'min_sizes': [[16, 32], [64, 128], [256, 512]],
    'steps': [8, 16, 32],
    'variance': [0.1, 0.2],
}

from itertools import product
from math import ceil

class Anchors:
    """完全按照参考实现的Anchor生成器"""
    def __init__(self, cfg, image_size=None):
        self.min_sizes = cfg['min_sizes']
        self.steps = cfg['steps']
        self.image_size = image_size
        self.feature_maps = [[ceil(self.image_size[0] / step), ceil(self.image_size[1] / step)] for step in self.steps]

    def get_anchors(self):
        anchors = []
        for k, f in enumerate(self.feature_maps):
            min_sizes = self.min_sizes[k]
            for i, j in product(range(f[0]), range(f[1])):
                for min_size in min_sizes:
                    s_kx = min_size / self.image_size[1]
                    s_ky = min_size / self.image_size[0]
                    dense_cx = [x * self.steps[k] / self.image_size[1] for x in [j + 0.5]]
                    dense_cy = [y * self.steps[k] / self.image_size[0] for y in [i + 0.5]]
                    for cy, cx in product(dense_cy, dense_cx):
                        anchors += [cx, cy, s_kx, s_ky]
        return np.array(anchors).reshape(-1, 4)


def decode(loc, priors, variances):
    """完全按照参考实现的decode函数"""
    boxes = np.concatenate((priors[:, :2] + loc[:, :2] * variances[0] * priors[:, 2:],
                           priors[:, 2:] * np.exp(loc[:, 2:] * variances[1])), 1)
    boxes[:, :2] -= boxes[:, 2:] / 2
    boxes[:, 2:] += boxes[:, :2]
    return boxes


def decode_landm(pre, priors, variances):
    """完全按照参考实现的关键点解码函数"""
    landms = np.concatenate((priors[:, :2] + pre[:, :2] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 2:4] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 4:6] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 6:8] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 8:10] * variances[0] * priors[:, 2:],
                            ), 1)
    return landms


def pynms(dets, thresh):
    """完全按照参考实现的NMS函数"""
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    areas = (y2 - y1) * (x2 - x1)
    scores = dets[:, 4]
    keep = []
    index = scores.argsort()[::-1]

    while index.size > 0:
        i = index[0]
        keep.append(i)
        x11 = np.maximum(x1[i], x1[index[1:]])
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])
        w = np.maximum(0, x22 - x11)
        h = np.maximum(0, y22 - y11)
        overlaps = w * h
        ious = overlaps / (areas[i] + areas[index[1:]] - overlaps)
        idx = np.where(ious <= thresh)[0]
        index = index[idx + 1]
    return keep


def filter_box(org_box, conf_thres, iou_thres):
    """完全按照chitu-master实现的过滤函数"""
    # 添加调试信息
    if len(org_box) > 0:
        max_conf = np.max(org_box[..., 4])
        min_conf = np.min(org_box[..., 4])
        mean_conf = np.mean(org_box[..., 4])
        # print(f"置信度统计: 最大={max_conf:.4f}, 最小={min_conf:.4f}, 平均={mean_conf:.4f}, 阈值={conf_thres}")

        # 统计超过阈值的数量
        high_conf_count = np.sum(org_box[..., 4] > conf_thres)
        # print(f"超过阈值 {conf_thres} 的检测框数量: {high_conf_count}/{len(org_box)}")

    # 完全按照chitu-master的实现
    conf = org_box[..., 4] > conf_thres
    box = org_box[conf == True]
    output = []
    curr_cls_box = np.array(box)
    curr_cls_box[:, :4] = curr_cls_box[:, :4] * IMG_SIZE
    curr_cls_box[:, 5:] = curr_cls_box[:, 5:] * IMG_SIZE
    curr_out_box = pynms(curr_cls_box, iou_thres)
    for k in curr_out_box:
        output.append(curr_cls_box[k])
    return np.array(output)


def _decode_outputs(outputs, conf_thres=0.6, iou_thres=0.3):
    """完全按照chitu-master实现的解码函数"""
    try:
        # 完全按照chitu-master的处理方式
        output_1 = np.array(outputs[0]).squeeze()  # bbox回归
        output_2 = np.array(outputs[1]).squeeze()  # 置信度
        output_3 = np.array(outputs[2]).squeeze()  # 关键点

        # print(f"解码输入形状:")
        # print(f"  bbox回归: {output_1.shape}")
        # print(f"  置信度: {output_2.shape}")
        # print(f"  关键点: {output_3.shape}")

        # 生成anchors（完全按照chitu-master）
        anchors = Anchors(cfg_mnet, image_size=(IMG_SIZE, IMG_SIZE)).get_anchors()
        # print(f"  anchors: {anchors.shape}")

        # 解码（完全按照chitu-master）
        boxes = decode(output_1, anchors, cfg_mnet['variance'])
        landms = decode_landm(output_3, anchors, cfg_mnet['variance'])
        conf = output_2[:, 1:2]  # 取第二列作为置信度

        # print(f"解码后形状:")
        # print(f"  boxes: {boxes.shape}")
        # print(f"  landms: {landms.shape}")
        # print(f"  conf: {conf.shape}")

        # 合并（完全按照chitu-master）
        boxs_conf = np.concatenate((boxes, conf, landms), -1)
        # print(f"  合并后: {boxs_conf.shape}")

        # 过滤（完全按照chitu-master）
        boxs_conf = filter_box(boxs_conf, conf_thres, iou_thres)
        # print(f"  过滤后: {boxs_conf.shape if len(boxs_conf) > 0 else 'empty'}")

        return boxs_conf

    except Exception as e:
        print(f"❌ 解码失败: {e}")
        import traceback
        traceback.print_exc()
        return np.array([])
