import cv2
import random
import numpy as np
from rknnlite.api import RKNNLite
from hide_warnings import hide_warnings

@hide_warnings
def inference_del_wall(rknn_model, img):
    outputs = rknn_model.inference(inputs=[img], data_format=['nhwc'])
    return outputs

def auto_resize(img, new_w, new_h):
    h, w = img.shape[:2]
    scale = min(new_w / w, new_h / h)
    new_size = tuple(map(int, np.array(img.shape[:2][::-1]) * scale))
    return cv2.resize(img, new_size), scale


def letterbox(img, new_wh=(416, 416), color=(128, 128, 128)):
    new_img, scale = auto_resize(img, *new_wh)
    nh, nw, _ = new_img.shape
    iw, ih = new_wh
    dw, dh = (iw - nw) / 2, (ih - nh) / 2

    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    image_paded = cv2.copyMakeBorder(new_img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
    return image_paded, [scale, dw, dh]

def load_model(model_path, number):
    # Create RKNN object
    rknn_lite = RKNNLite()
    rknn_lite.load_rknn(model_path)
    # rknn_lite.init_runtime(core_mask = RKNNLite.NPU_CORE_AUTO)
    if number == 0:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
    elif number == 1:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_1)
    elif number == 2:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_2)
    
    elif number == 3:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
    elif number == 4:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_1)
    elif number == 5:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_2)
    
    elif number == -1:
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_AUTO)
    if ret != 0:
        print("Init runtime environment failed")
        exit(ret)
    return rknn_lite



def predict(confidence, nms_threshold, img, wh, rknn_model):
    masks = [[0, 1, 2], [3, 4, 5], [6, 7, 8]]
    anchors = [[10, 13], [16, 30], [33, 23], [30, 61], [62, 45], [59, 119], [116, 90], [156, 198], [373, 326]]

    img = np.expand_dims(img, 0)
    outputs = inference_del_wall(rknn_model, img)
    # outputs = rknn_model.inference(inputs=[img])
    # print(number, '----------------------')
    # post process
    input0_data = outputs[0]
    input1_data = outputs[1]
    input2_data = outputs[2]

    input0_data = input0_data.reshape([3, -1] + list(input0_data.shape[-2:]))
    input1_data = input1_data.reshape([3, -1] + list(input1_data.shape[-2:]))
    input2_data = input2_data.reshape([3, -1] + list(input2_data.shape[-2:]))

    input_data = list()
    input_data.append(np.transpose(input0_data, (2, 3, 0, 1)))
    input_data.append(np.transpose(input1_data, (2, 3, 0, 1)))
    input_data.append(np.transpose(input2_data, (2, 3, 0, 1)))

    boxes, classes, scores = [], [], []
    for input, mask in zip(input_data, masks):
        b, c, s = process(input, mask, anchors, wh[1])
        b, c, s = filter_boxes(b, c, s, confidence)
        boxes.append(b)
        classes.append(c)
        scores.append(s)

    boxes = np.concatenate(boxes)
    boxes = xywh2xyxy(boxes)
    classes = np.concatenate(classes)
    scores = np.concatenate(scores)

    nboxes, nclasses, nscores = [], [], []
    for c in set(classes):
        inds = np.where(classes == c)
        b = boxes[inds]
        c = classes[inds]
        s = scores[inds]
        keep = nms_boxes(b, s, nms_threshold)

        nboxes.append(b[keep])
        nclasses.append(c[keep])
        nscores.append(s[keep])
    if len(nboxes) >= 1:
        boxes = np.concatenate(nboxes)
        classes = np.concatenate(nclasses).reshape(-1, 1)
        scores = np.concatenate(nscores).reshape(-1, 1)
        box_src = np.concatenate([boxes, scores, classes], axis=1)
    else:
        box_src = []
    return box_src


def xywh2xyxy(x):
    # Convert [x, y, w, h] to [x1, y1, x2, y2]
    y = np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
    y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
    y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
    y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
    return y

def process(input, mask, anchors, img_h):

    anchors = [anchors[i] for i in mask]
    grid_h, grid_w = map(int, input.shape[0:2])

    box_confidence = sigmoid(input[..., 4])
    box_confidence = np.expand_dims(box_confidence, axis=-1)

    box_class_probs = sigmoid(input[..., 5:])

    box_xy = sigmoid(input[..., :2])*2 - 0.5

    col = np.tile(np.arange(0, grid_w), grid_w).reshape(-1, grid_w)
    row = np.tile(np.arange(0, grid_h).reshape(-1, 1), grid_h)
    col = col.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
    row = row.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
    grid = np.concatenate((col, row), axis=-1)
    box_xy += grid
    box_xy *= int(img_h/grid_h)

    box_wh = pow(sigmoid(input[..., 2:4])*2, 2)
    box_wh = box_wh * anchors

    box = np.concatenate((box_xy, box_wh), axis=-1)

    return box, box_confidence, box_class_probs

def get_max_scale(img, max_w, max_h):
    h, w = img.shape[:2]
    scale = min(max_w / w, max_h / h, 1)
    return scale


def get_new_size(img, scale):
    return tuple(map(int, np.array(img.shape[:2][::-1]) * scale))


def sigmoid(x):
    return 1 / (1 + np.exp(-x))


def filter_boxes(boxes, box_confidences, box_class_probs, conf_thres):
    box_scores = box_confidences * box_class_probs  
    box_classes = np.argmax(box_scores, axis=-1)
    box_class_scores = np.max(box_scores, axis=-1)

    pos = np.where(box_class_scores >= conf_thres)

    boxes = boxes[pos]
    classes = box_classes[pos]
    scores = box_class_scores[pos]
    return boxes, classes, scores
    # box_classes = np.argmax(box_class_probs, axis=-1)
    # box_class_scores = np.max(box_class_probs, axis=-1)
    # pos = np.where(box_confidences[..., 0] >= conf_thres)

    # boxes = boxes[pos]
    # classes = box_classes[pos]
    # scores = box_class_scores[pos]
    # return boxes, classes, scores


def nms_boxes(boxes, scores, iou_thres):
    """Suppress non-maximal boxes.

    # Arguments
        boxes: ndarray, boxes of objects.
        scores: ndarray, scores of objects.

    # Returns
        keep: ndarray, index of effective boxes.
    """
    x = boxes[:, 0]
    y = boxes[:, 1]
    w = boxes[:, 2] - boxes[:, 0]
    h = boxes[:, 3] - boxes[:, 1]

    areas = w * h
    order = scores.argsort()[::-1]

    keep = []
    while order.size > 0:
        i = order[0]
        keep.append(i)

        xx1 = np.maximum(x[i], x[order[1:]])
        yy1 = np.maximum(y[i], y[order[1:]])
        xx2 = np.minimum(x[i] + w[i], x[order[1:]] + w[order[1:]])
        yy2 = np.minimum(y[i] + h[i], y[order[1:]] + h[order[1:]])

        w1 = np.maximum(0.0, xx2 - xx1 + 0.00001)
        h1 = np.maximum(0.0, yy2 - yy1 + 0.00001)
        inter = w1 * h1

        ovr = inter / (areas[i] + areas[order[1:]] - inter)
        inds = np.where(ovr <= iou_thres)[0]
        order = order[inds + 1]
    keep = np.array(keep)
    return keep


def plot_one_box(x, img, color=None, label=None, line_thickness=1, score=""):
    tl = line_thickness or round(0.002 * (img.shape[0] + img.shape[1]) / 2) + 1  # line/font thickness
    color = color or [random.randint(0, 255) for _ in range(3)]
    c1, c2 = (int(x[0]), int(x[1])), (int(x[2]), int(x[3]))
    # if label == 'person':
    cv2.rectangle(img, c1, c2, color, thickness=2, lineType=cv2.LINE_AA)
    tf = max(tl - 1, 1)  # font thickness
    # print(label)
    cv2.putText(img, label + str(score), (c1[0], c2[1]), 1, 1.3, [225, 255, 255],
                thickness=tf,
                lineType=cv2.LINE_AA)


def get_draw_box(box_src, gain, label):
    draw_boxes = []
    for *box, score, cl in box_src:
        top, left, right, bottom = box
        top = max(int((top - gain[1]) / gain[0]), 0)
        left = max(int((left - gain[2]) / gain[0]), 0)
        right = max(int((right - gain[1]) / gain[0]), 0)
        bottom = max(int((bottom - gain[2]) / gain[0]), 0)
        draw_boxes.append([top, left, right, bottom, float(score), label[int(cl)]])
    return draw_boxes

