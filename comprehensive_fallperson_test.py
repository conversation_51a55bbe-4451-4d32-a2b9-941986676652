#!/usr/bin/env python3
"""
全面测试detect_fallperson算法的各个功能
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
import time
import importlib

def create_test_image():
    """创建测试图像"""
    img = np.ones((480, 640, 3), dtype=np.uint8) * 128
    # 添加一些简单的图形作为测试内容
    cv2.rectangle(img, (100, 100), (200, 300), (255, 255, 255), -1)  # 模拟人员
    cv2.rectangle(img, (300, 200), (400, 250), (200, 200, 200), -1)  # 模拟跌倒人员
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    return base64.b64encode(buffer).decode('utf-8')

def test_algorithm_initialization():
    """测试算法初始化"""
    print("🔍 测试算法初始化")
    print("=" * 50)
    
    try:
        # 添加模块路径
        sys.path.insert(0, 'detect_fallperson/v2_0_0')
        
        from utils.detect_fallperson import DetectFallperson
        
        # 创建算法实例
        detector = DetectFallperson("detect_fallperson", "v2.0.0", "fallperson")
        
        print("✅ 算法初始化成功")
        print(f"  - 标签: {detector.label}")
        print(f"  - 权重: {detector.weight}")
        print(f"  - 输入尺寸: {detector.size}")
        print(f"  - 进程数量: {len(detector.run_model)}")
        
        return detector, True
        
    except Exception as e:
        print(f"❌ 算法初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def test_parameter_validation(detector):
    """测试参数验证"""
    print("\n🔍 测试参数验证")
    print("=" * 50)
    
    if not detector:
        print("❌ 检测器未初始化，跳过测试")
        return False
    
    try:
        # 测试正确参数
        valid_data = {
            "transactionNumber": "test_001",
            "businessData": {
                "image": image_to_base64(create_test_image()),
                "imageType": "base64",
                "advsetValue": {
                    "fallPerson": "all",
                    "recvDataType": ["infer_results", "draw_image"]
                }
            }
        }
        
        # 测试参数验证方法
        is_valid = detector._DetectFallperson__check_param(valid_data)
        print(f"✅ 有效参数验证: {is_valid}")
        
        # 测试无效参数
        invalid_data = {
            "businessData": {
                "image": "",  # 空图像
                "imageType": "base64"
            }
        }
        
        is_invalid = detector._DetectFallperson__check_param(invalid_data)
        print(f"✅ 无效参数验证: {not is_invalid}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inference_modes(detector):
    """测试不同推理模式"""
    print("\n🔍 测试不同推理模式")
    print("=" * 50)
    
    if not detector:
        print("❌ 检测器未初始化，跳过测试")
        return False
    
    modes = ["all", "person", "fallperson"]
    results = {}
    
    for mode in modes:
        try:
            print(f"\n  测试模式: {mode}")
            
            test_data = {
                "transactionNumber": f"test_{mode}_{int(time.time())}",
                "businessData": {
                    "image": image_to_base64(create_test_image()),
                    "imageType": "base64",
                    "advsetValue": {
                        "fallPerson": mode,
                        "recvDataType": ["infer_results", "draw_image", "infer_boxes"],
                        "interval": 5
                    }
                }
            }
            
            # 调用推理接口
            result = detector.infer(test_data, "models.model_manager")
            print(f"    推理调用结果: {result}")
            
            # 等待一段时间让推理完成
            time.sleep(2)
            
            results[mode] = result
            
        except Exception as e:
            print(f"    ❌ 模式 {mode} 测试失败: {e}")
            results[mode] = False
    
    success_count = sum(1 for r in results.values() if r)
    print(f"\n✅ 推理模式测试完成: {success_count}/{len(modes)} 成功")
    
    return success_count == len(modes)

def test_postprocess_logic():
    """测试后处理逻辑"""
    print("\n🔍 测试后处理逻辑")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_fallperson/v2_0_0')
        
        from postprocess.post_process import PostProcess
        from utils.i18n import I18n
        
        # 创建后处理实例
        i18n = I18n()
        post_mess = ["detect_fallperson", "v2.0.0", "fallperson"]
        postprocessor = PostProcess(i18n, post_mess)
        
        # 创建测试数据
        test_img = create_test_image()
        
        # 模拟检测结果
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],
            [300, 150, 400, 350, 0.92, "fallperson"],
        ]
        
        modes_to_test = ["all", "person", "fallperson"]
        
        for mode in modes_to_test:
            print(f"\n  测试后处理模式: {mode}")
            
            test_data = {
                "businessData": {
                    "imageType": "base64",
                    "advsetValue": {
                        "fallPerson": mode,
                        "recvDataType": ["infer_results", "draw_image", "infer_boxes"]
                    }
                }
            }
            
            # 运行后处理
            result_json, post_bool = postprocessor.run_process(test_data, test_boxes, test_img)
            
            print(f"    是否触发报警: {post_bool}")
            print(f"    消息类型: {[v.get('type', 'unknown') for v in result_json.get('values', [])]}")
            
            # 检查boxes输出
            boxes = result_json.get('boxes', {})
            person_count = len(boxes.get('person', []))
            fallperson_count = len(boxes.get('fallperson', []))
            print(f"    person检测框: {person_count}, fallperson检测框: {fallperson_count}")
        
        print("✅ 后处理逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 后处理逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_drawing():
    """测试颜色绘制功能"""
    print("\n🔍 测试颜色绘制功能")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_fallperson/v2_0_0')
        
        from postprocess.utils.utils import draw_image_boxes
        
        # 创建测试图像
        test_img = create_test_image()
        
        # 测试检测框
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],      # 应该是绿色
            [300, 150, 400, 350, 0.92, "fallperson"],  # 应该是红色
            [500, 200, 600, 400, 0.78, "unknown"],     # 应该是默认黄色
        ]
        
        # 绘制检测框
        result_img = draw_image_boxes(test_img.copy(), test_boxes)
        
        print("✅ 颜色绘制测试完成")
        print("  - person类别: 绿色框")
        print("  - fallperson类别: 红色框")
        print("  - 未知类别: 默认黄色框")
        
        # 可选：保存测试图像
        # cv2.imwrite("test_fallperson_colors.jpg", result_img)
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色绘制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_management(detector):
    """测试任务管理功能"""
    print("\n🔍 测试任务管理功能")
    print("=" * 50)
    
    if not detector:
        print("❌ 检测器未初始化，跳过测试")
        return False
    
    try:
        # 测试任务启动
        task_data = {
            "callBackData": {
                "requestMethod": "POST",
                "url": "http://localhost:8080/callback"
            },
            "businessData": {
                "advsetValue": {
                    "recvDataType": ["infer_results"]
                }
            }
        }
        
        task_id = "test_task_001"
        
        # 启动任务
        start_result = detector.start_task(task_id, task_data)
        print(f"✅ 任务启动结果: {start_result}")
        
        # 检查任务状态
        time.sleep(1)
        task_status = detector.get_task_list()
        print(f"✅ 任务状态: {task_status}")
        
        # 停止任务
        stop_result = detector.stop_task(task_id)
        print(f"✅ 任务停止结果: {stop_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 detect_fallperson算法全面测试")
    print("=" * 70)
    
    # 初始化检测器
    detector, init_success = test_algorithm_initialization()
    
    tests = [
        ("参数验证", lambda: test_parameter_validation(detector)),
        ("推理模式", lambda: test_inference_modes(detector)),
        ("后处理逻辑", test_postprocess_logic),
        ("颜色绘制", test_color_drawing),
        ("任务管理", lambda: test_task_management(detector)),
    ]
    
    results = {"算法初始化": init_success}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！detect_fallperson算法功能正常")
        print("\n📋 功能确认:")
        print("  ✅ 算法初始化正常")
        print("  ✅ 参数验证正常")
        print("  ✅ 三种模式推理正常")
        print("  ✅ 后处理逻辑正确")
        print("  ✅ 颜色区分正常")
        print("  ✅ 任务管理正常")
    else:
        print("⚠️ 部分功能存在问题，需要进一步检查")
        
        # 提供具体的问题分析
        failed_tests = [name for name, passed in results.items() if not passed]
        if failed_tests:
            print(f"\n❌ 失败的测试: {', '.join(failed_tests)}")

if __name__ == "__main__":
    main()
