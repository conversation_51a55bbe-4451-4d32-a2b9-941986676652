from log import mylog
import schedule
from threading import Thread
from applications.digital_human.common.websocket.regist import websocket_regist
from applications.digital_human.common.websocket.msg_handle import message_handle

def init_digital_human(app):
    try:
        # 每天凌晨检查有效期是否失效的定时任务
        from applications.digital_human.common.utils.qa_service import stop_qa_service, do_check
        schedule.every().day.at("00:00").do(stop_qa_service)
        
        # 
        from applications.digital_human.view.sentryModeControl import init_mode
        init_mode(app)
        
        # 定时任务线程
        check_p = Thread(target=do_check)
        check_p.start()
        
    except Exception as e:
        mylog.info("数字人服务启动失败：" + e)