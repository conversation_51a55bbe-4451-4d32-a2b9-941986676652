from threading import Event, Thread
import time
import datetime
import queue
import cv2

from applications.models import Camera
from applications.extensions import db
from applications.algorithm.utils.algo_utils import *
from log import mylog

class FrameTM:
    def __init__(self, frame, tm):
        self.frame = frame
        self.tm = tm
    
class VideoCap:
    def __init__(self, url, camera, app, rate = drawframeRate):
        self.url = url
        self.camera = str(camera)
        
        self.cap = None
        self.flag = False
        self.isOpened = False
        self.w = 0
        self.h = 0
        
        self.event = Event()
        self.chouzhen = False
        self.rate = rate
        
        self.app = app
        
        self.q = queue.Queue(maxsize=3)


    def capture(self):
        if not self.flag and self.cap is None:
            self.flag = True
            self.cap = cv2.VideoCapture(self.url)
            if not self.cap.isOpened():
                if self.url[:4] != "rtsp":
                    for i in range(videocapTimes):
                        self.cap = cv2.VideoCapture(self.url)
                        if self.cap.isOpened():
                            self.w = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                            self.h = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                            self.isOpened = True
                            self.flag = False
                            break
                        else:
                            time.sleep(videocapGap)
                        
                    if not self.cap.isOpened():
                        self.q.queue.clear()
                        self.cap = None
                        self.isOpened = False
                        self.w = 0
                        self.h = 0
                        self.flag = False
                        # 修改数据库状态
                        with self.app.app_context():
                            Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "0"})
                            db.session.commit()
                        mylog.info("Failed to pull streams, camera id is: " + self.camera)
                        # sendExceptMsg("2", [self.camera])    
                        
                else:
                    mylog.info("Failed to pull rtsp streams, camera id is: " + self.camera)
                    with self.app.app_context():
                        Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "0"})
                        db.session.commit()
                    # 调接口
                    # sendExceptMsg("2", [self.camera])
                    self.isOpened = False
                    self.w = 0
                    self.h = 0
                    while not self.cap.isOpened():
                        mylog.info("camera id " + self.camera + " trying again")
                        self.cap = cv2.VideoCapture(self.url)
                        time.sleep(videocapGap)

                    self.w = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                    self.h = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                    self.isOpened = True
                    self.flag = False
            else:
                self.w = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                self.h = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                self.isOpened = True
                self.flag = False
                with self.app.app_context():
                    Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "1"})
                    db.session.commit()

    def startDrawframe(self):
        if not self.chouzhen:
            chouzhen_t = Thread(target=self.drawframe)
            self.event.clear()
            chouzhen_t.start()
            self.chouzhen = True
        
    def stopDrawframe(self):
        if self.chouzhen:
            self.event.set()
            self.q.queue.clear()
            self.chouzhen = False
    
    def drawframe(self):
        idx = 0
        while True:
            
            if self.event.is_set():
                mylog.info(self.camera + " stop drawframe.")
                break
            
            if self.isOpened:
                idx += 1
                ret = self.cap.grab()

                if not ret:
                    self.q.queue.clear()
                    mylog.info(self.camera + " is abnormal.")
                    self.cap.release()
                    self.cap = None
                    self.capture()
                    if self.cap is not None and self.cap.isOpened():
                        mylog.info(self.camera + " reconnect success.")
                        with self.app.app_context():
                            Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "1"})
                            db.session.commit()
                    else:
                        mylog.info(self.camera + " reconnect failed.")
                        with self.app.app_context():
                            Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "0"})
                            db.session.commit()
                        self.stopDrawframe()
                    
                elif idx >= self.rate:
                    # tm = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
                    tm = datetime.datetime.now()
                    _, frame = self.cap.retrieve()
                    if frame is not None:
                        if self.q.full():
                            self.q.get()
                        self.q.put(FrameTM(frame, tm), block=False)
                        idx = 0
    
            
    # 关闭
    def close(self):
        self.isOpened = False
        self.flag = False
        self.q.queue.clear()
        time.sleep(3)
        if self.cap is not None:
            self.cap.release()
            self.cap = None