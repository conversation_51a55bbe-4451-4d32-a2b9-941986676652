import cv2
import os
from multiprocessing import Process, Queue
from threading import Thread
from threading import Lock
import time
import json
import sys


class UsbCamera(object):
    def __init__(self, lock, camera_device, spaces):
        self.image = []
        self.lock = lock
        self.size_wh = [1920, 1080]  # 默认分辨率
        thread_ma = Thread(target=self.thread_manage, args=([camera_device, spaces]), daemon=True)
        thread_ma.start()

    def thread_manage(self, camera_device, spaces):
        while True:
            cap = cv2.VideoCapture()
            cap.open(int(camera_device), apiPreference=cv2.CAP_V4L2)#设置摄像头为V4L2
            #这个根据自己的相机参数配置为MJPG或YUY2或YUV
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))#输出图像为MJPG格式
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.size_wh[0])#修改图像宽度为1920
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.size_wh[1])#修改图像高度为1080
            cap_fps = cap.get(cv2.CAP_PROP_FPS)
            fps_save = cap_fps * 3 # 连续多少帧没有数据
            number_fps = fps_save * 2  #累计帧，防止pass累计
            if cap.isOpened():
                num_pass = 0
                num_loss = 0
                number = 0
                while True:
                    succ = cap.grab()
                    number += 1
                    judge_stop = False
                    judge_img = False
                    if succ:
                        num_pass += 1
                    else:
                        num_loss += 1
                    if num_loss >= fps_save:
                        judge_stop = True
                        num_loss = 0
                        number = 0
                    if num_pass >= spaces:
                        num_pass = 0
                        judge_img = True
                    if number >= number_fps:
                        num_loss = 0
                        number = 0
                    if judge_stop:
                        cap.release()
                        cv2.destroyAllWindows()
                        break
                    if judge_img:
                        success, im = cap.retrieve()
                        if not success:
                            continue
                        self.lock.acquire()
                        self.image = im
                        self.lock.release()
            else:
                time.sleep(10)


    def __iter__(self):
        return self

    def __next__(self):
        return self.image
