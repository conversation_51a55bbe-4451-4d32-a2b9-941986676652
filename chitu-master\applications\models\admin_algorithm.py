from applications.extensions import db
from datetime import datetime
    
class Weights(db.Model):
    __tablename__ = 'edge_box_weights_data'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment="权重ID")
    type = db.Column(db.String(8), comment="模型类型")
    path = db.Column(db.String(64), nullable=False, comment="权重所在的文件夹路径")
    name = db.Column(db.Text, comment="权重名称")
    infer_file = db.Column(db.String(64), nullable=False, comment="推理文件名称")
    status = db.Column(db.<PERSON>, nullable=False, default=False, comment="权重加载状态")
    algo = db.relationship("Algorithm", backref="weights")

  
class AlgorithmSetting(db.Model):
    __tablename__ = 'edge_box_algorithm_setting'
    id = db.Column(db.Integer, primary_key=True, comment="算法设置id")
    algo_num = db.Column(db.Integer, nullable=False, default=5, comment="可同时运行算法数量")
    camera_num = db.Column(db.Integer, nullable=False, default=6, comment="智能盒可同时接入视频路数")

class Algorithm(db.Model):
    __tablename__ = 'edge_box_algorithm_data'
    id = db.Column(db.String(8), primary_key=True, comment="算法ID")
    name = db.Column(db.String(32), unique=True, comment="算法名称")
    description = db.Column(db.String(256), comment="算法描述")
    status = db.Column(db.Boolean, nullable=False, default=False, comment="算法状态")
    weight = db.Column(db.Integer, db.ForeignKey("edge_box_weights_data.id"))
    camera_alarm_type = db.relationship('CameraAlarmType', back_populates='algorithm', cascade="all, delete-orphan")
    
class Camera(db.Model):
    __tablename__ = 'admin_camera'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True,nullable=False,comment='摄像头编号')
    camera_label = db.Column(db.String(32), nullable=False,comment='摄像头标识')
    stream_url = db.Column(db.String(255), nullable=False,comment='视频流地址')
    remark = db.Column(db.Text(), nullable=False,comment='描述')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False,comment='创建时间')
    update_time = db.Column(db.DateTime, default=datetime.now, nullable=False,comment='更新时间')
    camera_status = db.Column(db.String(1), nullable=False,comment='摄像头状态 关闭 1：关闭')
    camera_alarm_type = db.relationship('CameraAlarmType', back_populates='camera', cascade="all, delete-orphan")
    
class CameraAlarmType(db.Model):
    __tablename__ = 'admin_camera_alarm_type'
    camera_id = db.Column(db.Integer,db.ForeignKey('admin_camera.id'), primary_key=True, nullable=False,comment='摄像头主键id')
    alarm_type = db.Column(db.String(8),db.ForeignKey('edge_box_algorithm_data.id'), primary_key=True,nullable=False,comment='算法类型')
    status = db.Column(db.String(1), nullable=False,comment='状态 0：停用 1：启用')
    enable_screen_mirroring = db.Column(db.String(1), nullable=False,comment='开启投屏 0：未开启 1：开启')
    
    camera = db.relationship('Camera', back_populates='camera_alarm_type')
    algorithm = db.relationship('Algorithm', back_populates='camera_alarm_type')