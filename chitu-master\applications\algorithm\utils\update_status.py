import os
from flask import current_app
from applications.extensions import db
from applications.models.admin_algorithm import Algorithm, AlgorithmSetting, Weights, Camera, CameraAlarmType
from applications.algorithm.utils.load_model import *
from applications.algorithm.utils.detect import change_camera_alarm_status
# from applications.algorithm.utils.algo_utils import mylog
from log import mylog

'''
关闭算法前检查是否有算法开启
'''
def check_close_algo(algo):
    open_camera = db.session.query(CameraAlarmType).filter(db.and_(CameraAlarmType.alarm_type == algo.id, CameraAlarmType.status == "1")).all()
    if len(open_camera):
        return {"msg": "warn", "data": "{}个摄像头正在使用算法，是否要停用算法？".format(len(open_camera))}
    else:
        app = current_app._get_current_object()
        res = close_algo(algo, app, True)
        return res


def restart_algo(camera, app, algo_id):
    data = CameraAlarmType.query.filter(CameraAlarmType.camera_id==camera.id).all()
    temp = {}
    for item in data:
        if camera.stream_url not in temp:
            temp[camera.stream_url] = []
        
        if item.alarm_type == algo_id:
            result = {
                "camera_id": camera.id,
                "algo_id": item.alarm_type,
                "status": "0"
            }
        else:
            result = {
                "camera_id":camera.id,
                "algo_id":item.alarm_type,
                "status":item.status
            }
        temp[camera.stream_url].append(result)

    for key,value in temp.items():
        if len(value)!=0:
            change_camera_alarm_status(value,key,app)

'''
关闭算法
'''
def close_algo(algo, app, direct_del=False):
    
    # 如果有算法正在检测，则不能直接删除，要先停止算法检测，再删除，否则直接删除。
    if not direct_del:
        
        # 如果有摄像头正在检测，则直接停止检测该算法
        for camera in db.session.query(CameraAlarmType).filter(db.and_(CameraAlarmType.alarm_type == algo.id, CameraAlarmType.status == "1")).all():
            camera_info = Camera.query.filter_by(id=camera.camera_id).first()
            restart_algo(camera_info, app, algo.id)
    
    # 修改算法状态
    try:
        Algorithm.query.filter_by(id=algo.id).update({"status": False})
        db.session.commit()
    except:
        return {"msg": "算法关闭失败。"}
    
    other_use = db.session.query(Algorithm).filter(db.and_(Algorithm.weight == algo.weight, Algorithm.status == True)).all()
    if not len(other_use):
        # 如果没有其它算法在使用该算法，则删除模型
        if len(eval(Weights.query.filter_by(id=algo.weight).first().name)):
            del models[algo.weight]
        Weights.query.filter_by(id=algo.weight).update({"status": False})
        db.session.commit()
        
    CameraAlarmType.query.filter_by(alarm_type=algo.id).delete()
    db.session.commit()
    return {"msg": "success"}


'''
开启算法：
'''
def open_algo(algo):
    open_num = Algorithm.query.filter_by(status=True).count()
    
    if not len(AlgorithmSetting.query.all()):
        num = AlgorithmSetting(id=1, algo_num=5, camera_num=6)
        db.session.add(num)
        db.session.commit()
    
    allow_num = int(AlgorithmSetting.query.first().algo_num)
    if open_num >= allow_num:
        return {"msg": "系统最多同时启用" + str(allow_num) + "个算法"}
    else:
        
        weight = Weights.query.filter_by(id=algo.weight).first()
        if not weight.status:
            if len(eval(weight.name)):
                tmp_model = []
                weight_type = weight.type
                path = weight.path
                for w in eval(weight.name):
                    weight_path = os.path.join(path, w)
                    res = loadmodel(weight_path, weight_type)
                    if res["msg"] == "success":
                        tmp_model.append(res["model"])
                    else:
                        return res
                models[algo.weight] = tmp_model
            Weights.query.filter_by(id=algo.weight).update({"status": True})
            db.session.commit()
               
        Algorithm.query.filter_by(id=algo.id).update({"status": True})
        db.session.commit()
        
        for camera in Camera.query.all():
            camera_algo = CameraAlarmType(
                camera_id = camera.id,
                alarm_type = algo.id,
                status = "0",
                enable_screen_mirroring = "0"
            )
            try:
                db.session.add(camera_algo)
                db.session.commit()
            except:
                return {"msg": "入库失败，请联系售后处理"}
        
        return {"msg": "success"}


def update_algo_status(id):
    algo_info = Algorithm.query.filter_by(id=id).first()
    if algo_info.status:
        return check_close_algo(algo_info)
    else:
        return open_algo(algo_info)



def reload_algo_det(app):
    data=db.session.query(Camera,CameraAlarmType).join(CameraAlarmType,CameraAlarmType.camera_id == Camera.id).filter(CameraAlarmType.status=='1').all()
    temp = {}

    for item in data:
        camera,camera_alarm_type = item
        if camera.stream_url not in temp:
            temp[camera.stream_url] = []
            result = {
                "camera_id":camera.id,
                "algo_id":camera_alarm_type.alarm_type,
                "status":camera_alarm_type.status
            }    
            temp[camera.stream_url].append(result)
        else:
            result = {
                "camera_id":camera.id,
                "algo_id":camera_alarm_type.alarm_type,
                "status":camera_alarm_type.status
            }    
            temp[camera.stream_url].append(result)
    
    for key,value in temp.items():
        if len(value)!=0:
            change_camera_alarm_status(value,key,app)