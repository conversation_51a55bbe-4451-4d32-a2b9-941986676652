import datetime
from applications.extensions import db

#邮件推送 收件人信息表
class Email_Push_Receive(db.Model):
    __tablename__ = 'email_push_receive'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='邮件编号')
    receive_email = db.Column(db.String(64), comment='收件人邮箱')
    receive_name = db.Column(db.String(64), comment='收件人姓名')
    send_id = db.Column(db.Integer, comment='发件邮箱id')
    
    def json(self):
        return {
            'id': self.id,
            'receive_email': self.receive_email,
            'receive_name': self.receive_name,
            'send_id': self.send_id,
        }