#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: qa_model.py

Description: This is an load asr and bge model Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

from optimum.onnxruntime import ORTModelForFeatureExtraction
from transformers import AutoTokenizer, AutoModel
import sherpa_onnx
from tools.check_files import assert_file_exists


#load asr and bge model
def create_recognizer(args):
    assert_file_exists(args.encoder)
    assert_file_exists(args.decoder)
    assert_file_exists(args.joiner)
    assert_file_exists(args.tokens)
    # Please replace the model files if needed.
    # See https://k2-fsa.github.io/sherpa/onnx/pretrained_models/index.html
    # for download links.
    recognizer = sherpa_onnx.OnlineRecognizer.from_transducer(
        tokens=args.tokens,
        encoder=args.encoder,
        decoder=args.decoder,
        joiner=args.joiner,
        num_threads=1,
        sample_rate=16000,
        feature_dim=80,
        enable_endpoint_detection=True,
        rule1_min_trailing_silence=2.2,
        rule2_min_trailing_silence=1.0,
        rule3_min_utterance_length=280,  # it essentially disables this rule
        decoding_method=args.decoding_method,
        provider=args.provider,
        hotwords_file=args.hotwords_file,
        hotwords_score=args.hotwords_score,
        blank_penalty=args.blank_penalty,
    )
    #sentences_1 = qa_pairs_extract(args.qa_pairs)
    #sentences_2 = [args.user_query]
    model_ort = ORTModelForFeatureExtraction.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5', revision="refs/pr/13",file_name="onnx/model.onnx")
    tokenizer = AutoTokenizer.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
    #model = AutoModel.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
    return recognizer,model_ort,tokenizer

