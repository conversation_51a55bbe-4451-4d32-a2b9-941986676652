import datetime
from applications.extensions import db

#发件人
class Email_Push_Set(db.Model):
    __tablename__ = 'email_push_set'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='邮件编号')
    send_email = db.Column(db.String(64), nullable=False, comment='发件邮箱地址')
    password = db.Column(db.String(64), nullable=False, comment='发件邮箱登录密码')
    type = db.Column(db.String(2), comment='发件邮箱类型')
    type_name = db.Column(db.String(16), comment='发件邮箱类型名称')
    smtp_server = db.Column(db.String(32), comment='SMTP服务器')
    # subject = db.Column(db.String(128), comment='邮件主题')
    # content = db.Column(db.Text(), comment='邮件正文')
    # send_id = db.Column(db.Integer, comment='发件邮箱id')
    def json(self):
        return {
            'id': self.id,
            'send_email': self.send_email,
            'password': self.password,
            'type': self.type,
            'type_name': self.type_name,
            'smtp_server': self.smtp_server,
        }