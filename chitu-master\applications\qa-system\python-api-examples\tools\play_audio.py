"""
File Name: play_audio.py

Description: This is an play audio Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import soundfile as sf
import sounddevice as sd


def play_audio_file(file_path):
    data, samplerate = sf.read(file_path)
    devices = sd.query_devices()
    sd.default.device = 25
    sd.play(data*20, samplerate)
    sd.wait()

