name: test-nodejs-npm

on:
  workflow_dispatch:

  schedule:
    # minute (0-59)
    # hour (0-23)
    # day of the month (1-31)
    # month (1-12)
    # day of the week (0-6)
    # nightly build at 23:50 UTC time every day
    - cron: "50 23 * * *"

concurrency:
  group: test-nodejs-npm-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test-nodejs-npm:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-2019]
        python-version: ["3.8"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - uses: actions/setup-node@v4
        with:
          registry-url: 'https://registry.npmjs.org'

      - name: Display node version
        shell: bash
        run: |
          node --version
          npm --version

      - name: Run tests
        shell: bash
        run: |
          node --version
          npm --version

          export d=nodejs-examples
          ./.github/scripts/test-nodejs-npm.sh
