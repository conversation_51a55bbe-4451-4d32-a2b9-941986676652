{"version": "v2.0.0", "updateTime": "2024-01-01", "updateLog": [{"version": "v2.0.0", "date": "2024-01-01", "changes": ["重构为标准化插件架构", "支持多推理引擎(RKNN/PyTorch/TensorRT/ONNX)", "新增流式任务管理功能", "优化人脸检测和识别算法", "新增考勤打卡功能", "支持陌生人检测和报警", "完善配置管理和国际化支持"]}], "compatibility": {"minPythonVersion": "3.8", "supportedPlatforms": ["RK3588", "RK3566", "RK3568", "x86_64"], "requiredLibraries": ["rknnlite", "opencv-python", "numpy", "faiss-cpu"]}}