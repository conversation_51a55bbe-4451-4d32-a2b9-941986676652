name: test-go

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/test-go.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'go-api-examples/**'
      - 'scripts/go/**'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/test-go.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'go-api-examples/**'
      - 'scripts/go/**'

  workflow_dispatch:

concurrency:
  group: test-go-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test-go:
    name: ${{ matrix.os }} ${{matrix.arch }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: macos-latest
            arch: amd64

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-${{ matrix.arch }}

      - uses: actions/setup-go@v5
        with:
          go-version: '>=1.12'

      - name: Display go version
        shell: bash
        run: |
          go version
          go env GOPATH
          go env GOARCH

      - name: Build sherpa-onnx
        shell: bash
        run: |
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          mkdir build
          cd build
          cmake -DCMAKE_C_COMPILER_LAUNCHER=ccache -DCMAKE_CXX_COMPILER_LAUNCHER=ccache -DBUILD_SHARED_LIBS=ON -DSHERPA_ONNX_ENABLE_PORTAUDIO=OFF -DSHERPA_ONNX_ENABLE_WEBSOCKET=OFF ..
          make -j1
          cp -v _deps/onnxruntime-src/lib/libonnxruntime*dylib ./lib/

          cd ../scripts/go/_internal/
          go mod tidy
          go build

      - name: Test streaming HLG decoding
        shell: bash
        run: |
          cd scripts/go/_internal/streaming-hlg-decoding/
          ./run.sh

      - name: Test speaker identification
        shell: bash
        run: |
          cd scripts/go/_internal/speaker-identification/
          ./run.sh

      - name: Test non-streaming TTS (macOS)
        shell: bash
        run: |
          mkdir tts-waves

          cd scripts/go/_internal/non-streaming-tts/
          ls -lh
          go mod tidy
          cat go.mod
          go build
          ls -lh

          git lfs install

          echo "Test vits-ljs"
          git clone https://huggingface.co/csukuangfj/vits-ljs
          ./run-vits-ljs.sh
          rm -rf vits-ljs

          echo "Test vits-vctk"
          git clone https://huggingface.co/csukuangfj/vits-vctk
          ./run-vits-vctk.sh
          rm -rf vits-vctk

          echo "Test vits-zh-aishell3"
          curl -SL -O https://github.com/k2-fsa/sherpa-onnx/releases/download/tts-models/vits-icefall-zh-aishell3.tar.bz2
          tar xvf vits-icefall-zh-aishell3.tar.bz2
          rm vits-icefall-zh-aishell3.tar.bz2
          ./run-vits-zh-aishell3.sh
          rm -rf vits-icefall-zh-aishell3

          echo "Test vits-piper-en_US-lessac-medium"
          git clone https://huggingface.co/csukuangfj/vits-piper-en_US-lessac-medium
          ./run-vits-piper-en_US-lessac-medium.sh
          rm -rf vits-piper-en_US-lessac-medium

          cp *.wav ../../../../tts-waves/

      - uses: actions/upload-artifact@v4
        with:
          name: tts-waves
          path: tts-waves

      - name: Test non-streaming decoding files (macOS)
        shell: bash
        run: |
          cd scripts/go/_internal/non-streaming-decode-files/
          ls -lh
          go mod tidy
          cat go.mod
          go build
          ls -lh

          git lfs install

          echo "Test transducer"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-zipformer-en-2023-06-26
          ./run-transducer.sh
          rm -rf sherpa-onnx-zipformer-en-2023-06-26

          echo "Test paraformer"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-paraformer-zh-2023-03-28
          ./run-paraformer.sh
          rm -rf sherpa-onnx-paraformer-zh-2023-03-28

          echo "Test NeMo CTC"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-nemo-ctc-en-conformer-medium
          ./run-nemo-ctc.sh
          rm -rf sherpa-onnx-nemo-ctc-en-conformer-medium

          echo "Test Whisper tiny.en"
          GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-whisper-tiny.en
          cd sherpa-onnx-whisper-tiny.en
          git lfs pull --include "*.onnx"
          cd ..
          ./run-whisper.sh
          rm -rf sherpa-onnx-whisper-tiny.en

          echo "Test Tdnn yesno"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-tdnn-yesno
          ./run-tdnn-yesno.sh
          rm -rf sherpa-onnx-tdnn-yesno

      - name: Test streaming decoding files
        shell: bash
        run: |
          cd scripts/go/_internal/streaming-decode-files
          ls -lh
          go mod tidy
          cat go.mod
          go build
          ls -lh

          git lfs install

          echo "Test zipformer2 CTC"
          wget -qq https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2
          tar xvf sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2
          rm sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13.tar.bz2

          ./run-zipformer2-ctc.sh
          rm -rf sherpa-onnx-streaming-zipformer-ctc-multi-zh-hans-2023-12-13

          echo "Test transducer"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-streaming-zipformer-en-2023-06-26
          ./run-transducer.sh
          rm -rf sherpa-onnx-streaming-zipformer-en-2023-06-26

          echo "Test paraformer"
          git clone https://huggingface.co/csukuangfj/sherpa-onnx-streaming-paraformer-bilingual-zh-en
          ./run-paraformer.sh
          rm -rf sherpa-onnx-streaming-paraformer-bilingual-zh-en
