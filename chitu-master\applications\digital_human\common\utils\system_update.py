import os
import json
import shutil
import requests
import hashlib
import zipfile
import tarfile
import subprocess
import time
from multiprocessing import Process

from log import mylog
from applications.digital_human.common.common_utils import read_version_info, read_config, basic_settings_config_path, DIGITAL_HUMAN_VERSION_PATH
from applications.digital_human.common.utils.qa_service import stop_qa_service_force

digital_human_data_path = "/mnt/keep/workspace/digital_human_data"
package_dir = os.path.join(digital_human_data_path, "digital_human_package")
package_download = "download"
package_unpack = "unpack"
package_backup = "backup"

database_path = "/mnt/keep/workspace/chitu/pear.db"
digital_human_path = "/mnt/keep/workspace/chitu/applications/digital_human"
qa_system_path = "/mnt/keep/workspace/chitu/applications/qa-system"
digitalHuman_web_path = "/mnt/keep/workspace/chitu-web/chitu-digital-human/dist"

def read_description_info(description_dir):
    with open(os.path.join(description_dir, "description.json"), 'r', encoding = 'utf-8') as f:
        description = json.load(f)
    return description

def copy_with_real_time_disk_write(src, dst, buffer_size=1024 * 1024):
    with open(src, 'rb') as src_file:
        with open(dst, 'wb') as dst_file:
            while True:
                chunk = src_file.read(buffer_size)
                if not chunk:
                    break
                dst_file.write(chunk)
                dst_file.flush()
                os.fsync(dst_file.fileno())

def update_version_info(status):
    with open(DIGITAL_HUMAN_VERSION_PATH, 'r', encoding = 'utf-8') as f:
        version = json.load(f)
    version["updating"] = status
    if not status:
        version["currentVersion"] = version["newVersion"]["version"]
        for k in version["newVersion"].keys():
            version["newVersion"][k] = ""
    with open(DIGITAL_HUMAN_VERSION_PATH, 'w') as write_f:
        json.dump(version, write_f, indent=4, ensure_ascii=False)
        write_f.flush()
        os.fsync(write_f.fileno())

def check_md5(target_file, md5):
    try:
        m = hashlib.md5()
        with open(target_file, 'rb') as f:
            for line in f:
                m.update(line)
        md5code = m.hexdigest()
        if md5code != md5:
            return False
        else:
            return True
    except Exception as e:
        mylog.info(e)
        return False

def download(url, target_file, md5):
    try:
        response = requests.get(url, timeout=(10, 180))
        with open(target_file, 'wb') as file:
            file.write(response.content)
            file.flush()
            os.fsync(file.fileno())
        
        if check_md5(target_file, md5):
            return True, "下载成功", 201
        else:
            return False, "文件MD5值校验失败", 412
    except Exception as e:
        mylog.info(e)
        return False, "下载失败", 411
    
    
def unpack(file_path, target_dir):
    try:
        if file_path.endswith('.zip'):
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(target_dir)
        elif file_path.endswith('.tar') or file_path.endswith('.tar.gz') or file_path.endswith('.tgz'):
            with tarfile.open(file_path, 'r') as tar_ref:
                tar_ref.extractall(target_dir)
        else:
            return False, "不支持的压缩格式", 421
        return True, "", 200
    except Exception as e:
        mylog.info(e)
        return False, "更新包解压失败", 422

def package_check(target_dir):
    try:
        dirs = os.listdir(target_dir)
        need_exist = ["digital_human", "dist", "qa-system", "description.json"]
        if set(need_exist) <= set(dirs):
            description = read_description_info(target_dir)
            if description["package_add"]:
                if not os.path.exists(os.path.join(target_dir, "others", "install.sh")):
                    return False, "缺少安装环境包的脚本文件", 424
            if description["database_update"]:
                if not os.path.exists(os.path.join(target_dir, "others", "pear.sh")):
                    return False, "缺少数据库文件修改脚本", 425
            if len(description["digital_human_data"].keys()) > 0:
                for files in description["digital_human_data"].keys():
                    if not os.path.exists(os.path.join(target_dir, "others", "digital_human_data", files)):
                        return False, "缺少数据文件", 426
            return True, "", 200
        else:
            return False, "更新包缺少必要文件", 427
    except Exception as e:
        mylog.info(e)
        return False, "更新包文件校验失败", 423

def backup_prepare(target_dir, description):
    try:
        if description["package_add"]:
            file_name = os.path.join(target_dir, "others", 'install.sh')
            cmd_com = f"sed -i 's/\r$//' {file_name}"
            result = subprocess.Popen(cmd_com, stdout=subprocess.PIPE, shell=True)
            result = subprocess.run(['sh', os.path.join(target_dir, "others", 'install.sh')], check=True, text=True, capture_output=True)
            if result.returncode != 0:
                return False, "环境包安装失败", 428
        if description["database_update"]:
            try:
                shutil.copyfile(database_path, os.path.join(os.path.dirname(database_path), "pear_new.db"))
                file_name = os.path.join(target_dir, "others", 'pear.sh')
                cmd_com = f"sed -i 's/\r$//' {file_name}"
                result = subprocess.Popen(cmd_com, stdout=subprocess.PIPE, shell=True)
                result = subprocess.run(['sh', os.path.join(target_dir, "others", 'pear.sh')] + [os.path.join(os.path.dirname(database_path), "pear_new.db")], check=True, text=True, capture_output=True)
                if result.returncode != 0:
                    if os.path.exists(os.path.join(os.path.dirname(database_path), "pear_new.db")):
                        os.remove(os.path.join(os.path.dirname(database_path), "pear_new.db"))
                    return False, "数据库迁移失败", 429
            except:
                if os.path.exists(os.path.join(os.path.dirname(database_path), "pear_new.db")):
                    os.remove(os.path.join(os.path.dirname(database_path), "pear_new.db"))
                return False, "数据库迁移失败", 429
        if len(description["config"].keys()) > 0:
            conf = read_config()
            for k in description["config"].keys():
                if k not in conf.keys():
                    conf[k] = description["config"][k]
            with open(os.path.join(target_dir, "config.json"), 'w', buffering=1) as write_f:
                json.dump(conf, write_f, indent=4, ensure_ascii=False)
                write_f.flush()
                os.fsync(write_f.fileno())
        return True, "", 200
    except Exception as e:
        mylog.info(e)
        return False, "配置文件迁移失败", 430   

def backup(backup_path, description, description_path):
    try:
        # 检查前后端以及语音算法代码是否备份
        if not os.path.exists(os.path.join(backup_path, "backup.tar.gz")):
            return False
        else:
            version = read_version_info()
            if time.time() - version["newVersion"]["createTime"] < 60:
                time.sleep(int(time.time() - version["newVersion"]["createTime"]))
                 
            # with tarfile.open(os.path.join(backup_path, "backup.tar.gz"), 'r') as tar:  
            #     tar.extractall(backup_path)
            #     tar.fileobj.flush()
            #     os.fsync(tar.fileobj.fileno())
            
        # 检查数字人历史数据是否备份
        if len(description["digital_human_data"].keys()) > 0:
            if not os.path.exists(os.path.join(backup_path, "digital_human_data.tar.gz")):
                return False
               
        # 备份数据库是否备份
        if not os.path.exists(os.path.join(backup_path, "pear.db")):
            return False
        
        # 备份说明文件
        copy_with_real_time_disk_write(description_path, os.path.join(backup_path, "description.json"))
        
        return True
    except Exception as e:
        mylog.info(e)
        return False

def copy_files_with_flush(src_dir, dst_dir):
    """
    按照原目录结构将文件从 src_dir 复制到 dst_dir，并确保实时落盘。
    """
    for root, dirs, files in os.walk(src_dir):
        relative_path = os.path.relpath(root, src_dir)
        target_root = os.path.join(dst_dir, relative_path)
        
        if relative_path.split('/')[-1] == "__pycache__":
            continue
        
        if not os.path.exists(target_root):
            os.makedirs(target_root)

        for file in files:
            src_file = os.path.join(root, file)
            dst_file = os.path.join(target_root, file)

            with open(src_file, "rb") as src_f, open(dst_file, "wb") as dst_f:
                shutil.copyfileobj(src_f, dst_f)
                dst_f.flush()
                os.fsync(dst_f.fileno())
    time.sleep(1)


def update(file_dir, description):
    ''' 
    progress_code:
    0: 成功
    1：替换配置文件失败
    2：替换 pear.db 失败
    3：替换 digital_human 文件夹失败
    4：替换 qa-system 文件夹失败
    5：替换 dist 文件夹失败
    6：移动数据文件失败
    7: 更新成功之后导包失败
    以上操作按顺序执行的，方便回滚，比如失败码为3，则回滚1，2步骤
    '''
    progress_code = 1
    try:
        # 1. 替换配置文件
        update_version_info(True)
        if len(description["config"].keys()) > 0:
            copy_with_real_time_disk_write(os.path.join(file_dir, "config.json"), basic_settings_config_path)
            # shutil.copy(os.path.join(file_dir, "config.json"), basic_settings_config_path)
            
        # 2. 替换 pear.db
        progress_code = 2   
        if description["database_update"]:
            if os.path.exists(database_path):
                os.remove(database_path)
            os.rename(os.path.join(os.path.dirname(database_path), "pear_new.db"), database_path)
            
        # 3. 替换 digital_human 文件夹
        progress_code = 3
        shutil.rmtree(digital_human_path)
        copy_files_with_flush(os.path.join(file_dir, "digital_human"), digital_human_path)

        # 4. 替换 qa-system 文件夹
        progress_code = 4
        shutil.rmtree(qa_system_path)
        copy_files_with_flush(os.path.join(file_dir, "qa-system"), qa_system_path)

        # 5. 替换 dist 文件夹
        progress_code = 5
        copy_files_with_flush(os.path.join(file_dir, "dist"), digitalHuman_web_path)
        
        # 6. 移动数据文件
        progress_code = 6
        if len(description["digital_human_data"].keys()) > 0:
            for file in description["digital_human_data"].keys():
                target_dir = description["digital_human_data"][file]
                if target_dir.startswith('/'):
                    if not target_dir.startswith(digital_human_data_path):
                        update_version_info(False)
                        return False, progress_code
                elif target_dir == "." or target_dir == "":
                    target_dir = digital_human_data_path
                elif target_dir.startswith('./'):
                    target_dir = os.path.join(digital_human_data_path, target_dir[2:])
                else:
                    target_dir = os.path.join(digital_human_data_path, target_dir)
                    
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir)
                if os.path.isfile(os.path.join(file_dir, "others", "digital_human_data", file)):
                    copy_with_real_time_disk_write(os.path.join(file_dir, "others", "digital_human_data", file), os.path.join(target_dir, file))
                if os.path.isdir(os.path.join(file_dir, "others", "digital_human_data", file)):
                    if os.path.exists(os.path.join(target_dir, file)):
                        shutil.rmtree(os.path.join(target_dir, file))
                    copy_files_with_flush(os.path.join(file_dir, "others", "digital_human_data", file), os.path.join(target_dir, file))

        progress_code = 0
        return True, progress_code
    except Exception as e:
        mylog.info(e)
        update_version_info(False)
        return False, progress_code

def rollback(description, backup_path, code=6):
    try:
        if code > 0:
            if len(description["config"].keys()) > 0:
                shutil.copy(os.path.join(backup_path, "digital_human_data/basicSettings/config.json", "config.json"), basic_settings_config_path)
        # if code > 1:
        #     if description["database_update"]:
        #         shutil.copy(os.path.join(backup_path, "pear.db"), database_path)
        if code > 2:
            with tarfile.open(os.path.join(backup_path, "backup.tar.gz"), 'r') as tar:  
                tar.extractall(backup_path)
                tar.fileobj.flush()
                os.fsync(tar.fileobj.fileno())
            shutil.rmtree(digital_human_path)
            shutil.copytree(os.path.join(backup_path, "digital_human"), digital_human_path, dirs_exist_ok=True)
        if code > 3:
            shutil.rmtree(qa_system_path)
            shutil.copytree(os.path.join(backup_path, "qa-system"), qa_system_path, dirs_exist_ok=True)
        if code > 4:
            shutil.rmtree(digitalHuman_web_path)
            shutil.copytree(os.path.join(backup_path, "dist"), digitalHuman_web_path, dirs_exist_ok=True)
        if code > 5:
            with tarfile.open(os.path.join(backup_path, "digital_human_data.tar.gz"), 'r') as tar:  
                tar.extractall(backup_path)
                tar.fileobj.flush()
                os.fsync(tar.fileobj.fileno())
            shutil.copytree(os.path.join(backup_path, "digital_human_data"), digital_human_data_path, dirs_exist_ok=True)
        return True
    except Exception as e:
        mylog.info(e)
        return False

def kill_app():
    try:
        stop_qa_service_force()
        time.sleep(3)
        os.system("sh stop.sh")
    except Exception as e:
        mylog.info(e)

async def send_msg_deal(clients, code, message):
    msg = {
        "messageType": "updateProgress",
        "messageBody": {
            "code": code,
            "msg": message
        }
    }
    await clients["screen"].send(json.dumps(msg))
    mylog.info("WEBSOCKET: 向前端发送消息：" + str(msg))


# 系统更新主函数
async def update_digitalHuman_system(clients):
    version_info = read_version_info()
    url = version_info["newVersion"]["downloadUrl"]
    md5 = version_info["newVersion"]["md5"]
    file_name = url.split("/")[-1]
    download_file = os.path.join(package_dir, package_download, file_name)
    
    if os.path.exists(os.path.join(package_dir, package_download)):
        shutil.rmtree(os.path.join(package_dir, package_download))
    os.makedirs(os.path.join(package_dir, package_download))
    
    res, msg, res_code = download(url, download_file, md5)
    if res:
        await send_msg_deal(clients, 201, msg)
        mylog.info("系统更新：下载完成。")
        
        unpack_dir = os.path.join(package_dir, package_unpack)
        if os.path.exists(unpack_dir):
            shutil.rmtree(unpack_dir)
        os.makedirs(unpack_dir)
        
        res, msg, res_code = unpack(download_file, unpack_dir)
        if res:
            mylog.info("系统更新：解压完成。")
            
            package_file_dir = os.path.join(unpack_dir, os.listdir(unpack_dir)[0])
            res, msg, res_code = package_check(package_file_dir)
            if res:
                mylog.info("系统更新：文件校验成功。")
                description = read_description_info(package_file_dir)
                res, msg, res_code = backup_prepare(package_file_dir, description)
                if res:
                    backup_dir = os.path.join(package_dir, package_backup)
                    if backup(backup_dir, description, os.path.join(package_file_dir, "description.json")):
                        mylog.info("系统更新：文件备份检查成功。")
                        update_res, update_code = update(package_file_dir, description)
                        if update_res:
                            mylog.info("系统更新：更新成功，即将重启服务！！！！！")
                            update_version_info(False)
                            await send_msg_deal(clients, 202, "更新成功, 即将重启服务。")
                            kill_process = Process(target=kill_app)
                            kill_process.start()
                        else:
                            await send_msg_deal(clients, 432, "更新失败")
                            await send_msg_deal(clients, 203, "即将开始回滚")
                            mylog.info("系统更新：更新失败，即将回滚。")
                            if not rollback(description, backup_dir, update_code):
                                mylog.info("系统更新：回滚失败，系统崩盘。")
                            else:
                                mylog.info("系统更新：回滚成功！！！！！")
                    else:
                        await send_msg_deal(clients, 431, "数据备份失败")
                        mylog.info("系统更新：备份失败。")
                else:
                    await send_msg_deal(clients, res_code, msg)
                    mylog.info("系统更新：安装环境包失败或数据库迁移失败或配置文件迁移失败。")
            else:
                await send_msg_deal(clients, res_code, msg)
                mylog.info("系统更新：文件校验失败。")
        else:
            await send_msg_deal(clients, res_code, msg)
            mylog.info("系统更新：解压失败。")
    else:
        await send_msg_deal(clients, res_code, msg)
        mylog.info("系统更新：下载失败。")