#!/usr/bin/env python3
"""
人脸检测算法修复验证脚本
测试detect_face_alarm算法的检测和坐标转换是否正确
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
from pathlib import Path

# 添加算法路径
sys.path.append('detect_face_alarm/v2_0_0')

def create_test_image(width=640, height=480):
    """创建测试图像"""
    # 创建一个简单的测试图像
    img = np.ones((height, width, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 绘制一些简单的形状作为"人脸"
    cv2.rectangle(img, (100, 100), (200, 200), (255, 255, 255), -1)  # 白色矩形
    cv2.rectangle(img, (400, 200), (500, 300), (255, 255, 255), -1)  # 另一个白色矩形
    
    # 添加一些细节
    cv2.circle(img, (150, 150), 20, (0, 0, 0), -1)  # 黑色圆圈
    cv2.circle(img, (450, 250), 20, (0, 0, 0), -1)  # 黑色圆圈
    
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    return img_base64

def test_preprocessing():
    """测试预处理流程"""
    print("=" * 60)
    print("测试1: 预处理流程")
    print("=" * 60)
    
    try:
        from preprocess.detect_preprocess import DetectPreProcess
        
        # 创建测试图像
        test_img = create_test_image(800, 600)
        print(f"原始测试图像尺寸: {test_img.shape}")
        
        # 转换为base64
        img_base64 = image_to_base64(test_img)
        
        # 构建测试数据
        test_data = {
            "businessData": {
                "image": img_base64,
                "imageType": "base64",
                "advsetValue": {
                    "recvDataType": ["draw_image", "source_image", "infer_boxes"],
                    "faceDetect": "all"
                }
            }
        }
        
        # 测试预处理
        preprocessor = DetectPreProcess()
        letterbox_img, source_img, gain = preprocessor.get_data(test_data)
        
        print(f"✅ 预处理成功:")
        print(f"  - 原始图像: {source_img.shape}")
        print(f"  - letterbox图像: {letterbox_img.shape}")
        print(f"  - gain参数: {gain}")
        
        return letterbox_img, source_img, gain
        
    except Exception as e:
        print(f"❌ 预处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n" + "=" * 60)
    print("测试2: 坐标转换")
    print("=" * 60)
    
    try:
        from inference.inference_rknn.utils_rknn.utils import get_face_boxes
        
        # 模拟letterbox坐标系的检测结果
        letterbox_detections = [
            [100, 100, 200, 200, 0.9, 0],  # 第一个人脸
            [400, 300, 500, 400, 0.8, 0],  # 第二个人脸
        ]
        
        # 模拟gain参数 [scale, dw, dh]
        gain = [0.8, 80, 60]  # 缩放0.8倍，填充80像素宽，60像素高
        
        # 原始图像尺寸
        original_shape = (600, 800, 3)  # (h, w, c)
        
        print(f"letterbox检测结果: {letterbox_detections}")
        print(f"gain参数: {gain}")
        print(f"原始图像尺寸: {original_shape}")
        
        # 转换坐标
        converted_boxes = get_face_boxes(letterbox_detections, gain, ["face"], original_shape)
        
        print(f"✅ 坐标转换成功:")
        for i, box in enumerate(converted_boxes):
            print(f"  - 人脸{i+1}: {box}")
        
        return converted_boxes
        
    except Exception as e:
        print(f"❌ 坐标转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_full_pipeline():
    """测试完整流程"""
    print("\n" + "=" * 60)
    print("测试3: 完整检测流程")
    print("=" * 60)
    
    try:
        from utils.face_recognition import DetectFaceAlarm
        
        # 创建算法实例
        face_detector = DetectFaceAlarm("face_recognition", "v2.0.0", "face_alarm")
        
        # 创建测试图像
        test_img = create_test_image(640, 480)
        img_base64 = image_to_base64(test_img)
        
        # 构建测试数据
        test_data = {
            "transactionNumber": "test_001",
            "businessData": {
                "image": img_base64,
                "imageType": "base64",
                "advsetValue": {
                    "recvDataType": ["draw_image", "source_image", "infer_boxes", "infer_results"],
                    "faceDetect": "all",
                    "interval": 5
                }
            }
        }
        
        print(f"测试数据准备完成，图像尺寸: {test_img.shape}")
        
        # 执行推理
        result = face_detector.infer(test_data, "models.test_model")
        
        if result:
            print("✅ 完整流程测试成功")
            print("  - 算法接受了输入数据")
            print("  - 推理流程正常启动")
        else:
            print("❌ 完整流程测试失败")
            
        return result
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detection_parameters():
    """测试检测参数"""
    print("\n" + "=" * 60)
    print("测试4: 检测参数验证")
    print("=" * 60)
    
    try:
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建推理实例
        inference = InferenceRknn(["face"], "detect_face_alarm/v2_0_0/weights", "retinaface_mob", 0)
        
        print(f"✅ 推理引擎初始化成功:")
        print(f"  - 置信度阈值: {inference._confidence}")
        print(f"  - NMS阈值: {inference._nms_threshold}")
        print(f"  - 最小人脸比例: {inference._min_face_ratio}")
        print(f"  - 最大人脸数量: {inference._max_faces_per_image}")
        print(f"  - 最小人脸尺寸: {inference._min_face_size}")
        
        # 测试参数更新
        inference.update_detection_params(
            confidence=0.7,
            nms_threshold=0.3,
            min_face_ratio=0.01,
            max_faces=5
        )
        
        print(f"✅ 参数更新成功:")
        print(f"  - 新置信度阈值: {inference._confidence}")
        print(f"  - 新NMS阈值: {inference._nms_threshold}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检测参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 人脸检测算法修复验证")
    print("=" * 60)
    
    # 检查算法文件是否存在
    algo_path = Path("detect_face_alarm/v2_0_0")
    if not algo_path.exists():
        print("❌ 算法路径不存在，请确保在正确的目录下运行测试")
        return
    
    print(f"✅ 算法路径存在: {algo_path.absolute()}")
    
    # 运行各项测试
    tests = [
        ("预处理流程", test_preprocessing),
        ("坐标转换", test_coordinate_conversion),
        ("检测参数", test_detection_parameters),
        ("完整流程", test_full_pipeline),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result is not None and result is not False
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！算法修复成功")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
