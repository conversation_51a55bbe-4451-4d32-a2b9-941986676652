import cv2
import base64


def image_base64(image):
    # image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 将图像数据转换为字节数组
    is_success, buffer = cv2.imencode('.jpg', image)
    if not is_success:
        print('图片编码失败')
    # 将字节数组转换为Base64编码的字符串
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    return img_base64


def image_byte(image):
    # 将图片编码为 JPEG 格式的字节流（可替换为 '.png'）
    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 90]  # 可选：JPEG 压缩质量（0-100）
    _, encoded_image = cv2.imencode(".jpg", image, encode_param)
    # 转换为 Python 的 bytes 类型
    image_bytes = encoded_image.tobytes()
    return image_bytes