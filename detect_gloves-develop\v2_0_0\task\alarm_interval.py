import time 

class AlarmInterval(object):
    def __init__(self, interval):
        self.time = 0
        self.interval = interval
    def run_interval(self, post_bool):
        if post_bool:
            time_now = time.time()
            # a = time_now - self.time
            # print('LOSS TIME ', a)
            if time_now - self.time > self.interval:
                self.time = time.time()
                return True
            else:
                return False
        else:
            return False