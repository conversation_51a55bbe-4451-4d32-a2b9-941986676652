# from models.base_plugin import BasePlugin
# 临时使用简单的基类
class BasePlugin:
    def __init__(self, model_name, model_version, algo_name):
        self.model_name = model_name
        self.model_version = model_version
        self.algo_name = algo_name
import gc
from threading import Thread
from multiprocessing import Process, Queue
import sys
import os
import time
import importlib

# 添加当前目录到路径 - 更强健的路径修复
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 确保能找到v2_0_0目录
v2_0_0_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if v2_0_0_dir not in sys.path:
    sys.path.insert(0, v2_0_0_dir)

from utils.log import mylog
from inference.model_check import ModelCheck
from preprocess.detect_preprocess import DetectPreProcess
from postprocess.post_process import PostProcess
from task.image_alarm_interval import contrast_time
from utils.frame_flow import frame_flow
from utils.i18n import I18n


class DetectFaceAlarm(BasePlugin):
    """
    人脸识别算法类 - 完全基于detect_gloves标准框架
    一个权重对应一个类，一个接口
    Args:
        BasePlugin (_type_): _description_
    """

    def __init__(self, model_name, model_version, algo_name):
        super().__init__(model_name, model_version, algo_name)
        self.post_mess = [model_name, model_version, algo_name]
        self.que = {}
        self.process_pid = {}
        self.kill_que = {}
        self.size = [640, 640]
        self.weight = "retinaface_mob"
        self.label = ['face']
        self.i18n = I18n()
        self.in_que = Queue(maxsize=5)
        self.out_que = Queue(maxsize=5)
        k = 3
        self.run_model = {}
        for i in range(k):
            self.run_model.update({i:Process(target=self._infer, args=(i, self.in_que, self.out_que), daemon=True)})
            self.run_model[i].start()
        back_model = Thread(target=self._get_message, daemon=True)
        back_model.start()

    def __check_param(self, data):
        """检查单张图片推理参数 - 与detect_gloves保持一致"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if not data.get("businessData", {}).get("image"):
            return False
        if data.get("businessData", {}).get("imageType") not in ["base64", "byte"]:
            return False
        return True

    def __url_check_param(self, data):
        """检查流任务参数 - 与detect_gloves保持一致"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if data.get("callBackData", {}).get("requestMethod") not in ["post", "POST"]:
            return False
        return True

    def change_language(self, language):
        back_message = self.i18n.reload(language)
        return back_message

    def infer(self, data, model_way): # 接收字典 - 与detect_gloves保持一致
        if self.__check_param(data):
            if self.in_que.full():
                self.in_que.get()
            self.in_que.put([data, model_way])
            return True
        else:
            mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
            return False

    def start_task(self, task_id, data):
        # 实现流的开启检测任务, 队列，算法状态，- 完全按照detect_gloves标准
        try:
            if self.__url_check_param(data):
                self.que.update({task_id: Queue()})
                self.kill_que.update({task_id: Queue()})
                self.process_pid.update({task_id: Process(target=frame_flow, args=(self.weight, self.post_mess, self.i18n, task_id, self.que[task_id], self.kill_que[task_id]))})
                self.process_pid[task_id].start()
                self.que[task_id].put(data)
                return 200
            else:
                mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
                return 500
        except Exception as e:
            mylog.error(f"start task error: {e} \n")
            return 500

    def stop_task(self, task_id):
        try:
            # 关闭流的检测任务
            if task_id in self.kill_que:
                self.kill_que[task_id].put(True)
                self.process_pid[task_id].join()  # 确保进程已经完全终止

            if task_id in self.process_pid:
                if self.process_pid[task_id].is_alive():
                    self.process_pid[task_id].terminate()
                    gc.collect()

            if not self.process_pid[task_id].is_alive():
                del self.process_pid[task_id]
                del self.kill_que[task_id]
                del self.que[task_id]

            return 200
        except Exception as e:
            mylog.error(f"stop task error: {e}")
            return 500

    def change_task(self, task_id, data):
        try:
            # 算法更改状态，摄像头参数更改
            if task_id in self.que:
                self.que[task_id].put(data)
            return 200
        except Exception as e:
            mylog.error(f"change task error: {e}")
            return 500

    def query_task(self, task_id=None):
        back_task = {}
        if task_id is None:
            for id in self.process_pid.keys():
                back_task[id] = self.process_pid[id].is_alive()
        elif task_id in self.process_pid.keys():
            back_task[task_id] = self.process_pid[task_id].is_alive()
        return back_task

    def _infer(self, number, in_que, out_que):
        engine_excuter = ModelCheck(self.label, self.weight, number)  # face = [人脸]
        pre_process = DetectPreProcess()
        post_process = PostProcess(self.i18n, self.post_mess)
        while True:
            try:
                if not in_que.empty():
                    data, model_way = in_que.get()
                    pre_data, source_img, gain = pre_process.get_data(data) # 前处理
                    output = engine_excuter.run(pre_data, gain) # 推理
                    back_json, post_bool = post_process.run_process(data, output, source_img)
                    out_que.put([back_json, post_bool, data, model_way])
                else:
                    time.sleep(0.01)
            except Exception as e:
                mylog.error(f"infer image error: {e} \n")
                out_que.put([None, False, data, model_way])

    def _get_message(self):
        interval_image = {}
        while True:
            if not self.out_que.empty():
                back_json, post_bool, data, model_way = self.out_que.get()
                # 从这打印
                business_data = data["businessData"]
                if 'interval' in business_data['advsetValue']:
                    transaction_number = data['transactionNumber']
                    interval = business_data['advsetValue']['interval']
                    #还有这里
                    if post_bool:
                        alarm_bool, interval_image = contrast_time(interval_image, transaction_number, interval)
                        interval_image = interval_image
                        if alarm_bool:
                            back_json["transactionNumber"] = transaction_number
                            importlib.import_module(model_way).get_model_back(back_json, data)
                        else:
                            importlib.import_module(model_way).get_model_back(None, data)
                    else:
                        importlib.import_module(model_way).get_model_back(None, data)
                else:
                    if post_bool:
                        back_json["transactionNumber"] = data.get('transactionNumber', '')
                    importlib.import_module(model_way).get_model_back(back_json if post_bool else None, data)
            else:
                time.sleep(0.01)
