import datetime
from applications.extensions import db

class PushSettings(db.Model):
    __tablename__ = 'admin_push_settings'

    id = db.<PERSON>umn(db.<PERSON>I<PERSON>ger, primary_key=True, autoincrement=True, comment='主键Id')
    push_type = db.Column(db.String(1), nullable=False, comment='推送类型 1：微信 2：钉钉 3：邮箱')
    push_type_name = db.Column(db.String(16), nullable=False, comment='推送类型名称')
    status = db.Column(db.String(1), nullable=False, comment='状态 0：关闭 1：开启')

    def json(self):
        return {
            'id': self.id,
            'push_type': self.push_type,
            'push_type_name': self.push_type_name,
            'status': self.status
        }