from ..preprocess.rtsp_camera import RtspCamera
from ..preprocess.usb_camera import UsbCamera
from ..preprocess.video_file import VideoFile
from ..preprocess.utils.preprocess import PreProcess
from ..task.http_post import HttpPost
from ..task.alarm_interval import AlarmInterval
from ..postprocess.post_process import PostProcess
from ..inference.model_check import ModelCheck
from ..utils.log import mylog
import threading
import time
import copy
import gc



def frame_flow(weight, post_mess, i18n, task_id, que, kill_que):
    label = ["fallperson", "person"]
    engine_excuter = ModelCheck(label, weight, -1)  # helmet = [帽子, 颜色, 数量]
    while True:
        try:
            if que.empty() and kill_que.empty():
                time.sleep(1)
                continue
            if not kill_que.empty():
                kill_bool = kill_que.get()
                if kill_bool:
                    del engine_excuter
                    break
            message_json = que.get()
            pre_process = PreProcess()
            post_process = PostProcess(i18n, post_mess)
            http_post = HttpPost(message_json["businessData"]["imageType"])
            business_data = message_json["businessData"]
            url_type = business_data["urlType"]
            http_post.post_url = message_json["callBackData"]["callBackUrl"]
            if "interval"in business_data["advsetValue"]:
                interval = business_data["advsetValue"]["interval"]
            else:
                interval = 30
            alarm_interval = AlarmInterval(interval)
            
            # rtsp摄像头
            if url_type == 0:
                lock = threading.Lock()
                rtsp_url = business_data["url"]
                if "frameRate" in business_data:
                    spaces = business_data["frameRate"]
                else:
                    spaces = 4
                dataset = RtspCamera(lock, rtsp_url, spaces)
                for frame in dataset:
                    if len(frame) == 0:
                        time.sleep(1)
                        continue
                    lock.acquire()
                    source_img = copy.deepcopy(frame)
                    lock.release()
                    pre_data, gain = pre_process.letterbox(source_img) # 前处理
                    output = engine_excuter.run(pre_data, gain) # 推理
                    back_json, post_bool = post_process.run_process(message_json, output, source_img)
                    back_bool = alarm_interval.run_interval(post_bool)
                    if back_bool:
                        back_json["inDeviceId"] = business_data["inDeviceId"]
                        back_json["taskId"] = task_id
                        http_post.data.append(back_json)
                    if not que.empty() or not kill_que.empty():
                        break

            # usb摄像头
            if url_type == 1:
                lock = threading.Lock()
                rtsp_url = business_data["url"]
                if "frameRate" in business_data:
                    spaces = business_data["frameRate"]
                else:
                    spaces = 4
                dataset = UsbCamera(lock, rtsp_url, spaces)
                for frame in dataset:
                    if len(frame) == 0:
                        time.sleep(1)
                        continue
                    lock.acquire()
                    source_img = copy.deepcopy(frame)
                    lock.release()
                    pre_data, gain = pre_process.letterbox(source_img) # 前处理
                    output = engine_excuter.run(pre_data, gain) # 推理
                    back_json, post_bool = post_process.run_process(message_json, output, source_img)
                    back_bool = alarm_interval.run_interval(post_bool)
                    if back_bool:
                        back_json["inDeviceId"] = business_data["inDeviceId"]
                        back_json["taskId"] = task_id
                        http_post.data.append(back_json)
                    if not que.empty() or not kill_que.empty():
                        break
            
            # 视频文件
            if url_type == 2:
                rtsp_url = business_data["url"]
                if "frameRate" in business_data:
                    spaces = business_data["frameRate"]
                else:
                    spaces = 4
                dataset = VideoFile(rtsp_url, spaces)
                while True:
                    source_img = VideoFile.get_image()
                    if source_img is None:
                        print("完成视频分析")
                        break
                    pre_data, gain = pre_process.letterbox(source_img) # 前处理
                    output = engine_excuter.run(pre_data, gain) # 推理
                    back_json, post_bool = post_process.run_process(message_json, output, source_img)
                    back_bool = alarm_interval.run_interval(post_bool)
                    if back_bool:
                        back_json["inDeviceId"] = business_data["inDeviceId"]
                        back_json["taskId"] = task_id
                        http_post.data.append(back_json)

                    if not que.empty() or not kill_que.empty():
                        break
        
            del pre_process
            del post_process
            del http_post
            del alarm_interval
            gc.collect()
        except Exception as e:
            mylog.error(f"frame flow error: {e} \n")
            break

                
            
