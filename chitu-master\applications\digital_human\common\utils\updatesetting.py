import requests
import json
from applications.extensions import db
from applications.digital_human.models import digital_human_basic_setting 
from applications.digital_human.models import digital_human_interactive 
from log import mylog
import os
import socket


s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
s.connect(('8.8.8.8',1))
resp = s.getsockname()[0]
res = resp + ':9620'


def upload_video(videos,app):
    video_url = {}
    path = '/mnt/keep/workspace/digital_human_data/digital_human_videos/wsb_videos'
    if not os.path.exists(path):
        os.makedirs(path)
    files = os.listdir(path)
    file_list = [f for f in files]
    if len(file_list) > 5:
        for f in file_list:
            os.remove(os.path.join(path, f))
    try:
        for v in videos:
            video = videos.get(v)
            response = requests.get(video, timeout=5)
            filename = video.split('/')[-1] 
            save_path = f'/mnt/keep/workspace/digital_human_data/digital_human_videos/wsb_videos/{filename}'  
            nginx_path = f'wsb_videos/{filename}'

            if response.status_code == 200:
                with open(save_path, 'wb') as f:  
                    f.write(response.content)  
                url = nginx_path
                video_url[v] = url
                
            else:
                return None

    except:
        pass

    return video_url


def download_video(video_url, app):
    try:
        if video_url is None:
            return 500, None
        
        video_url = "http://" + app.config['MINIO_ADDRESS'] + video_url
        response = requests.get(video_url, timeout=5)
        filename = video_url.split('/')[-1] 
        save_path = f'/mnt/keep/workspace/digital_human_data/digital_human_videos/wsb_videos/{filename}'  
        nginx_path = f'wsb_videos/{filename}'

        if response.status_code == 200:
            with open(save_path, 'wb') as f:  
                f.write(response.content)  
            return 200, nginx_path
        else:
            return 500, None
    except:
        return 500, None


def upload_bottom_video(bottom_list, app):
    if bottom_list is not None:
        bottom_line_list = []
        for item in bottom_list:
            bottom_line = {}
            ret, url = download_video(item.get("bottomLineVideoUrl", None), app)
            if ret == 200:
                bottom_line["bottom_line_reply"] = item.get("bottomLineReply", None)
                bottom_line["bottomVideoDuration"] = item.get("bottomVideoDuration", None)
                bottom_line["bottomLineVideoUrl"] = url
                bottom_line_list.append(bottom_line)
            else:
                return []
        return bottom_line_list
    else:
        mylog.info("兜底话术字段为空.....")

def update_awakenGreeting(messageBody, app):
    dir = {}
    dir['muteItGreeting'] = messageBody.get("muteItGreeting", None)
    dir["unmuteItGreeting"] = messageBody.get("unmuteItGreeting", None)
    dir['attendanceMode'] = messageBody.get("attendanceMode", None)
    dir['exitAttendanceMode'] = messageBody.get("exitAttendanceMode", None)
    dir['exitAttendanceDuration'] = messageBody.get("exitAttendanceDuration", None)
    dir['muteAssistantMode'] = messageBody.get("muteAssistantMode", None)
    dir['muteUnattendedMode'] = messageBody.get("muteUnattendedMode", None)
    dir['enterPublicity'] = messageBody.get("mutePublicity", "进入宣传")
    dir['exitPublicity'] = messageBody.get("unmutePublicity", "退出宣传")

    digital_info = digital_human_basic_setting(
        command_words = json.dumps(dir, ensure_ascii=False),
        awaken_greeting = messageBody.get("awakenGreeting", None)
    )
    with app.app_context():
        try:
            if digital_human_basic_setting.query.get(1):
                obj = digital_human_basic_setting.query.get(1) 
                obj.command_words = json.dumps(dir, ensure_ascii=False)
                obj.awaken_greeting = messageBody.get("awakenGreeting")
            else:
                db.session.add(digital_info)
            db.session.commit()
            print("成功入库awa")
        except Exception as e:
            db.session.rollback()
            print(f"入库失败:{e}")


def update_sensitiveWords(messageBody, app):
    digital_info = digital_human_basic_setting(
        sensitive_words = json.dumps(messageBody.get('sensitiveWords', None), ensure_ascii=False)
    )
    with app.app_context():
        try:
            if digital_human_basic_setting.query.get(1):
                obj = digital_human_basic_setting.query.get(1) 
                obj.sensitive_words = json.dumps(messageBody.get("sensitiveWords"), ensure_ascii=False)
            else:
                db.session.add(digital_info)
            db.session.commit()
            print("成功入库sen")
        except Exception as e:
            db.session.rollback()
            print(f"入库失败:{e}")

from applications.digital_human.common.common_utils import update_wake_interval
def reply_audio(videos, app, messageBody):
    uploaded_video_url = upload_video(videos, app)
    bottom_videos_url = upload_bottom_video(messageBody.get("bottomLineList", None), app)
    with app.app_context():
        digital_info = digital_human_basic_setting(
            welcome_video_url = uploaded_video_url.get("welcomeVideoUrl",None),
            welcomeVideoDuration = messageBody.get("welcomeVideoDuration",None),
            welcome_greeting = messageBody.get("welcomeGreeting",None),
            sensitive_words_video_url = uploaded_video_url.get("sensitiveWordsVideoUrl",None),
            sensitiveWordsVideoDuration = messageBody.get("sensitiveWordsVideoDuration",None),
            sensitive_words_reply = messageBody.get("sensitiveWordsReply",None),
            # bottom_video_url = uploaded_video_url.get("bottomLineVideoUrl",None),
            # bottomVideoDuration = messageBody.get("bottomVideoDuration",None),
            welcomeGreetingInterval = messageBody.get("welcomeGreetingInterval",None),
            # bottom_line_reply = messageBody.get("bottomLineReply",None)
            bottom_line_list = str(bottom_videos_url)
        )
        try:
            update_wake_interval(messageBody.get("welcomeGreetingInterval",None))
            if digital_human_basic_setting.query.get(1):
                obj = digital_human_basic_setting.query.get(1) 
                
                # if uploaded_video_url.get("welcomeVideoUrl"):
                obj.welcome_video_url = uploaded_video_url.get("welcomeVideoUrl",None)
                obj.welcomeVideoDuration = messageBody.get("welcomeVideoDuration",None)
                obj.welcome_greeting = messageBody.get("welcomeGreeting",None)
                # if uploaded_video_url.get("sensitiveWordsVideoUrl"):
                obj.sensitive_words_video_url = uploaded_video_url.get("sensitiveWordsVideoUrl",None)
                obj.sensitiveWordsVideoDuration = messageBody.get("sensitiveWordsVideoDuration",None) 
                obj.sensitive_words_reply = messageBody.get("sensitiveWordsReply",None)
                # if uploaded_video_url.get("bottomLineVideoUrl"):
                # obj.bottom_video_url = uploaded_video_url.get("bottomLineVideoUrl",None)
                # obj.bottomVideoDuration = messageBody.get("bottomVideoDuration",None)
                # obj.bottom_line_reply = messageBody.get("bottomLineReply",None)
                obj.welcomeGreetingInterval = messageBody.get("welcomeGreetingInterval",None)
                obj.bottom_line_list = str(bottom_videos_url)
            else:
                db.session.add(digital_info)
            db.session.commit()
            print("视频地址已成功入库")
        except Exception as e:
            db.session.rollback()
            print(f"入库失败:{e}")

# 根据minio的地址下载视频，并入库
def main1(msg, app):
    mylog.info(f"{msg}---------------start------------")
    messageType = msg.get("messageType")
    if messageType == "commonReplyAudio":
        messageBody = msg.get("messageBody")
        videos = {}
        if messageBody.get("welcomeVideoUrl"):
            welcomeVideoUrl = messageBody.get("welcomeVideoUrl")
            welcomeVideoUrl = "http://" + app.config['MINIO_ADDRESS'] + welcomeVideoUrl
            videos["welcomeVideoUrl"] = welcomeVideoUrl
        if messageBody.get("sensitiveWordsVideoUrl"):
            sensitiveWordsVideoUrl = messageBody.get("sensitiveWordsVideoUrl")
            sensitiveWordsVideoUrl = "http://" + app.config['MINIO_ADDRESS'] + sensitiveWordsVideoUrl
            videos["sensitiveWordsVideoUrl"] = sensitiveWordsVideoUrl
        # if messageBody.get("bottomLineVideoUrl"):
        #     bottomLineVideoUrl = messageBody.get("bottomLineVideoUrl")
        #     bottomLineVideoUrl = "http://" + app.config['MINIO_ADDRESS'] + bottomLineVideoUrl
        #     videos["bottomLineVideoUrl"] = bottomLineVideoUrl
        reply_audio(videos,app,messageBody)

    if messageType == "awakenGreeting":
        messageBody = msg.get("messageBody")
        update_awakenGreeting(messageBody, app)

    if messageType == "sensitiveWords":
        messageBody = msg.get("messageBody")
        update_sensitiveWords(messageBody, app)

def update_keyInfoList(serviceTypekeywords, app):
    digital_info = digital_human_basic_setting(
        unattendedModeKeyInfoList = json.dumps(serviceTypekeywords.get('1', None), ensure_ascii=False)
    )
    with app.app_context():
        try:
            if digital_human_basic_setting.query.get(1):
                obj = digital_human_basic_setting.query.get(1) 
                obj.unattendedModeKeyInfoList = json.dumps(serviceTypekeywords.get('1', None), ensure_ascii=False)
            else:
                db.session.add(digital_info)
            db.session.commit()
        except Exception as e:
            db.session.rollback()

if __name__ == "__main__":
    main1()