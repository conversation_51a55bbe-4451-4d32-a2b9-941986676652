#!/usr/bin/env python3
"""
模拟registry.py的导入方式来测试修复
"""

import sys
import os
import importlib

def test_registry_style_import():
    """模拟registry.py的导入方式"""
    print("🔍 模拟registry.py的导入方式")
    print("=" * 50)
    
    try:
        # 模拟registry.py中的导入方式
        # 首先添加models目录到路径
        models_dir = os.path.join(os.getcwd(), 'models')
        if models_dir not in sys.path:
            sys.path.insert(0, models_dir)
        
        print(f"[DEBUG] 当前工作目录: {os.getcwd()}")
        print(f"[DEBUG] models目录: {models_dir}")
        print(f"[DEBUG] models目录存在: {os.path.exists(models_dir)}")
        
        # 检查detect_face_alarm目录
        face_alarm_dir = os.path.join(models_dir, 'detect_face_alarm')
        print(f"[DEBUG] detect_face_alarm目录: {face_alarm_dir}")
        print(f"[DEBUG] detect_face_alarm目录存在: {os.path.exists(face_alarm_dir)}")
        
        # 检查v2_0_0目录
        v2_0_0_dir = os.path.join(face_alarm_dir, 'v2_0_0')
        print(f"[DEBUG] v2_0_0目录: {v2_0_0_dir}")
        print(f"[DEBUG] v2_0_0目录存在: {os.path.exists(v2_0_0_dir)}")
        
        # 检查main.py文件
        main_file = os.path.join(v2_0_0_dir, 'main.py')
        print(f"[DEBUG] main.py文件: {main_file}")
        print(f"[DEBUG] main.py文件存在: {os.path.exists(main_file)}")
        
        # 尝试导入main模块
        version_modname = "detect_face_alarm.v2_0_0"
        print(f"[DEBUG] 尝试导入: {version_modname}.main")
        
        main_module = importlib.import_module(version_modname + ".main")
        print("✅ main模块导入成功")
        
        # 检查load函数
        if hasattr(main_module, 'load'):
            print("✅ load函数存在")
            
            # 尝试调用load函数（但不实际执行，因为可能需要模型文件）
            print("✅ 可以调用load函数")
        else:
            print("❌ load函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_import():
    """测试直接导入方式"""
    print("\n🔍 测试直接导入方式")
    print("=" * 50)
    
    try:
        # 直接添加detect_face_alarm/v2_0_0到路径
        v2_0_0_dir = os.path.join(os.getcwd(), 'detect_face_alarm', 'v2_0_0')
        if v2_0_0_dir not in sys.path:
            sys.path.insert(0, v2_0_0_dir)
        
        print(f"[DEBUG] 添加到sys.path: {v2_0_0_dir}")
        
        # 尝试导入utils.face_recognition
        from utils.face_recognition import DetectFaceAlarm
        print("✅ DetectFaceAlarm直接导入成功")
        
        # 尝试导入main
        import main
        print("✅ main模块直接导入成功")
        
        if hasattr(main, 'load'):
            print("✅ load函数存在")
        else:
            print("❌ load函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_file_structure():
    """检查文件结构"""
    print("\n🔍 检查文件结构")
    print("=" * 50)
    
    required_files = [
        'detect_face_alarm/v2_0_0/main.py',
        'detect_face_alarm/v2_0_0/utils/__init__.py',
        'detect_face_alarm/v2_0_0/utils/face_recognition.py',
        'detect_face_alarm/v2_0_0/utils/log.py',
        'detect_face_alarm/v2_0_0/utils/i18n.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🚀 测试registry.py风格的导入")
    print("=" * 60)
    
    tests = [
        ("文件结构检查", check_file_structure),
        ("直接导入测试", test_direct_import),
        ("registry风格导入测试", test_registry_style_import),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！app.py应该可以正常运行")
    else:
        print("⚠️ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
