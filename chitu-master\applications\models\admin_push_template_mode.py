import datetime
from applications.extensions import db

class TemplateMode(db.Model):
    __tablename__ = 'admin_template_mode'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='主键Id')
    template_type = db.Column(db.String(1), nullable=False, comment='模板类型 1：文本类型 2：图文类型')
    template_name = db.Column(db.String(16), nullable=False, comment='推送类型名称')
    

    def json(self):
        return {
            'id': self.id,
            'template_type': self.template_type,
            'template_name': self.template_name,
        }