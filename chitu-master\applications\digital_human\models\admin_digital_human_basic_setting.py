import datetime
from applications.extensions import db


class digital_human_basic_setting(db.Model):
    __tablename__ = 'admin_digital_human_basic_setting'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键')
    background_id = db.Column(db.Integer, nullable=True,primary_key=False, comment='背景ID')
    audio_id = db.Column(db.Integer, nullable=True,primary_key=False, comment='声音ID')
    speaker_id = db.Column(db.Integer, nullable=True,primary_key=False, comment='形象ID')
    welcome_greeting = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='欢迎语')
    sensitive_words_reply = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='敏感词回复话术')
    # bottom_line_reply = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='兜底回复话术')
    command_words = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='命令词')
    awaken_greeting = db.Column(db.CHAR(30), nullable=True,primary_key=False, comment='唤醒语')
    welcome_video_url = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='欢迎语视频地址')
    welcomeVideoDuration = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='欢迎语视频时长')
    sensitive_words_video_url = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='敏感词视频地址')
    sensitiveWordsVideoDuration = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='敏感词视频时长')
    # bottom_video_url = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='兜底视频地址')
    # bottomVideoDuration = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='兜底视频时长')
    welcomeGreetingInterval = db.Column(db.CHAR(128), nullable=True,primary_key=False, comment='欢迎语主动打招呼时间间隔')
    sensitive_words = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment="敏感词1,敏感词2,敏感词3")
    unattendedModeKeyInfoList = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment="无人值守关键词集合")
    bottom_line_list = db.Column(db.Text, nullable=True,primary_key=False, comment='兜底回复话术列表')

    def json(self):
        return {
            'id': self.id,
            'background_id': self.background_id,
            'audio_id': self.audio_id,
            'speaker_id': self.speaker_id,
            'welcome_greeting': self.welcome_greeting,
            'sensitive_words_reply': self.sensitive_words_reply,
            # 'bottom_line_reply': self.bottom_line_reply,
            'command_words': self.command_words,
            'awaken_greeting': self.awaken_greeting,
            'welcome_video_url': self.welcome_video_url,
            'welcomeVideoDuration': self.welcomeVideoDuration,
            'sensitive_words_video_url': self.sensitive_words_video_url,
            'sensitiveWordsVideoDuration': self.sensitiveWordsVideoDuration,
            # 'bottom_video_url': self.bottom_video_url,
            # 'bottomVideoDuration': self.bottomVideoDuration,
            'welcomeGreetingInterval': self.welcomeGreetingInterval,
            'sensitive_words': self.sensitive_words,
            "unattendedModeKeyInfoList": self.unattendedModeKeyInfoList,
            "bottom_line_list": self.bottom_line_list
        }