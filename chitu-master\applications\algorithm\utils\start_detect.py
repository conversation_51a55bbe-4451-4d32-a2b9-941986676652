import os
import cv2
import time
from threading import Event, Thread
import importlib
from concurrent.futures import as_completed

from applications.algorithm.utils.algo_utils import get_infer_name, models, alarm_img_path, alarm_interval
from applications.common.utils.push import handle_alarm_sync
from applications.models.admin_algorithm import Algorithm, Camera
from applications.extensions import db
from flask import current_app
from log import mylog

'''
单个摄像头的总控制线程
'''
class StartupDetect():
    def __init__(self, truelist, id, used_weight_id, app):
        super(StartupDetect, self).__init__()
        self.truelist = truelist
        self.camera = str(id)
        self.event = Event()
        self.is_detecting = False
        self.det = None
        self.app = app
        self.models = used_weight_id
    
    def Det(self):
        videoCap = self.truelist.trueL[self.camera]["videoCap"]
        videoCap.capture()
        if not videoCap.isOpened:
            mylog.info("camera id " + self.camera + " open camera failed.")
            with self.app.app_context():
                Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "0"})
                db.session.commit()
        else:
            mylog.info("camera id " + self.camera + " open camera success.")
            with self.app.app_context():
                Camera.query.filter_by(id=int(self.camera)).update({"camera_status": "1"})
                db.session.commit()
            videoCap.startDrawframe()
            
            if self.det is None:
                self.det = Detect(self.camera, self.truelist)
            
            while True:
                 
                if self.event.is_set():
                    break
                
                if not videoCap.q.empty() and videoCap.isOpened:
                    taskList = []
                    tmpFame = videoCap.q.get()
                    frame = tmpFame.frame
                    tm = tmpFame.tm
                    
                    for id in self.models:
                        task = self.truelist.trueL[self.camera]["threadPool"].submit(self.det.detect, id, frame, tm, self.app)
                        taskList.append(task)

                    for future in as_completed(taskList):
                        pass

    def startDet(self):
        if not self.is_detecting:
            self.event.clear()
            det_t = Thread(target=self.Det)
            self.event.clear()
            det_t.start()
            self.is_detecting = True
    
    def stopDet(self):
        if self.is_detecting:
            self.event.set()
            self.is_detecting = False
        
    def reStartDet(self, truelist, id, used_weight_id, app):
        self.truelist = truelist
        self.camera = str(id)
        self.models = used_weight_id
        self.app = app
        if self.is_detecting:
            self.stopDet()
        self.startDet()



class Detect:
    
    def __init__(self, camera, truelist):
        super(Detect, self).__init__()
        self.camera = camera
        self.truelist = truelist
        self.last_alarm_time = {}
        for item in self.truelist.trueL[self.camera]["alarmType"]:
            self.last_alarm_time[item] = -1
        
    # 模型检测
    def detect(self, id, frame, tm, app):
        img = frame.copy()
        modelname = get_infer_name(id)
        try:
            res = importlib.import_module(modelname).infer(img, models[id], self.truelist.trueL[self.camera]["alarmType"])
        except Exception as e:
            return {"msg": "推理代码出现错误，请联系售后处理"}

        for key, val in res.items():
            
            if not val[0]:
                cur_time = time.time()
                if self.last_alarm_time[key] == -1 or cur_time - self.last_alarm_time[key] > alarm_interval:
                    mylog.info(self.camera + " " + key + " 报警-------")
                    pic_path = os.path.join(alarm_img_path, "{}.jpg".format(time.time()))
                    cv2.imwrite(pic_path, val[1])
                    
                    alarm_data = {
                        'camera_id': self.camera,
                        'alarm_type': key,
                        'pic_url': pic_path,
                        'alarm_time': tm
                    }
                    handle_alarm_sync(alarm_data, app)
                    self.last_alarm_time[key] = cur_time
                else:
                    mylog.info(self.camera + " " + key + " 报警, 不上报 =========")       
                    # pass
            else:
                mylog.info(self.camera + " " + key + " safe")
                # pass
                