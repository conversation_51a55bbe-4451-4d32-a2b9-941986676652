import os
import json
import requests
import subprocess
import shutil
from threading import Lock
from log import mylog
from applications.extensions import db
from applications.mqtt.mqtt_connect import mqtt_client
from applications.digital_human.common.utils.interactive import main, delete_interactive, async_interactive
from applications.digital_human.common.utils.updatesetting import main1
from applications.digital_human.models import digital_human_interactive
from applications.digital_human.common.common_utils import (read_config, basic_settings_config_path, create_rsa_pair, read_public_key, read_version_info, DIGITAL_HUMAN_VERSION_PATH,
                                                            rsa_encryption, read_private_key, rsa_decryption, PROPAGANDA_PICS_DIR, SLEEPY_PICS_DIR)
from applications.digital_human.common.utils.qa_service import check_qa_service, start_qa_service
from applications.digital_human.common.utils.stranger_rec import reload_ATTENDANCE_MAX_WAIT_TIME
from applications.digital_human.view.utils import ws_send_algo_msg, get_ws_client_keys, ws_send_screen_msg

qapath = "applications/qa-system/python-api-examples"

update_config_msg = {
    "messageType": "replyStatus",
    "configurationModificationCode": "1",
    "configurationModificationMessage": "配置修改"
}

def update_exit_attendance_duration(duration):
    conf = read_config()
    conf["exitAttendanceDuration"] = int(duration)
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)
    reload_ATTENDANCE_MAX_WAIT_TIME(int(duration))

def get_minio_url(url, app):
    return "http://" + app.config['MINIO_ADDRESS'] + url

# 下载，入库，返回成功列表和失败列表
def recv_requestQuestionVideo(client, msg, SN, respond_topic, app):
    try:
        result = main(msg, app)
        success_result = result[0]
        error_result = result[1]
        res_code = 200
        res_msg = "成功"
        ws_send_algo_msg(update_config_msg)
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"

    plainText = {}
    plainText["code"] = res_code
    plainText["data"] = {
        "messageType": "requestQuestionVideo",
        "messageBody": {
            "serialNumber": SN,
            "successList": success_result,
            "errorList": error_result
        }
    }
    plainText["msg"] = res_msg
    mqtt_client.publish(respond_topic, plainText)
    mylog.info(f'问答发布消息成功返回："successList":{success_result},"errorList":{error_result}')

# 更新问答库的问题状态，相当于修改数据库内容
def recv_requestQuestionStatus(client, msg, SN, respond_topic,app):

    success_result = []
    error_result = []
    enabled_result = []
    try:
        if msg.get("messageType") == "requestQuestionStatus":
            messageBody = msg.get("messageBody")
            questionId = messageBody.get("questionId")
            enabled = messageBody.get("enabled")
            enabled_result.append(enabled)
            for i in questionId:
                with app.app_context():
                    try:
                        if digital_human_interactive.query.filter_by(questionId=i).first():
                            obj = digital_human_interactive.query.filter_by(questionId=i).first()
                            obj.enabled = enabled
                            db.session.commit()
                            success_result.append(i)
                    except Exception as e:
                        db.session.rollback()
                        print(f"入库失败: {e}")
                        dic = {
                            "questionId": i,
                            "message": e
                        }
                        error_result.append(dic)   
        res_code = 200
        res_msg = "成功"
        ws_send_algo_msg(update_config_msg)
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
    plainText = {}
    plainText["code"] = res_code
    plainText["data"] = {
        "messageType": "requestQuestionStatus",
        "messageBody": {
            "enabled": enabled_result[0],
            "serialNumber": SN,
            "successList": success_result,
            "errorList": error_result
        }
    }
    plainText["msg"] = res_msg
    mqtt_client.publish(respond_topic, plainText)

# 下载，入库
def recv_commonReplyAudio(client, msg, SN, respond_topic, app):
    try:
        main1(msg, app)
        res_code = 200
        res_msg = "成功"
        ws_send_algo_msg(update_config_msg)
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"

    plainText = {}
    plainText["code"] = res_code
    plainText["data"] = {
        "messageType": "commonReplyAudio",
        "messageBody": SN
    }
    plainText["msg"] = res_msg
    mqtt_client.publish(respond_topic, plainText)

# 下载视频，入库
def recv_awakenGreeting(client, msg, SN, respond_topic, app):
    try:
        main1(msg, app)
        update_exit_attendance_duration(msg["messageBody"]["exitAttendanceDuration"])
        ws_send_algo_msg(update_config_msg)
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"

    plainText = {}
    plainText["code"] = res_code
    plainText["data"] = {
        "messageType": "awakenGreeting",
        "messageBody": SN
    }
    plainText["msg"] = res_msg
    mqtt_client.publish(respond_topic, plainText)
    
# 下载视频，入库
def recv_sensitiveWords(client, msg, SN, respond_topic, app):
    try:
        main1(msg, app)
        ws_send_algo_msg(update_config_msg)
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
    plainText = {}
    plainText["code"] = res_code
    plainText["data"] = {
        "messageType": "sensitiveWords",
        "messageBody": SN
    }
    plainText["msg"] = res_msg
    mqtt_client.publish(respond_topic, plainText)

# 接收背景或者形象，下载并将结果上报至平台端
def recv_backGround_or_speaker(client, msg, SN, respond_topic, save_path, app):
    messageType = msg["messageType"]
    url = get_minio_url(msg["messageBody"], app)
    try:
        frame = requests.get(url)
        with open(save_path, mode = "wb") as f:
            f.write(frame.content)
        
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(str(e))
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    
# 相关信息存储在"/mnt/keep/workspace/digital_human_data/basicSettings/config.json"，修改json文件
def recv_intersection(client, msg, SN, respond_topic):
    try:
        interactionVal = msg["messageBody"]["interactionVal"]
        conf = read_config()
        conf["interactionVal"] = interactionVal
        conf["interactionDefault"] = "1"
        with open(basic_settings_config_path, 'w') as write_f:
            json.dump(conf, write_f, indent=4, ensure_ascii=False)
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    
# 相关信息存储在"/mnt/keep/workspace/digital_human_data/basicSettings/config.json"，修改json文件
def recv_question_guidance(client, msg, SN, respond_topic):
    try:
        question = msg["messageBody"]["question"]
        conf = read_config()
        conf["questionGuide"] = question
        with open(basic_settings_config_path, 'w') as write_f:
            json.dump(conf, write_f, indent=4, ensure_ascii=False)
        res_code = 200
        res_msg = "成功"
    except:
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)

# 这里的有效期采用RSA加密，同样存储在配置文件中（需要考虑用户续费的情况）
def recv_service_period(client, msg, SN, respond_topic):
    try:
        servicePeriod = msg["messageBody"]
        conf = read_config()
        end_date = rsa_decryption(conf["servicePeriod"], read_private_key())
        if end_date != servicePeriod:
            create_rsa_pair()
            conf["servicePeriod"] = rsa_encryption(servicePeriod, read_public_key())
            with open(basic_settings_config_path, 'w') as write_f:
                json.dump(conf, write_f, indent=4, ensure_ascii=False)
            
            # 失效续费情况
            if "algorithm" not in get_ws_client_keys():
                need_msg = start_qa_service()
                if need_msg:
                    warnMsg = {
                        "messageType": "messageToast",
                        "messageBody": {
                            "mode": "7",
                            "code": 300,
                            "text": "服务正在启动..."
                        }
                    }
                    ws_send_screen_msg(json.dumps(warnMsg))
        
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    
from applications.digital_human.common.utils.mode_change import mode_dict
from applications.digital_human.view.sentryModeControl import update_sentry_schedule
from applications.digital_human.common.utils.updatesetting import update_keyInfoList
# 修改json配置文件
def recv_service_mode_control(client, msg, SN, respond_topic, app):
    try:
        conf = read_config()
        old_info = [conf.get("sentryModeStatus", "0"), conf.get("sentryModeStartTime"), conf.get("sentryModeStopTime")]
        new_info = [msg["messageBody"].get("sentryModeStatus"), msg["messageBody"].get("modeStartTime"), msg["messageBody"].get("modeEndTime")]
        if new_info != old_info:
            conf["sentryModeStatus"] = msg["messageBody"]["sentryModeStatus"]
            conf["sentryModeStartTime"] = msg["messageBody"]["modeStartTime"]
            conf["sentryModeStopTime"] = msg["messageBody"]["modeEndTime"]
            conf["nextServiceMode"] = mode_dict[msg["messageBody"]["nextServiceModeType"]]
            with open(basic_settings_config_path, 'w') as write_f:
                json.dump(conf, write_f, indent=4, ensure_ascii=False) 
            update_sentry_schedule(app)
            
        # 修改关键字
        keyword_list = {}
        for item in msg["messageBody"]["serviceModeKeyList"]:
            keyword_list[item["serviceModeTypeKey"]] = item["keyInfoList"]
        update_keyInfoList(keyword_list, app)
        
        # 如果算法未启动，则启动算法服务
        if "algorithm" not in get_ws_client_keys():
            need_msg = start_qa_service()
            warnMsg = {
                "messageType": "messageToast",
                "messageBody": {
                    "mode": "7",
                    "code": 300,
                    "text": "服务正在启动..."
                }
            }
            ws_send_screen_msg(json.dumps(warnMsg))
        
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": {
            "serviceModeType": msg["messageBody"]["serviceModeType"],
            "serialNumber": SN
        }
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)


def recv_robot_message_return(res_code, res_msg, SN, respond_topic, msg):
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": {
            "robotMessageId": msg["messageBody"]["robotMessageId"],
            "serialNumber": SN
        }
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    
def recv_minio_images(client, msg, SN, respond_topic, app):
    try:
        if msg["messageBody"]["publicityType"] == "0":
            target_dir = PROPAGANDA_PICS_DIR
        elif msg["messageBody"]["publicityType"] == "1":
            target_dir = SLEEPY_PICS_DIR
        else:
            pass
        
        if len(os.listdir()) > 0:
            shutil.rmtree(target_dir)
            os.mkdir(target_dir)
        for url in msg["messageBody"]["publicityUrlList"]:
            frame = requests.get(get_minio_url(url, app))
            with open(os.path.join(target_dir, url.split('/')[-1]), mode = "wb") as f:
                f.write(frame.content)
        res_code = 200
        res_msg = "成功"
    except:
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": {
            "serialNumber": SN,
            "publicityType": msg["messageBody"]["publicityType"]
        }
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)

from applications.digital_human.view.back_up import backup
import time
def recv_system_update(client, msg, SN, respond_topic, app):
    try:
        version_info = read_version_info()
        version = msg["messageBody"]["digitalHumanVersion"]
        url = msg["messageBody"]["packageUrl"]
        
        version_info["newVersion"]["version"] = version
        version_info["newVersion"]["downloadUrl"] = get_minio_url(url, app)
        version_info["newVersion"]["md5"] = msg["messageBody"]["md5"]
        version_info["newVersion"]["createTime"] = time.time()
        with open(DIGITAL_HUMAN_VERSION_PATH, 'w') as write_f:
            json.dump(version_info, write_f, indent=4, ensure_ascii=False)
        
        backup()
        update_info = {
            "messageType": "devOpsMessage",
            "messageBody": "version"
        }
        ws_send_screen_msg(json.dumps(update_info))
        
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
      
def recv_sync_question_answer(client, msg, SN, respond_topic, app):
    try:
        res_code, res_msg, returnids1, returnids2 = async_interactive(msg["messageBody"]["releasedIds"], msg["messageBody"]["releaseingIds"], msg["messageBody"]["deletedIds"], app)
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        returnids1 = msg["messageBody"]["deletedIds"]
        
    respond_info = {}
    respond_info["code"] = res_code
    if res_code == 200:
        messagebody = {
            "serialNumber": SN,
            "toReleaseIds": returnids1,
            "noExistReleaseIds": returnids2
        }
    else:
        messagebody = {
            "serialNumber": SN,
            "releasedIds": [],
            "releaseingIds": [],
            "deletedIds": returnids1
        }
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": messagebody
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    ws_send_algo_msg(update_config_msg)

def recv_delete_question_answer(client, msg, SN, respond_topic, app):
    try:
        deletedIds = msg["messageBody"]["deletedIds"]
        res_code, res_msg, returnIds = delete_interactive(deletedIds, app)
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
        returnIds = deletedIds
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": {
            "serialNumber": SN,
            "deletedIds": returnIds
        }
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)
    ws_send_algo_msg(update_config_msg)
    
    
def recv_screen_info(client, msg, SN, respond_topic):
    try:
        direction = msg["messageBody"]["screenDirection"]
        conf = read_config()
        conf["screenDirection"] = direction
        with open(basic_settings_config_path, 'w') as write_f:
            json.dump(conf, write_f, indent=4, ensure_ascii=False)
        res_code = 200
        res_msg = "成功"
    except:
        res_code = 500
        res_msg = "失败"
        
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    mqtt_client.publish(respond_topic, respond_info)