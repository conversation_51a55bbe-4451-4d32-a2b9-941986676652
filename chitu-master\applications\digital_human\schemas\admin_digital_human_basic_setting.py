from applications.extensions import ma
from marshmallow import fields


class digital_human_basic_settingOutSchema(ma.<PERSON>):
    id = fields.Integer()
    background_id = fields.Str()
    audio_id = fields.Str()
    speaker_id = fields.Str()
    welcome_greeting = fields.Str()
    sensitive_words_reply = fields.Str()
    # bottom_line_reply = fields.Str()
    # mute_it_greeting = fields.Str()
    command_words = fields.Str()
    # unmute_it_greeting = fields.Str()
    awaken_greeting = fields.Str()
    welcome_video_url = fields.Str()
    sensitive_words_video_url = fields.Str()
    # bottom_video_url = fields.Str()
    dialogue_record_status = fields.Str()
    audio_interaction_status = fields.Str()
    question_guide_status = fields.Str()
    question = fields.Str()
    sensitive_words = fields.Str()
    bottom_line_list = fields.Str()