name: apk-speaker-identification

on:
  push:
    tags:
      - '*'

  workflow_dispatch:

concurrency:
  group: apk-speaker-identification-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  apk_speaker_identification:
    if: github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa'
    runs-on: ${{ matrix.os }}
    name: apk for tts ${{ matrix.index }}/${{ matrix.total }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        total: ["10"]
        index: ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # https://github.com/actions/setup-java
      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin' # See 'Supported distributions' for available options
          java-version: '21'

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-android

      - name: Display NDK HOME
        shell: bash
        run: |
          echo "ANDROID_NDK_LATEST_HOME: ${ANDROID_NDK_LATEST_HOME}"
          ls -lh ${ANDROID_NDK_LATEST_HOME}

      - name: Install Python dependencies
        shell: bash
        run: |
          python3 -m pip install --upgrade pip jinja2

      - name: Generate build script
        shell: bash
        run: |
          cd scripts/apk

          total=${{ matrix.total }}
          index=${{ matrix.index }}

          ./generate-speaker-identification-apk-script.py --total $total --index $index

          chmod +x build-apk-speaker-identification.sh
          mv -v ./build-apk-speaker-identification.sh ../..

      - name: build APK
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          export ANDROID_NDK=$ANDROID_NDK_LATEST_HOME
          ./build-apk-speaker-identification.sh

      - name: Display APK
        shell: bash
        run: |
          ls -lh ./apks/
          du -h -d1 .

      - name: Publish to huggingface
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/csukuangfj/sherpa-onnx-apk huggingface
            cd huggingface
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            mkdir -p speaker-identification
            cp -v ../apks/*.apk ./speaker-identification/
            git status
            git lfs track "*.apk"
            git add .
            git commit -m "add more apks"
            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-apk main
