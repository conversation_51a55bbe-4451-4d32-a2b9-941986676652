from log import mylog
from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.orm import sessionmaker

# # 设置源数据库和目标数据库的连接字符串
# source_db_url = 'sqlite:///test.db'
# target_db_url = 'sqlite:///testa.db'

def database_copy(target_db_url, source_db_url):
    try:
        # 创建数据库引擎
        source_engine = create_engine("sqlite:///" + source_db_url)
        target_engine = create_engine("sqlite:///" + target_db_url)
        
        # 创建元数据对象
        source_metadata = MetaData()
        target_metadata = MetaData()
        
        # 反射源数据库中的表
        source_metadata.reflect(bind=source_engine)
        target_metadata.reflect(bind=target_engine)

        # 获取源数据库的所有表
        tables = source_metadata.tables.values()
        tables2 = source_metadata.tables.values()

        # 打开源数据库和目标数据库的会话
        Session = sessionmaker(bind=source_engine)
        source_session = Session()

        Session = sessionmaker(bind=target_engine)
        target_session = Session()

        # 将数据从源数据库复制到目标数据库
        for table in tables:
            source_table = source_metadata.tables[table.name]
            target_table = target_metadata.tables[table.name]

            # 获取源表的所有数据
            rows = source_session.execute(source_table.select()).fetchall()
            rows2 = target_session.execute(target_table.select()).fetchall()

            # 将数据插入目标表
            if len(rows2) == 0:
                for row in rows:
                    target_session.execute(target_table.insert().values(row))

            # 提交每个表的数据
            target_session.commit()

        # 关闭会话
        source_session.close()
        target_session.close()
        return True
    
    except Exception as e:
        mylog.info(f"系统更新：迁移数据库时发生错误{e}")
        return False