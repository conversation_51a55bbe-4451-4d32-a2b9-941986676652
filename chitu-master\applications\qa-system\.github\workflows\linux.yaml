name: linux

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/linux.yaml'
      - '.github/scripts/test-kws.sh'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
      - 'c-api-examples/**'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/linux.yaml'
      - '.github/scripts/test-kws.sh'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'

  workflow_dispatch:

concurrency:
  group: linux-${{ github.ref }}
  cancel-in-progress: true

jobs:
  linux:
    name: ${{ matrix.build_type }} shared-${{ matrix.shared_lib }} tts-${{ matrix.with_tts }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        build_type: [Release, Debug]
        shared_lib: [ON, OFF]
        with_tts: [ON, OFF]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Display PWD
        shell: bash
        run: |
          echo "pwd: $PWD"
          ls -lh

      - name: Build sherpa-onnx
        uses: addnab/docker-run-action@v3
        with:
            image: quay.io/pypa/manylinux2014_x86_64
            options: |
              --volume ${{ github.workspace }}/:/home/<USER>/work/sherpa-onnx/sherpa-onnx
            shell: bash
            run: |
              uname -a
              gcc --version
              cmake --version
              cat /etc/*release
              id
              pwd

              cd /home/<USER>/work/sherpa-onnx/sherpa-onnx

              git clone --depth 1 https://github.com/alsa-project/alsa-lib
              pushd alsa-lib
              ./gitcompile
              popd

              export CPLUS_INCLUDE_PATH=$PWD/alsa-lib/include:$CPLUS_INCLUDE_PATH
              export SHERPA_ONNX_ALSA_LIB_DIR=$PWD/alsa-lib/src/.libs

              mkdir build
              cd build

              cmake -DSHERPA_ONNX_ENABLE_TTS=${{ matrix.with_tts }} -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} -D BUILD_SHARED_LIBS=${{ matrix.shared_lib }} -DCMAKE_INSTALL_PREFIX=./install ..

              make -j2
              make install

              ls -lh lib
              ls -lh bin

      - name: Display dependencies of sherpa-onnx for linux
        shell: bash
        run: |
          sudo chown -R $USER ./build
          ls -lh build/bin
          ls -lh build/_deps/onnxruntime-src/lib/

          echo "strip"
          strip build/bin/*
          echo "after strip"
          ls -lh build/bin

          file build/bin/sherpa-onnx
          file build/bin/sherpa-onnx
          ls -lh build/bin/sherpa-onnx
          readelf -d build/bin/sherpa-onnx

      - uses: actions/upload-artifact@v4
        with:
          name: release-${{ matrix.build_type }}-with-shared-lib-${{ matrix.shared_lib }}-with-tts-${{ matrix.with_tts }}
          path: build/bin/*

      - name: Test online CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-ctc.sh

      - name: Test C API
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export SLID_EXE=spoken-language-identification-c-api
          export SID_EXE=speaker-identification-c-api

          .github/scripts/test-c-api.sh

      - name: Test spoken language identification (C++ API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline-language-identification

          .github/scripts/test-spoken-language-identification.sh

      - name: Test transducer kws
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-keyword-spotter

          .github/scripts/test-kws.sh


      - name: Test offline Whisper
        if: matrix.build_type != 'Debug'
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          readelf -d build/bin/sherpa-onnx-offline

          .github/scripts/test-offline-whisper.sh

      - name: Test offline CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          .github/scripts/test-offline-ctc.sh

      - name: Test offline TTS
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline-tts

          .github/scripts/test-offline-tts.sh

      - name: Test online paraformer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-paraformer.sh

      - name: Test offline transducer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          .github/scripts/test-offline-transducer.sh

      - name: Test online transducer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-transducer.sh

      - name: Test online transducer (C API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=decode-file-c-api

          .github/scripts/test-online-transducer.sh

      - name: Copy files
        shell: bash
        if: matrix.build_type == 'Release'
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          if [[ ${{ matrix.shared_lib }} == 'ON' ]]; then
            suffix=shared
          else
            suffix=static
          fi

          if [[ ${{ matrix.with_tts }} ]]; then
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-x64-$suffix
          else
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-x64-$suffix-no-tts
          fi
          mkdir $dst

          cp -a build/install/bin $dst/
          cp -a build/install/lib $dst/
          cp -a build/install/include $dst/

          tree $dst

          tar cjvf ${dst}.tar.bz2 $dst

      - name: Publish to huggingface
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && (github.event_name == 'push' || github.event_name == 'workflow_dispatch') && matrix.build_type == 'Release'
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-libs huggingface

            cd huggingface
            git lfs pull
            mkdir -p linux-x64

            cp -v ../sherpa-onnx-*.tar.bz2 ./linux-x64

            git status
            git lfs track "*.bz2"

            git add .

            git commit -m "upload sherpa-onnx-${SHERPA_ONNX_VERSION}"

            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-libs main

      - name: Release pre-compiled binaries and libs for linux x64
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/') && matrix.build_type == 'Release'
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*.tar.bz2

      - uses: actions/upload-artifact@v4
        with:
          name: tts-generated-test-files-${{ matrix.build_type }}-${{ matrix.shared_lib }}-with-tts-${{ matrix.with_tts }}
          path: tts

