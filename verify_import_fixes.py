#!/usr/bin/env python3
"""
验证导入修复的脚本
"""

import os
from pathlib import Path

def check_file_imports(file_path, expected_fixes):
    """检查文件的导入修复"""
    if not Path(file_path).exists():
        return False, f"文件不存在: {file_path}"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_fixes = []
        for fix in expected_fixes:
            if fix not in content:
                missing_fixes.append(fix)
        
        if missing_fixes:
            return False, f"缺失修复: {missing_fixes}"
        else:
            return True, "所有修复都存在"
            
    except Exception as e:
        return False, f"读取文件失败: {e}"

def main():
    """主验证函数"""
    print("🔍 验证导入修复")
    print("=" * 50)
    
    # 需要检查的文件和期望的修复内容
    files_to_check = {
        'detect_face_alarm/v2_0_0/utils/face_recognition.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog',
            'from inference.model_check import ModelCheck'
        ],
        'detect_face_alarm/v2_0_0/preprocess/detect_preprocess.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog',
            'from preprocess.utils.preprocess import PreProcess'
        ],
        'detect_face_alarm/v2_0_0/inference/inference_rknn/inference_rknn.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog',
            'from inference.inference_rknn.utils_rknn.utils import'
        ],
        'detect_face_alarm/v2_0_0/postprocess/post_process.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog',
            'from postprocess.utils.boxes_iou import polygon_IOU'
        ],
        'detect_face_alarm/v2_0_0/inference/model_check.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog'
        ],
        'detect_face_alarm/v2_0_0/task/http_post.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog'
        ],
        'detect_face_alarm/v2_0_0/utils/frame_flow.py': [
            'sys.path.insert(0, parent_dir)',
            'from utils.log import mylog',
            'from preprocess.rtsp_camera import RtspCamera'
        ]
    }
    
    all_passed = True
    
    for file_path, expected_fixes in files_to_check.items():
        print(f"\n检查文件: {file_path}")
        passed, message = check_file_imports(file_path, expected_fixes)
        
        if passed:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            all_passed = False
    
    print("\n" + "=" * 50)
    print("验证总结")
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有导入修复验证通过！")
        print("\n📋 修复完成的内容:")
        print("  ✅ 所有相对导入已改为绝对导入")
        print("  ✅ 所有文件都添加了路径修复代码")
        print("  ✅ 导入语句格式正确")
        
        print("\n🚀 现在可以:")
        print("  1. 尝试运行算法")
        print("  2. 进行实际测试")
        print("  3. 验证功能正常")
    else:
        print("⚠️ 部分导入修复验证失败")
        print("需要检查上述失败的文件")

if __name__ == "__main__":
    main()
