import time

Width = 640
Height = 480
line_p1 = [0, Height * 0.8]
line_p2 = [640, Height * 0.8]
def is_get_in(p1, p2):
    
    a = (line_p1[1] - line_p2[1]) / (line_p1[0] - line_p2[0])
    b = line_p1[1] - a * line_p1[0]
    mid_xy = ((line_p1[0] + line_p2[0]) / 2, (line_p1[1] + line_p2[1]) / 2)

    if abs(a) > 1:
        if mid_xy[0] < Width / 2:
            if p1[0] < (p1[1] - b) / a and p2[0] > (p2[1] - b) / a:
                return 'in'
            else:
                return 'out'
        else:
            if p1[0] < (p1[1] - b) / a and p2[0] > (p2[1] - b) / a:
                return 'out'
            else:
                return 'in'
    else:
        if mid_xy[1] < Height / 2:
            if p1[1] > a * p1[0] + b and p2[1] < a * p2[0] + b:
                return 'out'
            else:
                return 'in'
        else:
            if p1[1] > a * p1[0] + b and p2[1] < a * p2[0] + b:
                return 'out'
            else:
                return 'in'


class peopleTrackRecord: 
    def __init__(self, recog_interval=5, alert_interval=60):
        self.recog_interval = recog_interval
        self.alert_interval = alert_interval
        self.last_alert_time = None
        self.people_data = {}
        # self.track_id_list = []
        
    def add_track_record(self, track_id, cur_point, box, person_type = "Unknown"):
        if track_id not in self.people_data.keys():
            self.people_data[track_id] = {"person_type": person_type, "start_time": time.time(), "last_point": [-1, -1], "current_point": cur_point, "box": box}
        else:
            self.people_data[track_id]["last_point"] = self.people_data[track_id]["current_point"]
            self.people_data[track_id]["current_point"] = cur_point
            self.people_data[track_id]["box"] = box
            if person_type != "Unknown" and self.people_data[track_id]["person_type"] == "Unknown":
                self.people_data[track_id]["person_type"] = person_type

    def trigger_alert(self, track_ids):
        if self.last_alert_time is None or time.time() - self.last_alert_time > self.alert_interval:
            for id in track_ids:
                # 陌生人逗留超过5秒
                if time.time() - self.people_data[id]["start_time"] > self.recog_interval and self.people_data[id]["person_type"] == "Unknown":
                    box = self.people_data[id]["box"]
                    del self.people_data[id]
                    self.last_alert_time = time.time()
                    return [True, box]
                
                # 陌生人由外进入
                if self.people_data[id]["last_point"] != [-1, -1] and self.people_data[id]["person_type"] == "Unknown":
                    res = is_get_in(self.people_data[id]["last_point"], self.people_data[id]["current_point"])
                    box = self.people_data[id]["box"]
                    del self.people_data[id]
                    if res == "in":
                        self.last_alert_time = time.time()
                        return [True, box]
        return [False, None]
    
people_track = peopleTrackRecord()