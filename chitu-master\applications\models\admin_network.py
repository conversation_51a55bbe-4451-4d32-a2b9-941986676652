
from applications.extensions import db


class NetworkConfig(db.Model):
    __tablename__ = 'admin_network_config'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键')
    lan_id = db.Column(db.BigInteger,nullable=False)
    lan_name = db.Column(db.String(32), nullable=False)
    gateway = db.Column(db.String(15))
    ip = db.Column(db.String(15))
    subnet_mask = db.Column(db.String(15), nullable=True)  # 仅在高级配置中使用

    def json(self):
        return {
            'id': self.id,
            'lan_id': self.lan_id,
            'lan_name': self.lan_name,
            'gateway': self.gateway,
            'ip': self.ip,
            'subnet_mask': self.subnet_mask
        }
