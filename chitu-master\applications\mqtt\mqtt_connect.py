from flask import current_app
import paho.mqtt.client as mqtt
import json
from threading import Thread

from applications.mqtt.utils import (send_regist_info, send_device_usage, send_device_usage_voluntarily, encrypt_msg, get_SN, 
                                     publish_mqtt_msg, NEED_SUBSCRIBE_TOPICS, RE_REGIST_INTERVAL, get_digital_human_version)

from applications.common.value_dispatch import value_dispatch
from log import mylog
import time

false = False
true = True
null = ''

SN = get_SN()

@value_dispatch
def dispatch_tasks(topic):
    mylog.info("MQTT: Not subscribed to this topic: {}.".format(topic))

# 注册回复主题
@dispatch_tasks.register("deviceRegisterResponse")
def deal_topic_regist_res(*args):
    topic, msg_content, client, _ = args
    if msg_content["result"] == "true":
        # 如果注册成功，则发送版本信息，同时启动线程每个一段时间发送资源占用信息
        mylog.info("MQTT: 注册成功。")
        version_info = {
            "messageType": "packageVersion",
            "messageBody": {
                "digitalHumanVersion": get_digital_human_version(),
                "serialNumber": SN
            }
        }
        res = publish_mqtt_msg(client, "digitalHumanTopic", json.dumps(version_info))
        if res == 0:
            mylog.info("MQTT: 发送注册信息成功。")
        s_d = Thread(target=send_device_usage_voluntarily, args=(client, SN))
        s_d.start()
    else:
        # 如果注册失败，则记录日志，并在一段时间后重新发起注册
        mylog.info("MQTT: Regist failed. And re-regist later.")
        time.sleep(RE_REGIST_INTERVAL)
        send_regist_info(client, SN)

# 请求盒子资源占用情况主题，直接发送资源占用信息即可      
@dispatch_tasks.register("RequestEdgeBoxResource")
def deal_topic_device_usage_request(*args):
    topic, msg_content, client, _ = args
    send_device_usage(client, SN)

# 数字员工消息接收主题
@dispatch_tasks.register("digitalAvatarTopic")
def deal_topic_digital_avatar_request(*args):
    topic, msg_content, client, app = args
    try:
        from applications.digital_human.view.distribute_tasks import digital_human_dispatch_tasks
        # 这里根本不同的messageType做不同的处理，同时使用线程处理，避免因错误造成所有功能不可用
        task_th = Thread(target=digital_human_dispatch_tasks, args=(msg_content["messageType"], client, msg_content, SN, app))
        task_th.start()
        # digital_human_dispatch_tasks(msg_content["messageType"], client, msg_content, SN, app)
    except ModuleNotFoundError as e:
        mylog.info(e)
        mylog.info("MQTT: No digital human module.")


message_queue = []
def add_message_to_queue(topic, msg):
    message_queue.append((topic, msg))


def send_cached_messages():
    for topic, msg in message_queue:
        mqtt_client.publish(topic, msg)
    message_queue.clear()


class mqttClient():
    def __init__(self, app):
        self.app = app
        self.connect_success = False
        
        self.client = mqtt.Client()
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message_with_extra_param(app)
        self.client.on_disconnect = self.on_disconnect
        self.client.username_pw_set(encrypt_msg(self.app.config['MQTT_USERNAME']), encrypt_msg(self.app.config['MQTT_PASSWORD']))


    def connect(self):
        while True:
            try:
                self.client.connect(self.app.config['MQTT_BROKER'], self.app.config['MQTT_PORT'], self.app.config['MQTT_HEARTBEAT_INTERVAL'])
                break
            except ConnectionRefusedError:
                mylog.info("MQTT: 客户端连接失败，未找到MQTT服务。")
                time.sleep(self.app.config['MQTT_RECONNECT_INTERVAL'])
            except:
                mylog.info("MQTT: 客户端连接失败，稍后重试。")
                time.sleep(self.app.config['MQTT_RECONNECT_INTERVAL'])
        
        for topic in NEED_SUBSCRIBE_TOPICS:
            self.client.subscribe(topic, 2)
            
        self.connect_success = True
        self.client.loop_start()
    
    
    def publish(self, topic, msg):
        if self.connect_success:
            res = publish_mqtt_msg(self.client, topic, msg)
            if res == 0:
                mylog.info("MQTT：消息发送成功")
            else:
                mylog.info("MQTT：消息发送失败")
        else:
            mylog.info("MQTT: 连接失败，无法发送消息。")
            add_message_to_queue(topic, msg)
    
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            mylog.info("MQTT: MQTT客户端连接成功。")
            send_regist_info(self.client, SN)
            send_cached_messages()
        elif rc == 4:
            mylog.info("MQTT: 客户端连接失败，失败原因为：身份验证失败。")
        else:
            mylog.info("MQTT: 客户端连接失败，错误码为：" + str(rc))

    def on_disconnect(self, client, userdata, rc):
        mylog.info("MQTT: 客户端断开连接，即将尝试重连...")
        self.connect_success = False
        if rc != 0:
            while True:
                try:
                    self.client.reconnect()
                    self.connect_success = True
                    self.client.loop_start()
                    break
                except:
                    pass

    def on_message_with_extra_param(self, app):
        def on_message(client, userdata, msg):
            msg_receive = json.loads(msg.payload)
            mylog.info("MQTT: 接收到服务端消息: '主题': {}, '消息内容：': {}".format(msg.topic, str(msg_receive)))
            # 消息解密校验
            if encrypt_msg(msg_receive["plainText"]) == msg_receive["cipherText"]:
                msg_content = eval(msg_receive["plainText"])
                # 根据不同的主题，做不同的消息处理
                dispatch_tasks(msg.topic, msg_content, client, app)
            else:
                mylog.info("MQTT: 消息不合法。")
        return on_message

app = current_app._get_current_object()
mqtt_client = mqttClient(app)
mqtt_connect_th = Thread(target=mqtt_client.connect)
mqtt_connect_th.start()