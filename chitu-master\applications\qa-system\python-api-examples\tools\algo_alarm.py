#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: algo_alarm.py

Description: This is an check model path Python script.

Author: songzhimeng
Creation Date: September 2, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""


import requests
import json

def send_alarm_message(message):
    webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/7f34b7c5-7bc4-43bf-bfad-50bbd868b23b'
    headers = {'Content-Type': 'application/json'}
    data = {
        "msg_type": "text",
        "content": {
            "text": message
        }
    }
    try:
        response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            return True
        else:
            return False
    except Exception as e:
        return False

