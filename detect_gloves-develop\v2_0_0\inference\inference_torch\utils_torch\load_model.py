import yaml
import torch.nn as nn
import torch
import numpy as np

def load_yaml(yaml_path, ):
    with open(yaml_path, errors='ignore') as f:
        names = yaml.safe_load(f)['names']
    return names

class DetectMultiBackend(nn.Module):
    def __init__(self, model, device, fp16=False):
        super().__init__()
        stride = 32
        stride = max(int(model.stride.max()), 32)  # model stride
        names = model.module.names if hasattr(model, 'module') else model.names  # get class names
        model.half() if fp16 else model.float()
        self.model = model  # explicitly assign for to(), cpu(), cuda(), half()
        self.__dict__.update(locals())  # assign all variables to self
    def forward(self, im, augment=False, visualize=False, val=False):
        # YOLOv5 MultiBackend inference
        # b, ch, h, w = im.shape  # batch, channel, height, width
        y = self.model(im, augment=augment, visualize=visualize)[0]
        if isinstance(y, np.ndarray):
            y = torch.tensor(y, device=self.device)
        return (y, []) if val else y

    def warmup(self, imgsz=(1, 3, 640, 640)):
        # Warmup model by running inference once
        if self.device.type != 'cpu':  # only warmup GPU models
            im = torch.zeros(*imgsz, dtype=torch.half if self.fp16 else torch.float, device=self.device)  # input
            for _ in range(1):  #
                self.forward(im)  # warmup
