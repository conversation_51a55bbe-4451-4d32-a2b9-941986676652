import cv2
import numpy as np
from rknnlite.api import RKNNLite
import faiss
import os
import pickle  # 用于保存和加载 id_name_map
from itertools import product
from math import ceil
from PIL import Image
import time
from log import mylog

from applications.digital_human.common.common_utils import WAKE_INTERVAL
from applications.digital_human.common.utils.kalman import Sort
from applications.digital_human.common.utils.track_record import people_track
from applications.digital_human.common.utils.inference_rknn import letterbox, predict, get_draw_box

np.seterr(divide='ignore',invalid='ignore')

# 配置文件
IMG_SIZE = 640
model_path = '/mnt/keep/workspace/digital_human_data/face_info/weights/retinaface_mob.rknn'
face_model_path = '/mnt/keep/workspace/digital_human_data/face_info/weights/face.rknn'
people_model_path = '/mnt/keep/workspace/digital_human_data/face_info/weights/people.rknn'


tracker = Sort()
cfg_mnet = {
    'min_sizes': [[16, 32], [64, 128], [256, 512]],
    'steps': [8, 16, 32],
    'variance': [0.1, 0.2],
}

class Anchors:
    def __init__(self, cfg, image_size=None):
        self.min_sizes = cfg['min_sizes']
        self.steps = cfg['steps']
        self.image_size = image_size
        self.feature_maps = [[ceil(self.image_size[0] / step), ceil(self.image_size[1] / step)] for step in self.steps]

    def get_anchors(self):
        anchors = []
        for k, f in enumerate(self.feature_maps):
            min_sizes = self.min_sizes[k]
            for i, j in product(range(f[0]), range(f[1])):
                for min_size in min_sizes:
                    s_kx = min_size / self.image_size[1]
                    s_ky = min_size / self.image_size[0]
                    dense_cx = [x * self.steps[k] / self.image_size[1] for x in [j + 0.5]]
                    dense_cy = [y * self.steps[k] / self.image_size[0] for y in [i + 0.5]]
                    for cy, cx in product(dense_cy, dense_cx):
                        anchors += [cx, cy, s_kx, s_ky]
        return np.array(anchors).reshape(-1, 4)


def letterbox_image(image, size):
    ih, iw, _ = np.shape(image)
    w, h = size
    scale = min(w/iw, h/ih)
    nw = int(iw * scale)
    nh = int(ih * scale)

    image = cv2.resize(image, (nw, nh))
    new_image = np.ones([size[1], size[0], 3]) * 128
    new_image[(h-nh)//2:nh+(h-nh)//2, (w-nw)//2:nw+(w-nw)//2] = image
    return new_image


def decode(loc, priors, variances):
    boxes = np.concatenate((priors[:, :2] + loc[:, :2] * variances[0] * priors[:, 2:], priors[:, 2:] * np.exp(loc[:, 2:] * variances[1])), 1)
    boxes[:, :2] -= boxes[:, 2:] / 2
    boxes[:, 2:] += boxes[:, :2]
    return boxes


def decode_landm(pre, priors, variances):
    landms = np.concatenate((priors[:, :2] + pre[:, :2] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 2:4] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 4:6] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 6:8] * variances[0] * priors[:, 2:],
                             priors[:, :2] + pre[:, 8:10] * variances[0] * priors[:, 2:],
                            ), 1)
    return landms


def pynms(dets, thresh):
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    areas = (y2 - y1) * (x2 - x1)
    scores = dets[:, 4]
    keep = []
    index = scores.argsort()[::-1]

    while index.size > 0:
        i = index[0]
        keep.append(i)
        x11 = np.maximum(x1[i], x1[index[1:]])
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])
        w = np.maximum(0, x22 - x11)
        h = np.maximum(0, y22 - y11)
        overlaps = w * h
        ious = overlaps / (areas[i] + areas[index[1:]] - overlaps)
        idx = np.where(ious <= thresh)[0]
        index = index[idx + 1]
    return keep


def filter_box(org_box, conf_thres, iou_thres):
    conf = org_box[..., 4] > conf_thres
    box = org_box[conf == True]
    output = []
    curr_cls_box = np.array(box)
    curr_cls_box[:, :4] = curr_cls_box[:, :4] * IMG_SIZE
    curr_cls_box[:, 5:] = curr_cls_box[:, 5:] * IMG_SIZE
    curr_out_box = pynms(curr_cls_box, iou_thres)
    for k in curr_out_box:
        output.append(curr_cls_box[k])
    return np.array(output)


def procss_img(img_path):
    if isinstance(img_path, str):
        img = cv2.imread(img_path)
    else:
        img = img_path
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = letterbox_image(img, (IMG_SIZE, IMG_SIZE))
    or_img = np.array(img, np.uint8)
    or_img = cv2.cvtColor(or_img, cv2.COLOR_RGB2BGR)
    img = img.astype(dtype=np.float32)
    img -= np.array((104, 117, 123), np.float32)
    img = np.expand_dims(img, axis=0)
    return img, or_img


def to_input(pil_rgb_image):
    np_img = np.array(pil_rgb_image)
    brg_img = ((np_img[:, :, ::-1] / 255.0) - 0.5) / 0.5
    return np.expand_dims(brg_img.transpose(2, 0, 1), axis=0).astype(np.float32)


def transform_landmarks(original_landmarks, face_bbox, face_image_shape):
    x1, y1, x2, y2 = face_bbox
    face_width, face_height = x2 - x1, y2 - y1
    scale_x = face_width / face_image_shape[1]
    scale_y = face_height / face_image_shape[0]
    transformed_landmarks = original_landmarks.copy()
    transformed_landmarks[:, 0] = (transformed_landmarks[:, 0] - x1) / scale_x
    transformed_landmarks[:, 1] = (transformed_landmarks[:, 1] - y1) / scale_y
    return transformed_landmarks


def norm_crop(img, landmark, image_size=112):
    """
    人脸对齐函数
    Args:
        img: 输入的人脸图片,建议为opencv读取的图片
        landmark: 五个人脸关键点,输入时建议reshape成[-1,2]
        image_size: 对齐后图片的尺寸
    Returns: 对齐后的人脸图片
    """
    def transformation_from_points(points1, points2):
        points1 = points1.astype(np.float64)
        points2 = points2.astype(np.float64)
        c1 = np.mean(points1, axis=0)
        c2 = np.mean(points2, axis=0)
        points1 -= c1
        points2 -= c2
        s1 = np.std(points1)
        s2 = np.std(points2)
        points1 /= s1
        points2 /= s2
        U, S, Vt = np.linalg.svd(points1.T * points2)
        R = (U * Vt).T
        return np.vstack([np.hstack(((s2 / s1) * R, c2.T - (s2 / s1) * R * c1.T)), np.matrix([0., 0., 1.])])

    img_size = np.array([112, 112])
    coord5point = np.array([(0.31556875000000000, 0.4615741071428571),
                            (0.68262291666666670, 0.4615741071428571),
                            (0.50026249999999990, 0.6405053571428571),
                            (0.34947187500000004, 0.8246919642857142),
                            (0.65343645833333330, 0.8246919642857142)])
    coord5point = coord5point * img_size
    pts1 = np.float64(np.matrix([[point[0], point[1]] for point in landmark]))
    pts2 = np.float64(np.matrix([[point[0], point[1]] for point in coord5point]))
    try:
        M = transformation_from_points(pts1, pts2)
        warped = cv2.warpAffine(img, M[:2], (image_size, image_size))
    except Exception as e:
        warped = None
    return warped


def refine_face_bbox(bbox, img):
    height, width, _ = img.shape
    x1, y1, x2, y2 = bbox
    expand_w = (x2 - x1)
    expand_h = (y2 - y1)
    x1 -= expand_w * 0.40
    y1 -= expand_h * 0.40
    x2 += expand_w * 0.40
    y2 += expand_h * 0.40
    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
    x1 = np.clip(x1, 0, width - 1)
    y1 = np.clip(y1, 0, height - 1)
    x2 = np.clip(x2, 0, width - 1)
    y2 = np.clip(y2, 0, height - 1)
    return x1, y1, x2, y2



def detect_faces(img_face):
    """检测图像中的人脸并返回所有的边界框和关键点"""
    img, or_img = procss_img(img_face)
    outputs = face_recognition.rknn_detection.inference(inputs=[img])
    boxes = face_recognition._decode_outputs(outputs)
    return img,or_img,boxes

def get_largest_face(boxes):
    """从检测到的人脸中选择最大的一个"""
    if len(boxes) == 0:
        return None
    face_areas = [(box[2] - box[0]) * (box[3] - box[1]) for box in boxes]
    largest_face_index = np.argmax(face_areas)
    return boxes[largest_face_index]


def is_front_face(keypoints):
    '''
    侧脸判断
    抬头仰头判断
    Args:
        keypoints: 人脸关键点(左眼、右眼、鼻子、左嘴角、右嘴角)
    Returns: orientation, isfront
    '''
    isfront = True
    orientation = []
    # print(keypoints,"args keypoint#######################")
    # 侧脸判断
    a = orthogonal_point(keypoints[0], keypoints[1], keypoints[2])
    m = (keypoints[0] + keypoints[1]) / 2
    dis_front = np.linalg.norm(a - m) / np.maximum(np.linalg.norm(keypoints[0] - keypoints[1]), 1e-10)
    orientation.append(dis_front)
    # 抬头低头判断 取交点,满足：$(x_3 -a) = \alpha(b - a)$
    k1, c1 = linear_equation(keypoints[2][0], keypoints[2][1], a[0], a[1])
    k2, c2 = linear_equation(keypoints[3][0], keypoints[3][1], keypoints[4][0], keypoints[4][1])
    b = linear_equation(k1, c1, k2, c2, beg_k=False)  # x3a与x4x5的交点
    dis_bow = np.linalg.norm(keypoints[2] - a) / np.maximum(np.linalg.norm(b - a), 1e-10)
    orientation.append(dis_bow)
    if (dis_front > 0.5) or (not 0.2 < dis_bow < 0.7):
        isfront = False
    return orientation, isfront, [a, b]


def orthogonal_point(x1, x2, x3):
    '''
    已知：(x1,y1)、(x2,y2)、(x3,y3)
    求：a = (ax, ay)
    ∵ 向量x1x2 = (x2-x1,y2-y1) 正交于 向量ax3 = (x3-ax, y3-ay)
    ∴ (x2 - x1)(x3 - ax) + (y2-y1)(y3-ay) = 0
    ∵ a在向量x1x2上, y = λx + b
    ∴ (x2 - x1)(x3 - ax) + (y2-y1)(y3- λax + b) = 0
    ∴ ax = (x3*((x2 - x1)) + y3*(y2-y1) - (y2-y1)*b) / (((x2 - x1)) + (y2-y1)*λ)
    ∴ ay = λ ax + b
    '''
    x1, y1 = x1[0], x1[1]
    x2, y2 = x2[0], x2[1]
    x3, y3 = x3[0], x3[1]
    # conb, lambdax = np.linalg.solve(np.array([[1, x1], [1, x2]]), np.array([y1, y2]))
    k, b = linear_equation(x1, y1, x2, y2, beg_k=True)
    ax = (x3 * (x2 - x1) + y3 * (y2 - y1) - (y2 - y1) * b) / np.maximum((x2 - x1) + (y2 - y1) * k, 1e-10)
    ay = k * ax + b
    return np.array([ax, ay])


def linear_equation(x1, y1, x2, y2, beg_k=True):
    '''
    + beg_k=True：两点确定一条直线
    + beg_k=False: 求两条直线的交点
        a, x3, x4, x5 -> b
        直线1：y = k1 * x + c1
        直线2：y = k2 * x + c2
        x = (c1 - c2) / (k2 - k1)
        y = k1 * x + c1
    Args:
        beg_k: true 为解方程求截取和斜率,传入为x1, y1, x2, y2。false 为求x和y,传入为两个方程的截距和斜率
    Returns: (k, b) or (x, y)
    '''
    if beg_k:
        k = (y2 - y1) / np.maximum((x2 - x1), 1e-10)
        b = y1 - k * x1
        return k, b
    else:
        k1, c1, k2, c2 = x1, y1, x2, y2
        x = (c1 - c2) / np.maximum((k2 - k1), 1e-10)
        y = k1 * x + c1
        return np.array([x, y])


class FaceRecognition:
    def __init__(self, face_model_path, detection_model_path, dim=512, index_file='/mnt/keep/workspace/digital_human_data/face_info/face_files/face_index.faiss', 
                 map_file='/mnt/keep/workspace/digital_human_data/face_info/face_files/id_name_map.pkl', stranger_index_file = "/mnt/keep/workspace/digital_human_data/face_info/face_files/stranger_face_index.faiss",
                 stranger_map_file='/mnt/keep/workspace/digital_human_data/face_info/face_files/stranger_id_time.pkl'):
        # 初始化 RKNN 模型
        self.rknn_face = RKNNLite()
        ret = self.rknn_face.load_rknn(face_model_path)
        if ret != 0:
            raise RuntimeError("Failed to load face recognition model")
        ret = self.rknn_face.init_runtime()
        if ret != 0:
            raise RuntimeError("Failed to initialize face recognition runtime")

        self.rknn_detection = RKNNLite()
        ret = self.rknn_detection.load_rknn(detection_model_path)
        if ret != 0:
            raise RuntimeError("Failed to load face detection model")
        ret = self.rknn_detection.init_runtime()
        if ret != 0:
            raise RuntimeError("Failed to initialize face detection runtime")

        self.people_det = RKNNLite()
        ret = self.people_det.load_rknn(people_model_path)
        if ret != 0:
            raise RuntimeError("Failed to load face detection model")
        ret = self.people_det.init_runtime()
        if ret != 0:
            raise RuntimeError("Failed to initialize face detection runtime")
        
        
        # 初始化人脸库 Faiss 索引
        self.faiss_index = faiss.IndexIDMap(faiss.IndexFlatIP(dim))  # 512 是特征向量的维度
        self.id_name_map = {}  # 用于存储ID与姓名、person_id的映射关系
        self.current_id = 0  # 当前最大ID值
        self.wake_push_id_time = {}

        # 初始化陌生人人脸库
        self.stranger_current_id = 0
        self.stranger_faiss_index = faiss.IndexIDMap(faiss.IndexFlatIP(dim))  # 512 是特征向量的维度
        self.stranger_id_time_map = {}  # 用于存储ID与姓名、person_id的映射关系
        self.unknown_count = 0
        
        self.index_file = index_file
        self.map_file = map_file
        if os.path.exists(self.index_file):
            self.load_index()
        if os.path.exists(self.map_file):
            self.load_id_name_map()
            
        self.stranger_index_file = stranger_index_file
        self.stranger_map_file = stranger_map_file
        if os.path.exists(stranger_index_file):
            self.load_stranger_index()
        if os.path.exists(stranger_map_file):
            self.load_stranger_id_time_map()


    def clear_index(self):
        """
        清空 Faiss 索引和 id_name_map 映射关系
        """
        self.faiss_index.reset()
        self.id_name_map.clear()
        self.current_id = 0


    def save_index(self):
        """
        将 Faiss 索引持久化存储到文件
        """
        faiss.write_index(self.faiss_index, self.index_file)
        with open(self.map_file, 'wb') as f:
            pickle.dump(self.id_name_map, f)


    def save_stranger_index(self):
        faiss.write_index(self.stranger_faiss_index, self.stranger_index_file)
    
    def save_stranger_id_time_map(self):
        tmp = self.stranger_id_time_map
        tmp["nextId"] = self.stranger_current_id
        with open(self.stranger_map_file, 'wb') as f:
            pickle.dump(tmp, f)

    def load_index(self):
        """
        从文件加载 Faiss 索引
        """
        self.faiss_index = faiss.read_index(self.index_file)

    def load_stranger_index(self):
        self.stranger_faiss_index = faiss.read_index(self.stranger_index_file)


    def load_id_name_map(self):
        """
        从文件加载 id_name_map 映射关系
        """
        with open(self.map_file, 'rb') as f:
            self.id_name_map = pickle.load(f)
            
            # 筛选出可以转换为整数的键，并取其最大值
            integer_keys = [int(key) for key in self.id_name_map.keys() if str(key).isdigit()]
            
            if integer_keys:
                self.current_id = max(integer_keys) + 1
            else:
                self.current_id = 1  # 如果没有整数键，初始化为1

    def load_stranger_id_time_map(self):
        with open(self.stranger_map_file, 'rb') as f:
            self.stranger_id_time_map = pickle.load(f)
        self.stranger_current_id = self.stranger_id_time_map["nextId"]
        del self.stranger_id_time_map["nextId"]


    def _l2_normalize(self, vector):
        """
        进行L2归一化
        """
        return vector / np.linalg.norm(vector)


    def extract_features(self, img_path):
        img, or_img = procss_img(img_path)
        outputs = self.rknn_detection.inference(inputs=[img])
        boxes = self._decode_outputs(outputs)

        if len(boxes) == 1:
            b = list(map(int, boxes[0]))
            x1, y1, x2, y2 = b[:4]

            face_img = or_img[b[1]:b[3], b[0]:b[2]]
            landmarks = [b[5], b[6], b[7], b[8], b[9], b[10], b[11], b[12], b[13], b[14]]
            reshaped_keypoints = np.array(landmarks).reshape((5, 2))
            try:
                transformed_landmarks = transform_landmarks(reshaped_keypoints, (x1, y1, x2, y2), face_img.shape[:2])
                feature_vector = self._get_face_representation(face_img, transformed_landmarks, x1, y1, x2, y2)
                return self._l2_normalize(feature_vector)
            except:
                pass

        return None


    def add_face_to_index(self, img_path, person_id, lib_id, name):
        """
        添加人脸特征到 Faiss 索引和 id_name_map 中
        """
        feature_vector = self.extract_features(img_path)
        if feature_vector is None:
            feature_vector = np.zeros((512,), dtype=np.float32)
        
        # 添加到 Faiss 索引
        self.faiss_index.add_with_ids(np.array([feature_vector]).astype('float32'), np.array([self.current_id]))
        # 更新 id_name_map
        self.id_name_map[self.current_id] = {"person_id": person_id, "library_id": lib_id, "name": name}
        # 自增 current_id
        self.current_id += 1
        # 保存索引和映射
        self.save_index()
        return self.current_id - 1


    def update_face_in_index(self, img_path, person_id, lib_id, name=None):
        """
        更新人脸信息：重新提取人脸特征并更新到 Faiss 索引和 pkl 文件中。
        
        Args:
            person_id (str): 需要更新的人脸的 ID。
            img_path (str): 包含人脸的新图像的路径。
            name (str): 可选，新的人脸名称（如果需要更新）。
        """
        feature_vector = self.extract_features(img_path)

        if feature_vector is None:
            feature_vector = np.zeros((512,), dtype=np.float32)

        # 查找person_id对应的index
        index_to_update = None
        for index, info in self.id_name_map.items():
            if info["person_id"] == person_id and info["library_id"] == lib_id:
                index_to_update = index
                break
        if index_to_update is not None:
            # 删除旧的特征向量
            self.faiss_index.remove_ids(np.array([index_to_update]))

            # 添加新的特征向量到Faiss
            self.faiss_index.add_with_ids(np.array([feature_vector]), np.array([index_to_update]))

            # 更新id_name_map中的name
            if name:
                self.id_name_map[index_to_update]["name"] = name

            # 保存更新后的索引和映射关系
            self.save_index()
            return 1
        else:
            return

    def delete_face_from_index(self, person_id, lib_id):
        """
        从 Faiss 索引和 pkl 文件中删除指定的人员 ID 对应的人脸信息。
        
        Args:
            person_id (str): 需要删除的人脸的 ID。
        """
        index_to_delete = None
        for index, info in self.id_name_map.items():
            if info["person_id"] == person_id and info["library_id"] == lib_id:
                index_to_delete = index
                break
        
        if index_to_delete is not None:
            # 从 Faiss 索引中删除
            self.faiss_index.remove_ids(np.array([index_to_delete]))

            # 从 id_name_map 中删除
            del self.id_name_map[index_to_delete]

            # 保存更新后的索引和映射关系
            self.save_index()
            return 1
        else:
            return

    def recognize_face_wake(self, img, ratio_thred=0.005, similar_thred=0.15, stranger_similar_thred=0.5):
        # 检测人脸
        img, or_img = procss_img(img)
        outputs = self.rknn_detection.inference(inputs=[img])
        boxes = self._decode_outputs(outputs, 0.75, 0.7)
        or_img_area = or_img.shape[0] * or_img.shape[1]
        max_person_ratio = 0
        max_person_box = None
        
        # 找出占比最大且满足阈值的脸
        for b in boxes:
            person_area = abs((b[2] - b[0]) * (b[3] - b[1]))
            person_ratio = person_area / or_img_area
            if person_ratio > ratio_thred and person_ratio > max_person_ratio:
                max_person_ratio = person_ratio
                max_person_box = b
               
        if max_person_box is not None:
            # 获取人脸关键点
            b = list(map(int, b))
            landmarks = [b[5], b[6],b[7],b[8],b[9],b[10],b[11],b[12],b[13],b[14]]
            keypoints = np.array(landmarks)
            reshaped_keypoints = keypoints.reshape((5, 2))
            
            # 裁剪人脸，获取人脸特征向量
            refine_box = refine_face_bbox([b[0],b[1],b[2],b[3]], or_img)
            r_x1,r_y1,r_x2,r_y2 = refine_box

            face_img = or_img[int(r_y1):int(r_y2),int(r_x1):int(r_x2)]
            transformed_landmarks = transform_landmarks(reshaped_keypoints, (r_x1,r_y1,r_x2,r_y2), face_img.shape[:2])
            feature_vector = self._get_face_representation(face_img, transformed_landmarks, r_x1,r_y1,r_x2,r_y2)
            
            if feature_vector is not None:
                feature_vector = self._l2_normalize(feature_vector)
                
                # 对比人脸库
                distances, indices = self.faiss_index.search(np.array([feature_vector]).astype('float32'), 1)
                max_dist = distances[0][0]
                max_id = indices[0][0]
                # mylog.info("人脸库相似度：" + str(max_dist))
                if max_dist > similar_thred:
                    # 在人脸库
                    # return "staff", max_id
                    self.unknown_count = 0
                    if max_id not in self.wake_push_id_time.keys():
                        return 1   # 1：在人脸库，没唤醒过，唤醒
                    else:
                        # 之前推送过，但是已经过了时间了：重新唤醒，更新唤醒时间
                        if time.time() - self.wake_push_id_time[max_id] > WAKE_INTERVAL:
                            self.wake_push_id_time[max_id] = time.time()
                            return 1  # 1：在人脸库，唤醒过，但是唤醒已经过期了，唤醒
                        
                        # 之前推送过，未到时间：不再唤醒
                        else:
                            return 2   # 2：在人脸库，不唤醒
                        
                else:
                    # 不在人脸库，对比陌生人人脸库
                    stranger_distances, stranger_indices = self.stranger_faiss_index.search(np.array([feature_vector]).astype('float32'), 1)
                    stranger_max_dist = stranger_distances[0][0]
                    stranger_id = stranger_indices[0][0]
                    # mylog.info("陌生人脸库相似度：" + str(stranger_max_dist))
                    if stranger_max_dist > stranger_similar_thred:
                        # 在陌生人人脸库
                        # return "guest", stranger_id
                        self.unknown_count = 0
                        if time.time() - self.stranger_id_time_map[stranger_id] > WAKE_INTERVAL:
                            self.stranger_id_time_map[stranger_id] = time.time()
                            self.save_stranger_id_time_map()
                            return 3    # 3：在陌生人人脸库，唤醒
                        else:
                            return 4     # 4：在陌生人人脸库，不唤醒
                    
                    else:
                        # 不在陌生人人脸库
                        _, is_front, _ = is_front_face(reshaped_keypoints)
                        if is_front:
                            self.unknown_count += 1
                            # mylog.info("正脸")
                            if self.unknown_count > 10:
                                # 是正脸：先删除，再添加，再推消息
                                need_remove_ids = []
                                # mylog.info(self.stranger_id_time_map)
                                for id in list(self.stranger_id_time_map.keys()):
                                    if id != "nextId" and time.time() - self.stranger_id_time_map[id] > WAKE_INTERVAL:
                                        need_remove_ids.append(id)
                                        del self.stranger_id_time_map[id]
                                
                                # mylog.info(need_remove_ids)
                                if len(need_remove_ids) > 0:
                                    self.stranger_faiss_index.remove_ids(np.array(need_remove_ids))
                                # 添加
                                self.stranger_faiss_index.add_with_ids(np.array([feature_vector]), np.array([self.stranger_current_id]))
                                self.stranger_id_time_map[self.stranger_current_id] = time.time()
                                self.stranger_current_id = (self.stranger_current_id + 1) % 100000
                                self.save_stranger_index()
                                self.save_stranger_id_time_map()
                                # mylog.info("添加陌生人成功")
                                return 5   # 不在人脸库，不在陌生人人脸库，唤醒
                            else:
                                return 0
                            # return "unknown", None
                        else:
                            # 非正脸
                            # mylog.info("非正脸")
                            return 0
        else:
            return 0   # 0: 表示未识别到人
        
    # 带追踪
    # def recognize_stranger(self, img, similar_thred=0.12, confidence = 0.6, nms_threshold = 0.45):
    #     image, gain = letterbox(img, [IMG_SIZE, IMG_SIZE])
    #     box_src = predict(confidence, nms_threshold, image, [IMG_SIZE, IMG_SIZE], self.people_det)
    #     people_boxes = get_draw_box(box_src, gain)
        
    #     person_ratio_boxs = []
    #     or_img_area = img.shape[0] * img.shape[1]
    #     ratio_thred = 0.02
    #     for bb in people_boxes:
    #         person_area = abs((bb[2] - bb[0]) * (bb[3] - bb[1]))
    #         person_ratio = person_area / or_img_area
    #         if person_ratio > ratio_thred:
    #             person_ratio_boxs.append(bb)
                
    #     if len(person_ratio_boxs) > 0:
    #         tracks = tracker.update(np.asarray(person_ratio_boxs))
    #         track_list = []
    #         for b in tracks:
    #             person_box = [b[0], b[1], b[2], b[3]]
    #             new_img = img[int(b[1]):int(b[3]), int(b[0]):int(b[2])]
    #             track_id = int(b[4])
    #             track_list.append(track_id)
    #             foot_center_point = [int((b[0]+b[2])/2), int(b[3])]
                
    #             # cv2.imwrite("{}.jpg".format(time.time()), new_img)
    #             face_img, or_face_img = procss_img(new_img)
    #             face_outputs = self.rknn_detection.inference(inputs=[face_img])
    #             face_boxes = self._decode_outputs(face_outputs)
    #             if len(face_boxes) > 0:
    #                 # 识别到人脸，判断是否是陌生人
    #                 for fb in face_boxes:
    #                     fb = list(map(int, fb))
    #                     landmarks = [fb[5], fb[6],fb[7],fb[8],fb[9],fb[10],fb[11],fb[12],fb[13],fb[14]]
    #                     keypoints = np.array(landmarks)
    #                     reshaped_keypoints = keypoints.reshape((5, 2))

    #                     refine_box = refine_face_bbox([fb[0],fb[1],fb[2],fb[3]],or_face_img)
    #                     r_x1,r_y1,r_x2,r_y2 = refine_box

    #                     face_img = or_face_img[int(r_y1):int(r_y2),int(r_x1):int(r_x2)]
    #                     transformed_landmarks = transform_landmarks(reshaped_keypoints, (r_x1,r_y1,r_x2,r_y2), face_img.shape[:2])
    #                     feature_vector = self._get_face_representation(face_img, transformed_landmarks, r_x1,r_y1,r_x2,r_y2)
                        
    #                     if feature_vector is not None:
    #                         feature_vector = self._l2_normalize(feature_vector)
    #                         distances, indices = self.faiss_index.search(np.array([feature_vector]).astype('float32'), 1)
    #                         max_dist = distances[0][0]
    #                         # 陌生人
    #                         if max_dist < similar_thred:
    #                             # 陌生人
    #                             people_track.add_track_record(track_id, foot_center_point, person_box)
    #                         else:
    #                             # 熟人
    #                             people_track.add_track_record(track_id, foot_center_point, person_box, "known")
    #             else:
    #                 # 未识别到人脸，默认为陌生人
    #                 people_track.add_track_record(track_id, foot_center_point, person_box)
                    
    #         return track_list
    #     else:
    #         # 未检测到人体
    #         return []
    
    
    def recognize_stranger(self, img, similar_thred=0.12, confidence = 0.6, nms_threshold = 0.45):
        image, gain = letterbox(img, [IMG_SIZE, IMG_SIZE])
        box_src = predict(confidence, nms_threshold, image, [IMG_SIZE, IMG_SIZE], self.people_det)
        people_boxes = get_draw_box(box_src, gain)
        
        person_ratio_boxs = []
        or_img_area = img.shape[0] * img.shape[1]
        ratio_thred = 0.02
        for bb in people_boxes:
            person_area = abs((bb[2] - bb[0]) * (bb[3] - bb[1]))
            person_ratio = person_area / or_img_area
            if person_ratio > ratio_thred:
                person_ratio_boxs.append(bb)
         
        people_list = {
            "stranger": [],
            "known": []
        }
        if len(person_ratio_boxs) > 0:
            for b in person_ratio_boxs:
                person_box = [b[0], b[1], b[2], b[3]]
                new_img = img[int(b[1]):int(b[3]), int(b[0]):int(b[2])]

                face_img, or_face_img = procss_img(new_img)
                face_outputs = self.rknn_detection.inference(inputs=[face_img])
                face_boxes = self._decode_outputs(face_outputs)
                if len(face_boxes) > 0:
                    # 识别到人脸，判断是否是陌生人
                    for fb in face_boxes:
                        fb = list(map(int, fb))
                        landmarks = [fb[5], fb[6],fb[7],fb[8],fb[9],fb[10],fb[11],fb[12],fb[13],fb[14]]
                        keypoints = np.array(landmarks)
                        reshaped_keypoints = keypoints.reshape((5, 2))

                        refine_box = refine_face_bbox([fb[0],fb[1],fb[2],fb[3]],or_face_img)
                        r_x1,r_y1,r_x2,r_y2 = refine_box

                        face_img = or_face_img[int(r_y1):int(r_y2),int(r_x1):int(r_x2)]
                        transformed_landmarks = transform_landmarks(reshaped_keypoints, (r_x1,r_y1,r_x2,r_y2), face_img.shape[:2])
                        feature_vector = self._get_face_representation(face_img, transformed_landmarks, r_x1,r_y1,r_x2,r_y2)
                        
                        if feature_vector is not None:
                            feature_vector = self._l2_normalize(feature_vector)
                            distances, indices = self.faiss_index.search(np.array([feature_vector]).astype('float32'), 1)
                            max_dist = distances[0][0]
                            # 陌生人
                            # print(f"------------------{max_dist}---------------------")
                            if max_dist < similar_thred:
                                # 陌生人
                                people_list["stranger"].append(person_box)
                            else:
                                # 熟人
                                people_list["known"].append(person_box)
                else:
                    # 未识别到人脸，默认为陌生人
                    people_list["stranger"].append(person_box)
                    
            return people_list
        else:
            # 未检测到人体
            return people_list
        
    
    def recognize_face(self, largest_face_box,or_img):
        if largest_face_box is not None:
            b = list(map(int, largest_face_box))
            landmarks = [b[5], b[6],b[7],b[8],b[9],b[10],b[11],b[12],b[13],b[14]]
            keypoints = np.array(landmarks)
            reshaped_keypoints = keypoints.reshape((5, 2))

            refine_box = refine_face_bbox([b[0],b[1],b[2],b[3]],or_img)
            r_x1,r_y1,r_x2,r_y2 = refine_box

            face_img = or_img[int(r_y1):int(r_y2),int(r_x1):int(r_x2)]

            transformed_landmarks = transform_landmarks(reshaped_keypoints, (r_x1,r_y1,r_x2,r_y2), face_img.shape[:2])

            feature_vector = self._get_face_representation(face_img, transformed_landmarks, r_x1,r_y1,r_x2,r_y2)

            if feature_vector is not None:
                # 进行L2归一化
                feature_vector = self._l2_normalize(feature_vector)
                distances, indices = self.faiss_index.search(np.array([feature_vector]).astype('float32'), 1)
                max_dist = distances[0][0]
                
                if max_dist >=0:  # 对于内积，值越大相似度越高
                    matched_person = self.id_name_map[int(indices[0])]
                    # cv2.imwrite(f"./{time.time()}_{matched_person['name']}_{max_dist}.jpg",face_img)
                    return matched_person, max_dist
                else:
                    return None, 0


    def _decode_outputs(self, outputs, conf_thres = 0.6, iou_thres = 0.3):
        output_1 = np.array(outputs[0]).squeeze()
        output_2 = np.array(outputs[1]).squeeze()
        output_3 = np.array(outputs[2]).squeeze()

        anchors = Anchors(cfg_mnet, image_size=(IMG_SIZE, IMG_SIZE)).get_anchors()
        boxes = decode(output_1, anchors, cfg_mnet['variance'])
        landms = decode_landm(output_3, anchors, cfg_mnet['variance'])
        conf = output_2[:, 1:2]
        boxs_conf = np.concatenate((boxes, conf, landms), -1)
        boxs_conf = filter_box(boxs_conf, conf_thres, iou_thres)

        return boxs_conf
    
    
    def _get_face_representation(self, face_img, transformed_landmarks, x1, y1, x2, y2):
        align_face = norm_crop(face_img, transformed_landmarks)
        aligned_rgb_img = Image.fromarray(cv2.cvtColor(align_face, cv2.COLOR_BGR2RGB))
        bgr_tensor_input = to_input(aligned_rgb_img)

        if bgr_tensor_input is not None:
            feature = self.rknn_face.inference(inputs=[np.expand_dims(align_face, 0)])
            feature_array = np.array(feature).reshape(-1)  # 展平为一维向量

            if feature_array.size == 512:  # 确保维度正确
                return feature_array
            else:
                raise ValueError(f"Feature vector size is incorrect: expected 512, got {feature_array.size}")

        return None
    

    def display_faiss_vectors(self):
        """
        打印 Faiss 索引中的向量及它们与 id_name_map 的对应关系。
        """
        # 获取索引中的向量数量
        total_vectors = self.faiss_index.ntotal
        
        if total_vectors == 0:
            return

        # 为了获取向量，执行一个检索
        # 生成一个随机查询向量
        dummy_query = np.random.random((1, self.faiss_index.d)).astype('float32')
        distances, ids = self.faiss_index.search(dummy_query, total_vectors)

        # 遍历每个向量及其对应的ID
        for idx in range(total_vectors):
            vector_id = int(ids[0][idx])  # 确保 vector_id 是整数类型
            vector_info = self.id_name_map.get(vector_id, {"person_id": "Unknown", "name": "Unknown"})


face_recognition = FaceRecognition(face_model_path=face_model_path, detection_model_path=model_path)


# if __name__ == '__main__':
#     face_recognition = FaceRecognition(face_model_path=face_model_path, detection_model_path=model_path)
    
#     # 添加人脸到索引
#     face_recognition.add_face_to_index("", person_id='2', name='你知道')

#     # print(face_recognition.id_name_map)
#     # face_recognition.delete_face_from_index("3")
#     face_recognition.update_face_in_index("1","hyl.jpg","胡永雷")

#     print(face_recognition.id_name_map)
#     print(face_recognition.display_faiss_vectors())

#     import time
#     cap = cv2.VideoCapture(20)
#     while True:
#         ret, frame = cap.read()

#         matched_person, confidence = face_recognition.recognize_face(frame)
    
#         if matched_person:
#             print(f'识别结果: {matched_person["name"]}, 置信度: {confidence}')
      
        # 保存 Faiss 索引和映射关系
        # face_recognition.save_index()