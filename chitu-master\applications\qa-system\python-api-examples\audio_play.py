import sounddevice as sd  
import numpy as np  
from scipy.io.wavfile import read  
  
# 读取 WAV 文件  
samplerate, data = read('demo_result_826c26a40dd411ef9d4e2ef1180d97ca.wav')  
  
# 确保数据是 float32 类型，并且范围在 [-1, 1]  
# 如果数据是整数类型（如 int16），我们需要将其转换为 float32 并归一化  
if data.dtype not in [np.float32, np.float64]:  
    max_data = np.iinfo(data.dtype).max  
    data = data.astype(np.float32) / max_data  
  
# 播放音频  
# 注意：sounddevice.play 是非阻塞的，所以我们需要确保数据已经播放完毕  
# 我们可以使用 sounddevice.wait 来等待播放完成  
sd.play(data, samplerate)  
sd.wait()
