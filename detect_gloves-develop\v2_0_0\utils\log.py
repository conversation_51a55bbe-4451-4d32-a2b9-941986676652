import os
import sys
import logging
from logging.handlers import RotatingFile<PERSON><PERSON><PERSON>, TimedRotatingFileHandler
from time import strftime
PATH = "log/"
FMT = '%(asctime)s %(filename)s [line:%(lineno)d] %(levelname)s: %(message)s'
DATEFMT = '%Y-%m-%d %H:%M:%S'

if not os.path.exists(PATH):
    os.makedirs(PATH)

class MyLog(object):
    def __init__(self):
        self.logger = logging.getLogger()
        self.formatter = logging.Formatter(fmt=FMT, datefmt=DATEFMT)
        self.log_filename = '{0}{1}.log'.format(PATH, strftime("%Y-%m-%d"))

        self.logger.addHandler(self.get_file_handler(self.log_filename))
        # self.logger.addHandler(self.get_console_handler())
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            tfh = TimedRotatingFileHandler(filename=self.log_filename, when="D", interval=1, backupCount=6,encoding="utf-8")
            fmt = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s [line:%(lineno)d]')
            tfh.setFormatter(fmt)
            self.logger.addHandler(tfh)


    def get_file_handler(self, filename):
        filehandler = logging.FileHandler(filename, encoding="utf-8")
        filehandler.setFormatter(self.formatter)
        return filehandler
    
    def get_console_handler(self):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(self.formatter)
        return console_handler

if not os.path.exists(PATH):
    os.makedirs(PATH)

mylog = MyLog().logger
