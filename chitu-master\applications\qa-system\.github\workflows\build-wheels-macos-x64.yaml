name: build-wheels-macos-x64

on:
  push:
    branches:
      - wheel
    tags:
      - '*'
  workflow_dispatch:

env:
  SHERPA_ONNX_IS_IN_GITHUB_ACTIONS: 1

concurrency:
  group: build-wheels-macos-x64-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_wheels_macos_x64:
    name: ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest]
        python-version: ["cp37", "cp38", "cp39", "cp310", "cp311", "cp312"]

    steps:
      - uses: actions/checkout@v4

      # see https://cibuildwheel.readthedocs.io/en/stable/changelog/
      # for a list of versions
      - name: Build wheels
        if: matrix.python-version == 'cp37'
        uses: pypa/cibuildwheel@v2.11.4
        env:
          CIBW_BUILD: "${{ matrix.python-version}}-* "
          CIBW_ENVIRONMENT: SHERPA_ONNX_CMAKE_ARGS="-DCMAKE_OSX_ARCHITECTURES='x86_64'"
          CIBW_ARCHS: "x86_64"
          CIBW_BUILD_VERBOSITY: 3

          #  Don't repair macOS wheels
          CIBW_REPAIR_WHEEL_COMMAND_MACOS: ""

      - name: Build wheels
        if: matrix.python-version != 'cp37'
        uses: pypa/cibuildwheel@v2.15.0
        env:
          CIBW_BUILD: "${{ matrix.python-version}}-* "
          CIBW_ENVIRONMENT: SHERPA_ONNX_CMAKE_ARGS="-DCMAKE_OSX_ARCHITECTURES='x86_64'"
          CIBW_ARCHS: "x86_64"
          CIBW_BUILD_VERBOSITY: 3

          #  Don't repair macOS wheels
          CIBW_REPAIR_WHEEL_COMMAND_MACOS: ""

      - name: Display wheels
        shell: bash
        run: |
          ls -lh ./wheelhouse/

          ls -lh ./wheelhouse/*.whl

      - uses: actions/upload-artifact@v4
        with:
          name: wheel-${{ matrix.python-version }}
          path: ./wheelhouse/*.whl

      - name: Publish to huggingface
        if: matrix.python-version == 'cp38'
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/csukuangfj/sherpa-onnx-wheels huggingface
            cd huggingface
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../wheelhouse/*.whl .

            git status
            git add .
            git commit -m "add more wheels"
            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-wheels main

      - name: Publish wheels to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install wheel twine setuptools

          twine upload ./wheelhouse/*.whl
