import time
import json
import os
from selenium import webdriver
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options
from applications.models import Camera, Algorithm
from multiprocessing import Process,Manager
from applications.algorithm.screen.algo_screen import open_algo_screen
from applications.view.broadcast.live import get_local_ip
from multiprocessing import Queue
from log import mylog

geckodriver_path = '/usr/bin/geckodriver'
options = Options()
options.headless = True   # 如果需要无头浏览器，将False改为True


'''
service = FirefoxService(executable_path=geckodriver_path)
driver = webdriver.Firefox(service=service, options=options)
driver.maximize_window()
driver.implicitly_wait(2)
driver.set_page_load_timeout(3)
'''

algo_screen_p = None

manager = Manager()
shared_que = manager.Queue(maxsize=5)

def init_screen_mirror(app):
    global algo_screen_p,shared_que
    conf = json.load(open('applications/algorithm/screen_config.json', 'r'))
    if conf["Algorithm_Projection"]["status"]:
        mylog.info("算法投屏开启")
        camera_info = Camera.query.filter_by(camera_label=conf["Algorithm_Projection"]["camera"]).first()
        url = camera_info.stream_url
        camera_id = camera_info.id
        
        alarm_type = []
        weight_id = ""
        for item in conf["Algorithm_Projection"]["alarmType"]:
            alarm_type.append(Algorithm.query.filter_by(name=item).first().id)
            if weight_id == "":
                weight_id = Algorithm.query.filter_by(name=item).first().weight
        #
        if algo_screen_p is not None:
            stop_algo_screen()
            algo_screen_p.join()  # 确保旧的进程已经终止
            shared_que = manager.Queue(maxsize=5)

        result_que = Queue(maxsize=2)
        algo_screen_p = Process(target=open_algo_screen, args=(url, camera_id, weight_id, alarm_type, app, shared_que, result_que))
        algo_screen_p.start()
        
        time.sleep(4)
        mylog.info(f"-------------------{result_que.qsize()}")
        if result_que.qsize() > 0:
            driver.get("http://{}:9620".format(get_local_ip()))
            conf["Chitu_System"]["status"] = True
            conf["Digital_Avatar"]["status"] = False
            conf["Algorithm_Projection"]["status"] = False
            conf["Algorithm_Projection"]["camera"] = ""
            conf["Algorithm_Projection"]["alarmType"] = []
            with open('applications/algorithm/screen_config.json', 'w', encoding="utf-8") as f:
                f.write(json.dumps(conf))
        else:
            time.sleep(1)
            driver.get("http://{}:8000/api/algo_manage/projection/video_feed".format(get_local_ip()))
            
    elif conf["Chitu_System"]["status"]:
        mylog.info("赤兔投屏开启...")
        time.sleep(2)
        driver.get("http://{}:9620".format(get_local_ip()))
        
    elif conf["Digital_Avatar"]["status"]:
        mylog.info("数字人投屏开启...")
        time.sleep(2)
        driver.get("http://{}:9620/#/digital-human/screen".format(get_local_ip()))

def change_screen_mirror(screen_type):

    # pass
    # wins = driver.window_handles
    # mylog.info(wins)
    # driver.switch_to.window(wins[-1])
    
    try:
        if screen_type == "Chitu_System":
            driver.get("http://{}:9620".format(get_local_ip()))
        elif screen_type =="Algorithm_Projection":
            driver.get("http://{}:8000/api/algo_manage/projection/video_feed".format(get_local_ip()))
        elif screen_type =="Digital_Avatar":
            driver.get("http://{}:9620/#/digital-human/screen".format(get_local_ip()))
    except Exception as e:
        # mylog.error("Error changing screen mirror: {}".format(e))
        pass