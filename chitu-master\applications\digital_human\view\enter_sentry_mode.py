import json
import schedule
import threading

from log import mylog
from applications.digital_human.common.utils.stranger_rec import stranger_detect
from applications.digital_human.common.utils.qa_service import stop_qa_service_force
from applications.digital_human.common.common_utils import read_config, basic_settings_config_path
from applications.digital_human.common.utils.mode_change import enter_assistant_mode, enter_unattended_mode, enter_propagate_mode
from applications.digital_human.view.utils import ws_send_screen_msg

# 更新配置文件中的内容
def update_mode(mode):
    conf = read_config()
    conf["currentMode"] = mode
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)


# 进入哨兵模式: 启动陌生人识别功能, 语音服务不启动
'''
助理模式：无陌生人检测、语音服务启动
无人值守模式：陌生人检测启动、语音服务启动
'''
def enter_sentry_mode(last_mode=None, once=False):
    update_mode("SentryMode")
    stop_qa_service_force()
    if last_mode == "AssistantMode" or last_mode is None:
        stranger_detect.startStrangerDet()
        mylog.info("模式切换: 进入哨兵模式")
        msg = {
            "messageType": "messageToast",
            "messageBody": {
                "mode": "3",
                "code": 200,
                "text": "下班了,我将进入哨兵模式"
            }
        }
        ws_send_screen_msg(json.dumps(msg))
            
    elif last_mode == "SentryMode":
        mylog.info("模式切换: 目前已经是哨兵模式。")
    else:
        pass
    
    if once:
        schedule.clear("sentry_schedule_start")


# 退出哨兵模式，并进入设置的下一模式
def exit_sentry_mode():
    conf = read_config()
    next_mode = conf["nextServiceMode"]
    
    if next_mode in ["AssistantMode", "UnattendedMode"]:
        conf["currentMode"] = next_mode
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)
    
    if next_mode == "AssistantMode":
        need_msg = enter_assistant_mode(last_mode="SentryMode", need_update_file=False)
        msg = {
            "messageType": "messageToast",
            "messageBody": {
                "mode": "1",
                "code": 200,
                "text": "我已进入助理模式"
            }
        }
        ws_send_screen_msg(json.dumps(msg))
        if need_msg:
            warnMsg = {
                "messageType": "messageToast",
                "messageBody": {
                    "mode": "7",
                    "code": 300,
                    "text": "服务正在启动..."
                }
            }
            ws_send_screen_msg(json.dumps(warnMsg))    
        else:
            mylog.info("WEBSOCKET：前端客户端不在线。")
            
    elif next_mode == "UnattendedMode":
        need_msg = enter_unattended_mode(last_mode="SentryMode", need_update_file=False)
        msg = {
            "messageType": "messageToast",
            "messageBody": {
                "mode": "2",
                "code": 200,
                "text": "我已进入无人值守模式"
            }
        }
        ws_send_screen_msg(json.dumps(msg))
        if need_msg:
            warnMsg = {
                "messageType": "messageToast",
                "messageBody": {
                    "mode": "7",
                    "code": 300,
                    "text": "服务正在启动..."
                }
            }
            ws_send_screen_msg(json.dumps(warnMsg))
        else:
            mylog.info("WEBSOCKET：前端客户端不在线。")