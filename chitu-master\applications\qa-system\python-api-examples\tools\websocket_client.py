"""
File Name: Websocket_clienty

Description: This is an struct message from qa system Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import asyncio
import websockets
import json
import threading
import logging
from tools.qa_log import AppLogger
from tools.message_process import robot_message_process

class WebSocketClient:
    def __init__(self, uri, logger):
        self.logger = logger
        #服务端的url
        self.uri = uri
        #录音状态
        self.status = "1"
        #打卡状态
        self.attendanceStatus = "0"
        #人脸唤醒状态
        self.faceWakeUpStatus = "0"
        self.websocket = None
        #宣传状态
        self.propagandaStatus = "0"
        #运维状态
        self.operationsStatus = "0"
        #数据库更改状态
        self.configurationModificationCode = "0"
    #连接服务端
    def connect(self):
        self.th = threading.Thread(target=self.asyn_connect)
        self.th.start()

    def asyn_connect(self):
        asyncio.run(self.__connect())
    """    
    async def connect(self):
        async with websockets.connect(self.uri) as websocket:
            self.websocket = websocket
            message = {
                       'messageType': 'regist',
                       'clientType': 'algorithm'
                      }
            await self.send_message(json.dumps(message))
            print("注册成功")
    """
    async def __connect(self):
        async with websockets.connect(self.uri) as websocket:
            self.websocket =websocket
            message = {
                        'messageType': 'regist',
                        'clientType': 'algorithm'
                      }
            await self.send_message(json.dumps(message))
            self.logger.log_message('info', 'send message: ' + str(message))
            self.heartbeat_task = asyncio.create_task(self.send_heartbeat())
            await self.recv()
            await asyncio.Future()
    #接收消息
    async def recv(self):
        if self.websocket and not self.websocket.closed:
            while True:
                try:
                    message = await self.websocket.recv()
                    await self.process_message(message)
                    #self.message_received_event.set()
                except Exception as e:
                    #print(f"接收消息时发生错误: {e}")
                    continue
    #处理接受的消息
    async def process_message(self, message):
        # 解析消息，提取状态字段并更新
        # 这里假设消息是 JSON 格式，包含 "status" 字段
        try:
            data = json.loads(message)
            self.logger.log_message('info', 'recv message: ' + str(data))
            if 'faceWakeUpStatus' in data:
                self.faceWakeUpStatus = data["faceWakeUpStatus"]
            elif 'status' in data:           
                self.status = data['status']
            elif 'robotMessageInfo' in data:
                robotMessageInfo = data['robotMessageInfo']
                await robot_message_process(self,robotMessageInfo)
            elif 'propagandaStatus' in data:
                self.propagandaStatus = data['propagandaStatus']      
            elif 'configurationModificationCode' in data:
                self.configurationModificationCode = data['configurationModificationCode']   
            elif 'attendanceStatus' in data:
                self.attendanceStatus = data['attendanceStatus']   
            elif 'operationsStatus' in data:
                self.operationsStatus = data['operationsStatus']
                if data['operationsStatus'] == '1':
                    self.status ='0'
                elif data['operationsStatus'] == '0':
                    self.status = '1'    
        except json.JSONDecodeError:
            pass
    #获取录音状态    
    def get_status(self):
        return self.status
    #获取人脸状态
    def get_face_status(self):
        return self.faceWakeUpStatus
    #获取打卡状态
    def get_attendanceStatus(self):
        return self.attendanceStatus
    #获取宣传模式    
    def get_propaganda_status(self):
        return self.propagandaStatus
    #获取数据库更改状态
    def get_configurationModificationCode(self):
        return self.configurationModificationCode    
    #获取运维模式状态
    def get_operationsStatus(self):
        return self.operationsStatus
    #改变录音状态    
    async def change_status(self):
        await self.process_message(json.dumps({"status":"0"}))
 
    async def change_status_pick(self):
        await self.process_message(json.dumps({"status":"1"}))

    async def change_face_status(self):
        await self.process_message(json.dumps({"faceWakeUpStatus":"0"}))

    async def change_enterAttendanceStatus(self):
        await self.process_message(json.dumps({"attendanceStatus":"1"}))

    async def change_exitAttendanceStatus(self):
        await self.process_message(json.dumps({"attendanceStatus":"0"}))    

    async def change_exit_propaganda_status(self):
        await self.process_message(json.dumps({"propaganda":None}))  

    async def change_enter_propaganda_status(self):
        await self.process_message(json.dumps({"propaganda":"1"}))  

    async def change_exit_propaganda_status(self):
        await self.process_message(json.dumps({"propaganda":None}))  

    async def change_enter_propaganda_status(self):
        await self.process_message(json.dumps({"propaganda":"1"}))         

    async def change_configurationModificationCode(self):
        await self.process_message(json.dumps({"configurationModificationCode":"0"}))           
    #发送消息
    async def send_message(self, message):
        #async with websockets.connect(self.uri) as websocket:
        if self.websocket and not self.websocket.closed:
            await self.websocket.send(message)
            self.logger.log_message('info', 'send message: ' + str(message))
 
    #发送心跳  
    async def send_heartbeat(self):
        while True:
            try:
                if self.websocket and not self.websocket.closed:
                    message = {
                        'messageType': 'ping',
                        'clientType': 'algorithm'
                      }
                    await self.websocket.send(json.dumps(message))  
                else:
                    await self.__connect()
                    self.logger.log_message('info', '问答系统重连 websocket...')
                await asyncio.sleep(30)
            #except websockets.exceptions.ConnectionClosedError:
            except Exception as e:
                await asyncio.sleep(3)
