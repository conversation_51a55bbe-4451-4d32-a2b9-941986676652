#!/usr/bin/env python3
"""
测试人员跌倒算法的报警逻辑
"""

import sys
import os
import cv2
import numpy as np
import base64
import json

# 添加模块路径
sys.path.append('detect_fallperson/v2_0_0')

def create_test_image():
    """创建测试图像"""
    img = np.ones((480, 640, 3), dtype=np.uint8) * 128
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    return base64.b64encode(buffer).decode('utf-8')

def test_all_mode():
    """测试all模式"""
    print("🔍 测试all模式")
    print("=" * 50)
    
    try:
        from postprocess.post_process import PostProcess
        from utils.i18n import I18n
        
        # 创建后处理实例
        i18n = I18n()
        post_mess = ["detect_fallperson", "v2.0.0", "fallperson"]
        postprocessor = PostProcess(i18n, post_mess)
        
        # 创建测试数据
        test_img = create_test_image()
        
        # 模拟检测结果：包含person和fallperson
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],      # 正常人员
            [300, 150, 400, 350, 0.92, "fallperson"],  # 跌倒人员
            [500, 200, 600, 400, 0.78, "person"],      # 另一个正常人员
        ]
        
        test_data = {
            "businessData": {
                "imageType": "base64",
                "advsetValue": {
                    "fallPerson": "all",
                    "recvDataType": ["infer_results", "draw_image", "infer_boxes"]
                }
            }
        }
        
        # 运行后处理
        result_json, post_bool = postprocessor.run_process(test_data, test_boxes, test_img)
        
        print(f"✅ all模式测试完成")
        print(f"  - 是否触发报警: {post_bool}")
        print(f"  - 检测到的消息类型: {[v['type'] for v in result_json.get('values', [])]}")
        print(f"  - person检测框数量: {len(result_json.get('boxes', {}).get('person', []))}")
        print(f"  - fallperson检测框数量: {len(result_json.get('boxes', {}).get('fallperson', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ all模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_person_mode():
    """测试person模式"""
    print("\n🔍 测试person模式")
    print("=" * 50)
    
    try:
        from postprocess.post_process import PostProcess
        from utils.i18n import I18n
        
        # 创建后处理实例
        i18n = I18n()
        post_mess = ["detect_fallperson", "v2.0.0", "fallperson"]
        postprocessor = PostProcess(i18n, post_mess)
        
        # 创建测试数据
        test_img = create_test_image()
        
        # 模拟检测结果：包含person和fallperson
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],      # 正常人员
            [300, 150, 400, 350, 0.92, "fallperson"],  # 跌倒人员（应该被忽略）
        ]
        
        test_data = {
            "businessData": {
                "imageType": "base64",
                "advsetValue": {
                    "fallPerson": "person",
                    "recvDataType": ["infer_results", "draw_image", "infer_boxes"]
                }
            }
        }
        
        # 运行后处理
        result_json, post_bool = postprocessor.run_process(test_data, test_boxes, test_img)
        
        print(f"✅ person模式测试完成")
        print(f"  - 是否触发报警: {post_bool}")
        print(f"  - 检测到的消息类型: {[v['type'] for v in result_json.get('values', [])]}")
        print(f"  - person检测框数量: {len(result_json.get('boxes', {}).get('person', []))}")
        print(f"  - fallperson检测框数量: {len(result_json.get('boxes', {}).get('fallperson', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ person模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallperson_mode():
    """测试fallperson模式"""
    print("\n🔍 测试fallperson模式")
    print("=" * 50)
    
    try:
        from postprocess.post_process import PostProcess
        from utils.i18n import I18n
        
        # 创建后处理实例
        i18n = I18n()
        post_mess = ["detect_fallperson", "v2.0.0", "fallperson"]
        postprocessor = PostProcess(i18n, post_mess)
        
        # 创建测试数据
        test_img = create_test_image()
        
        # 模拟检测结果：包含person和fallperson
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],      # 正常人员（应该被忽略）
            [300, 150, 400, 350, 0.92, "fallperson"],  # 跌倒人员
        ]
        
        test_data = {
            "businessData": {
                "imageType": "base64",
                "advsetValue": {
                    "fallPerson": "fallperson",
                    "recvDataType": ["infer_results", "draw_image", "infer_boxes"]
                }
            }
        }
        
        # 运行后处理
        result_json, post_bool = postprocessor.run_process(test_data, test_boxes, test_img)
        
        print(f"✅ fallperson模式测试完成")
        print(f"  - 是否触发报警: {post_bool}")
        print(f"  - 检测到的消息类型: {[v['type'] for v in result_json.get('values', [])]}")
        print(f"  - person检测框数量: {len(result_json.get('boxes', {}).get('person', []))}")
        print(f"  - fallperson检测框数量: {len(result_json.get('boxes', {}).get('fallperson', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ fallperson模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_drawing():
    """测试颜色绘制"""
    print("\n🔍 测试颜色绘制")
    print("=" * 50)
    
    try:
        from postprocess.utils.utils import draw_image_boxes
        
        # 创建测试图像
        test_img = create_test_image()
        
        # 测试检测框
        test_boxes = [
            [100, 100, 200, 300, 0.85, "person"],      # 绿色框
            [300, 150, 400, 350, 0.92, "fallperson"],  # 红色框
        ]
        
        # 绘制检测框
        result_img = draw_image_boxes(test_img.copy(), test_boxes)
        
        print("✅ 颜色绘制测试完成")
        print("  - person类别使用绿色框")
        print("  - fallperson类别使用红色框")
        print("  - 图像绘制成功")
        
        # 保存测试图像（可选）
        # cv2.imwrite("test_fallperson_colors.jpg", result_img)
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色绘制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 人员跌倒算法报警逻辑测试")
    print("=" * 70)
    
    tests = [
        ("all模式测试", test_all_mode),
        ("person模式测试", test_person_mode),
        ("fallperson模式测试", test_fallperson_mode),
        ("颜色绘制测试", test_color_drawing),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！报警逻辑修改成功")
        print("\n📋 修改总结:")
        print("  ✅ all模式：检测到person或fallperson都会报警，颜色区分")
        print("  ✅ person模式：只处理person类别，检测到就报警")
        print("  ✅ fallperson模式：只处理fallperson类别，检测到就报警")
        print("  ✅ 画框颜色：person=绿色，fallperson=红色")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
