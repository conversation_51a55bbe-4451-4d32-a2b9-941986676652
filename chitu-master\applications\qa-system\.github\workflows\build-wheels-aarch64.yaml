name: build-wheels-aarch64

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

env:
  SHERPA_ONNX_IS_IN_GITHUB_ACTIONS: 1

concurrency:
  group: build-wheels-aarch64-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_wheels_aarch64:
    name: ${{ matrix.manylinux }} ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        python-version: ["cp37", "cp38", "cp39", "cp310", "cp311", "cp312"]
        manylinux: [manylinux2014] #, manylinux_2_28]

    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: all

      # see https://cibuildwheel.readthedocs.io/en/stable/changelog/
      # for a list of versions
      - name: Build wheels
        uses: pypa/cibuildwheel@v2.16.5
        env:
          CIBW_BEFORE_ALL: |
            git clone --depth 1 https://github.com/alsa-project/alsa-lib
            cd alsa-lib
            ./gitcompile
            cd ..
            echo "PWD"
            ls -lh /project/alsa-lib/src/.libs

          CIBW_ENVIRONMENT: CPLUS_INCLUDE_PATH=/project/alsa-lib/include:$CPLUS_INCLUDE_PATH SHERPA_ONNX_ALSA_LIB_DIR=/project/alsa-lib/src/.libs LD_LIBRARY_PATH=/project/build/bdist.linux-x86_64/wheel/sherpa_onnx/lib:$SHERPA_ONNX_ALSA_LIB_DIR SHERPA_ONNX_MAKE_ARGS="VERBOSE=1" SHERPA_ONNX_ENABLE_ALSA=1
          CIBW_BUILD: "${{ matrix.python-version}}-* "
          CIBW_SKIP: "cp27-* cp35-* cp36-* *-win32 pp* *-musllinux* *-manylinux_i686"
          CIBW_BUILD_VERBOSITY: 3
          CIBW_ARCHS_LINUX: aarch64
          CIBW_MANYLINUX_AARCH64_IMAGE: quay.io/pypa/${{ matrix.manylinux }}_aarch64
          # From onnxruntime >= 1.17.0, it drops support for CentOS 7.0 and it supports only manylinux_2_28.
          # manylinux_2_24 is no longer supported

      - name: Display wheels
        shell: bash
        run: |
          ls -lh ./wheelhouse/

      - name: Install patchelf
        if: matrix.os == 'ubuntu-latest'
        shell: bash
        run: |
          sudo apt-get update -q
          sudo apt-get install -q -y patchelf
          patchelf --help

      - name: Patch wheels
        shell: bash
        if: matrix.os == 'ubuntu-latest'
        run: |
          mkdir ./wheels
          sudo ./scripts/wheel/patch_wheel.py --in-dir ./wheelhouse --out-dir ./wheels

          ls -lh ./wheels/
          rm -rf ./wheelhouse
          mv ./wheels ./wheelhouse

      - name: Publish to huggingface
        if: (matrix.python-version == 'cp38' || matrix.python-version == 'cp39' ) && matrix.manylinux == 'manylinux2014'
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/csukuangfj/sherpa-onnx-wheels huggingface
            cd huggingface
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../wheelhouse/*.whl .

            git status
            git add .
            git commit -m "add more wheels"
            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-wheels main

      - uses: actions/upload-artifact@v4
        with:
          name: wheel-${{ matrix.python-version }}-${{ matrix.manylinux }}
          path: ./wheelhouse/*.whl

      - name: Publish wheels to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install wheel twine setuptools

          twine upload ./wheelhouse/*.whl
