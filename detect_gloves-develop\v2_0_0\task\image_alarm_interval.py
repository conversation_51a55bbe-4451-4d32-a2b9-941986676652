import time 


def contrast_time(interval_image, transaction_number, interval):
    if transaction_number not in interval_image:
        interval_image.update({transaction_number: 0})
    time_now = int(time.time())
    residual = time_now - interval_image[transaction_number]
    if residual > interval:
        interval_image[transaction_number] = int(time.time())
        return True, interval_image
    else:
        return False, interval_image

    