import datetime
from applications.extensions import db

correlation_inter = db.Table(
    "correlation_inter", # 中间表名称
    db.Column("main_id", db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>("admin_digital_human_interactive.id", ondelete='CASCADE'), primary_key=True),
    db.<PERSON>umn("correlation_id", db.Integer, db.<PERSON><PERSON>("admin_digital_human_interactive.id", ondelete='CASCADE'), primary_key=True),
)

class digital_human_interactive(db.Model):
    __tablename__ = 'admin_digital_human_interactive'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键')
    question = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='问题')
    answer = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='答案')
    interactive_video = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='交互视频')
    questionId = db.Column(db.Integer, nullable=True,primary_key=False, comment='问题ID')
    enabled = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='是否启用')
    similarQuestionList = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='相似问题')
    videoDuration = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='视频持续时间')
    fileType = db.Column(db.CHAR(1), nullable=True,primary_key=False, comment='答案附件的类型，0：图片；1：视频；空字符串表示无附件')
    fileUrl = db.Column(db.CHAR(100), nullable=True,primary_key=False, comment='答案附件地址')
    
    correlations = db.relationship("digital_human_interactive", 
                                   secondary = "correlation_inter", 
                                   primaryjoin=id == correlation_inter.c.main_id,
                                   secondaryjoin=id == correlation_inter.c.correlation_id,
                                   backref=db.backref('related_to', lazy='dynamic'))

    def json(self):
        return {
            'id': self.id,
            'question': self.question,
            'answer': self.answer,
            'interactive_video': self.interactive_video,
            'questionId': self.questionId,
            'enabled': self.enabled,
            'similarQuestionList': self.similarQuestionList,
            'videoDuration': self.videoDuration,
            'fileType': self.fileType,
            'fileUrl': self.fileUrl
        }