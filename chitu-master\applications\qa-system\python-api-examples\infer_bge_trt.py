from optimum.onnxruntime import ORTModelForFeatureExtraction
from transformers import AutoTokenizer, AutoModel
import torch
import time
import argparse
import pandas as pd


def qa_pairs_extract(qa_file):
    file_path = qa_file
    df = pd.read_excel(file_path)  
    column_data = df['问题'].tolist()  
    return column_data


def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--qa_pairs",
        type=str,
        required=True,
        help="Path to qa.xlsx",
    )
    
    parser.add_argument(
        "--user_query",
        type=str,
        required=True,
        help="user query",
    )

    return parser.parse_args()


def main():
    args = get_args()
    sentences_1 = qa_pairs_extract(args.qa_pairs)
    sentences_2 = [args.user_query]
    tokenizer = AutoTokenizer.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
    model = AutoModel.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
    model.eval()

    # Tokenize sentences
    encoded_input_1 = tokenizer(sentences_1, padding=True, truncation=True, return_tensors='pt')
    encoded_input_2 = tokenizer(sentences_2, padding=False, truncation=True, return_tensors='pt')
    # Compute token embeddingsa
    """
    dynamic_axes = {  
            'input_ids': {0: 'batch_size', 1: 'sequence_length'},  
            'attention_mask': {0: 'batch_size', 1: 'sequence_length'},  
            'token_type_ids': {0: 'batch_size', 1: 'sequence_length'},
            'output': {0: 'batch_size', 1: 'sequence_length'}  # 庠¹彍®似 潚~D轾S佇º课C录´达Y个  
            } 
    #(encoded_input_1["input_ids"],encoded_input_1["token_type_ids"],encoded_input_1["attention_mask"])
    
    torch.onnx.export(model,(encoded_input_1["input_ids"],encoded_input_1["token_type_ids"],encoded_input_1["attention_mask"]),"/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5/onnx/model.onnx", export_params=True,
                opset_version=14,
                do_constant_folding=False,
                input_names=['input_ids', 'token_type_ids','attention_mask'],
                output_names=['output'],
                dynamic_axes=dynamic_axes)
    """
    print(type(encoded_input_2))
    model_ort = ORTModelForFeatureExtraction.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5', revision="refs/pr/13",file_name="onnx/model.onnx")

    model_output_1 = model_ort(encoded_input_1["input_ids"],encoded_input_1["token_type_ids"],encoded_input_1["attention_mask"])
    print(time.time())
    start_time = time.time()
    model_output_2 = model_ort(encoded_input_2["input_ids"],encoded_input_2["token_type_ids"],encoded_input_2["attention_mask"])
    # Perform pooling. In this case, cls pooling.
    sentence_embeddings_1 = model_output_1[0][:, 0]
    sentence_embeddings_2 = model_output_2[0][:, 0]
    # normalize embeddings
    #sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
    #print("Sentence embeddings:", sentence_embeddings)
    scores = sentence_embeddings_1 @ sentence_embeddings_2.T
    print(scores)
    max_value = max(scores.view(-1).tolist())
    if max_value > 30:
        max_index = max(enumerate(scores), key=lambda x: x[1][0])[0]
        print(max_value)
        print(scores)
        print(sentences_2)
        print(sentences_1[max_index])
        print(time.time()-start_time)


if __name__ == "__main__":
    
    main()
