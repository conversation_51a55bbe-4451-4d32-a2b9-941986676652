name: build-wheels-win32

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

env:
  SHERPA_ONNX_IS_IN_GITHUB_ACTIONS: 1

concurrency:
  group: build-wheels-win32-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_wheels_win32:
    name: ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        python-version: ["cp37", "cp38", "cp39", "cp310", "cp311", "cp312"]

    steps:
      - uses: actions/checkout@v4

      # see https://cibuildwheel.readthedocs.io/en/stable/changelog/
      # for a list of versions
      - name: Build wheels
        uses: pypa/cibuildwheel@v2.16.5
        env:
          CIBW_ENVIRONMENT: SHERPA_ONNX_CMAKE_ARGS="-A Win32"
          CIBW_BUILD: "${{ matrix.python-version}}-* "
          CIBW_SKIP: "*-win_amd64"
          CIBW_BUILD_VERBOSITY: 3

      - name: Display wheels
        shell: bash
        run: |
          ls -lh ./wheelhouse/

      - uses: actions/upload-artifact@v4
        with:
          name: wheel-${{ matrix.python-version }}
          path: ./wheelhouse/*.whl

      - name: Publish to huggingface
        if: matrix.python-version == 'cp38'
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/csukuangfj/sherpa-onnx-wheels huggingface
            cd huggingface
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../wheelhouse/*.whl .

            git status
            git add .
            git commit -m "add more wheels"
            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-wheels main

      - name: Publish wheels to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install wheel twine setuptools

          twine upload ./wheelhouse/*.whl
