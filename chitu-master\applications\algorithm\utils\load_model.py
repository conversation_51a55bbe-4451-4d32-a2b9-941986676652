import os
from applications.extensions import db
from applications.models.admin_algorithm import Algorithm, Weights
from applications.algorithm.utils.algo_utils import support_model_type, models

def loadmodel(weight_path, model_type):
    if model_type in support_model_type:
        if model_type == "rknn":
            
            try:
                from rknnlite.api import RKNNLite
            except:
                return {"msg": "rknnlite导包失败，请联系售后处理"}
            
            try:
                rknn_lite = RKNNLite()
                rknn_lite.load_rknn(weight_path)
                rknn_lite.init_runtime()
            except:
                return {"msg": "模型加载失败，请联系售后处理"}
        
            return {"msg": "success", "model": rknn_lite}

    else:
        return {"msg": "不支持的权重类型，请联系售后处理"}


def init_load_models():
    open_algo = Algorithm.query.filter_by(status=True).group_by(Algorithm.weight).all()
    for tmp in open_algo:
        weight = Weights.query.filter_by(id=tmp.weight).first()
        if len(eval(weight.name)):
            path = weight.path
            weight_type = weight.type
            tmp_model = []
            for w in eval(weight.name):
                res = loadmodel(os.path.join(path, w), weight_type)
                if res["msg"] == "success":
                    tmp_model.append(res["model"])
                else:
                    return res
            models[tmp.weight] = tmp_model
        
        Weights.query.filter_by(id=tmp.weight).update({"status": True})
        db.session.commit()