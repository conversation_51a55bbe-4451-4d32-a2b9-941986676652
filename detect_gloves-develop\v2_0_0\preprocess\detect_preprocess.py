import numpy as np
import cv2
import base64
import io
from .utils.preprocess import PreProcess
from ..utils.log import mylog

class DetectPreProcess(PreProcess):
    
    def __init__(self):
        super().__init__()
        mylog.info('加载算法图片前处理模块')
        
    def base64_image(self, data_value, new_wh=(640, 640)):
        # 注意: 如果你的base64字符串是从网络或其他途径获取的，可能需要去掉头部信息如'data:image/jpeg;base64,'等
        # data:image/jpeg;base64,

        image_bytes = base64.b64decode(data_value)
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        img, gain = super().letterbox(image, new_wh)
        return img, image, gain
        
    def byte_image(self, data_value, new_wh=(640, 640)):
        image_array = cv2.imdecode(np.frombuffer(data_value, np.uint8), cv2.IMREAD_COLOR)
        img, gain = super().letterbox(image_array, new_wh)
        return img, image_array, gain

    def get_data(self, data):
        businessData = data['businessData']
        if businessData["imageType"] == "base64":
            image, source_img, gain = self.base64_image(businessData["image"])
        
        if businessData["imageType"] == "byte":
            image, source_img, gain = self.byte_image(businessData["image"])
        return image, source_img,  gain

