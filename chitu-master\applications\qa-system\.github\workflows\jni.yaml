name: jni

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/jni.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'kotlin-api-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/jni/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/jni.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'kotlin-api-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/jni/*'

  workflow_dispatch:

concurrency:
  group: jni-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  jni:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}

      - name: Display kotlin version
        shell: bash
        run: |
          kotlinc -version

      - name: Display java version
        shell: bash
        run: |
          java -version
          echo "JAVA_HOME is: ${JAVA_HOME}"

      - name:  Run JNI test
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          cd ./kotlin-api-examples
          ./run.sh
