import os
import time
import datetime
import schedule
import subprocess

from log import mylog
from applications.digital_human.common.common_utils import read_private_key, rsa_decryption, read_config

qapath = "applications/qa-system/python-api-examples"

def start_qa_service():
    need_msg = False
    res = check_qa_service()
    conf = read_config()
    mode = conf["currentMode"]
    if res == 1 and mode != "SentryMode":
        cur_dir = os.getcwd()
        os.chdir(qapath)
        subprocess.Popen(['bash', 'qa.sh'])
        os.chdir(cur_dir)
        mylog.info("算法服务正在启动....")
        need_msg = True
    return need_msg

def check_qa_service():
    current_time = int(time.time())
    try:
        recv_end_date = read_config()["servicePeriod"]
    except:
        mylog.info("未配置服务到期时间.....")
    
    end_date = rsa_decryption(recv_end_date, read_private_key())
    end_time = int(datetime.datetime.strptime(end_date + " 23:59:59", "%Y-%m-%d %H:%M:%S").timestamp())
    remain_day = int((end_time - current_time) / 86400)
    mylog.info("服务剩余时间为：" + str(remain_day)+"天。")
    if end_time - current_time <= 0 :
        return -1
    else:
        return 1
    
def stop_qa_service():
    res = check_qa_service()
    if res == -1:
        mylog.info('语音服务停止......')
        cur_dir = os.getcwd()
        os.chdir(qapath)
        subprocess.Popen(['bash', 'stop_qa.sh'])
        os.chdir(cur_dir)
        
def stop_qa_service_force():
    mylog.info('语音服务停止......')
    cur_dir = os.getcwd()
    os.chdir(qapath)
    subprocess.Popen(['bash', 'stop_qa.sh'])
    os.chdir(cur_dir)
    
    
def do_check():
    while True:
        schedule.run_pending()
        time.sleep(1)