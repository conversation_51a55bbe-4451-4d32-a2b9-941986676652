{"modelName": "detect_fallperson", "modelNameEn": "model for fall person detection", "modelNameZh": "人员跌倒检测模型", "modelVersion": "v2.0.0", "algoSet": [{"algoName": "fallperson", "algoNameEn": "alarm when people falling", "algoNameZh": "人员跌倒检测算法", "dataType": "image", "param": {"transactionNumber": "", "callBackData": "", "businessData": {"image": {"paramType": "String", "value": ""}, "imageType": {"paramType": "String", "value": "base64"}, "imageId": {"paramType": "number", "value": ""}, "advsetValue": {"area": {"areaType": {"display": false, "paramType": "String", "readType": "area", "required": true, "readonly": false, "options": [{"label": {"zh": "多边形", "en": "Polygn"}, "value": "POLYGON"}], "value": "POLYGON"}, "positions": {"display": false, "paramType": "List<List<float32>>", "readType": "area", "required": true, "readonly": false, "value": []}}, "interval": {"display": false, "paramType": "number", "readType": "inputNumber", "required": true, "readonly": false, "label": {"zh": "报警间隔(秒)", "en": "Alarm Interval(s)"}, "value": 30}, "fallPerson": {"display": false, "paramType": "String", "readType": "select", "required": true, "readonly": false, "label": {"zh": "检测方式", "en": "Check The Way"}, "value": "fallperson", "options": [{"label": {"zh": "全部", "en": "All"}, "value": "all"}, {"label": {"zh": "人员跌倒", "en": "Someone fall person."}, "value": "fallperson"}, {"label": {"zh": "非人员跌倒", "en": "Someone unfallperson."}, "value": "person"}]}, "recvDataType": {"display": false, "paramType": "number", "readType": "check", "required": true, "readonly": false, "label": {"zh": "结果输出类型", "en": "response type"}, "value": ["infer_results", "draw_image", "source_image", "infer_boxes"], "options": [{"label": {"zh": "推理结果", "en": "inference result"}, "value": "infer_results"}, {"label": {"zh": "绘制后图片", "en": "Drawn image"}, "value": "draw_image"}, {"label": {"zh": "原始图片", "en": "original image"}, "value": "source_image"}, {"label": {"zh": "Box信息", "en": "Box message"}, "value": "infer_boxes"}]}}}}}]}