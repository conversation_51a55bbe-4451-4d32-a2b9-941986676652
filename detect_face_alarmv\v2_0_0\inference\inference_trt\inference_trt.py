import os
import cv2
import yaml
import numpy as np
from .utils_trt.utils import non_max_suppression, letterbox, get_draw_box
from .utils_trt.host_device_mem import allocate_buffers, predict, get_engine


class InferenceTrt(object):
    def __init__(self, label, weight_path, model_name):
        cfg_path = "config/algorithm_inference.yaml"
        if os.path.exists(cfg_path):  # 判断文件是否存在，不存在就直接使用默认参数
            with open(cfg_path, "rb") as f:
                cfg = yaml.load(f, yaml.FullLoader)
            cfg_key = cfg.keys()
            if "transform_wh" in cfg_key:
                self.wh = cfg["transform_wh"]
            else:
                self.wh = [640, 640]

            if "confidence" in cfg_key:
                self.confidence = cfg["confidence"]
            else:
                self.confidence = 0.6

            if "nms_threshold" in cfg_key:
                self.nms_threshold = cfg["nms_threshold"]
            else:
                self.nms_threshold = 0.45
        else:
            self.wh = [640, 640]
            self.confidence = 0.6
            self.nms_threshold = 0.45

        self.label = label
        self.engine = get_engine('weight/{}.engine'.format(model_name))
        self.context = self.engine.create_execution_context()
        self.inputs, self.outputs, self.bindings, self.stream = allocate_buffers(self.engine, 1)
        self.context.set_binding_shape(0, (1, 3, self.wh[1], self.wh[0]))  # 1, 3, h, w

    def run(self, img_dict, gain, original_image_shape=None):
        """兼容新接口的run方法"""
        # 如果传入的是单个图像而不是字典，转换为字典格式
        if not isinstance(img_dict, dict):
            img_dict = {"default": img_dict}

        draw_boxes = {}
        for key in img_dict.keys():
            img, gain = letterbox(img_dict[key], self.wh)
            img_color = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_trans = np.transpose(img_color, (2, 0, 1)).astype(np.float32)
            img_dims = np.expand_dims(img_trans, axis=0)
            img_dims /= 255.0
            image = np.ascontiguousarray(img_dims)
            self.inputs[0].host = image
            trt_outputs = predict(self.context, bindings=self.bindings, inputs=self.inputs,
                                    outputs=self.outputs, stream=self.stream)
            trt_out = trt_outputs[0].reshape(1, -1, 13)
            trt_nms = non_max_suppression(trt_out, self.confidence, self.nms_threshold)
            draw_boxes[key] = get_draw_box(trt_nms[0], gain, self.label)

        # 如果输入是单个图像，返回单个结果
        if "default" in draw_boxes:
            return draw_boxes["default"]
        return draw_boxes


