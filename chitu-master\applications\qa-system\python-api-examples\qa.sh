#kill 之前死进程
ps -aux |grep qa.py | awk '{print$2}' | xargs -i kill -9 {}
cd applications/qa-system/python-api-examples/
source ../../../../../workspace/venv/bin/activate
#启动问答系统服务
#python3 qa.py  --tokens=./../sherpa-onnx-streaming-zipformer-small-bilingual-zh-en-2023-02-16/tokens.txt --encoder=./../sherpa-onnx-streaming-zipformer-small-bilingual-zh-en-2023-02-16/encoder-epoch-99-avg-1.onnx --decoder=./../sherpa-onnx-streaming-zipformer-small-bilingual-zh-en-2023-02-16/decoder-epoch-99-avg-1.onnx --joiner=./../sherpa-onnx-streaming-zipformer-small-bilingual-zh-en-2023-02-16/joiner-epoch-99-avg-1.onnx --decoding-method=modified_beam_search --hotwords-file=hotwords_file --hotwords-score=1.5 
#--decoding-method=modified_beam_search
python3 qa.py  --tokens=/mnt/keep/workspace/digital_human_data/speech_files/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/tokens.txt --encoder=/mnt/keep/workspace/digital_human_data/speech_files/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/encoder-epoch-99-avg-1.onnx --decoder=/mnt/keep/workspace/digital_human_data/speech_files/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/decoder-epoch-99-avg-1.onnx --joiner=/mnt/keep/workspace/digital_human_data/speech_files/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.onnx --decoding-method=modified_beam_search --hotwords-file=hotwords_file --hotwords-score=1.5

