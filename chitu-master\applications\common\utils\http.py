from flask import jsonify


def success_api( msg: str = "成功",code: int = 200,data = None):
    """ 成功响应 默认值“成功” """
    return jsonify(success=True, msg=msg, code=code, data=data)


def fail_api(msg: str = "失败",code: int = 400):
    """ 失败响应 默认值“失败” """
    return jsonify(success=False, msg=msg, code=code)


def table_api(msg: str = "操作成功", data=None,  count=0, current = 1, pageSize=10, code: int = 200):
    """ 动态表格渲染响应 """
    res = {
        'msg': msg,
        'code': code,
        'data': data,
        'count': count,
        "current":current,
        'pageSize': pageSize
    }
    return jsonify(res)

def algo_table_api(msg: str = "操作成功", data=None,  total=0, current = 1, pageSize=10, code: int = 200):
    """ 动态表格渲染响应 """
    res = {
        'msg': msg,
        'code': code,
        'data': data,
        'total': total,
        "current":current,
        'pageSize': pageSize
    }
    return jsonify(res)

def algo_table_api(msg: str = "操作成功", data=None,  total=0, current = 1, pageSize=10, code: int = 200):
    """ 动态表格渲染响应 """
    res = {
        'msg': msg,
        'code': code,
        'data': data,
        'total': total,
        "current":current,
        'pageSize': pageSize
    }
    return jsonify(res)
