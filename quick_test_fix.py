#!/usr/bin/env python3
"""
快速测试修复后的人脸算法
"""

import sys
import os
from pathlib import Path

def test_file_structure():
    """测试文件结构"""
    print("🔍 检查文件结构...")
    
    required_files = [
        'detect_face_alarm/v2_0_0/utils/face_recognition.py',
        'detect_face_alarm/v2_0_0/preprocess/detect_preprocess.py',
        'detect_face_alarm/v2_0_0/inference/inference_rknn/inference_rknn.py',
        'detect_face_alarm/v2_0_0/inference/inference_rknn/utils_rknn/utils.py',
        'detect_face_alarm/v2_0_0/postprocess/post_process.py',
        'detect_face_alarm/v2_0_0/postprocess/utils/boxes_iou.py',
        'detect_face_alarm/v2_0_0/config/documentation.json'
    ]
    
    all_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_exist = False
    
    return all_exist

def test_utils_content():
    """测试utils.py内容"""
    print("\n🔍 检查utils.py内容...")
    
    utils_path = Path('detect_face_alarm/v2_0_0/inference/inference_rknn/utils_rknn/utils.py')
    if not utils_path.exists():
        print("❌ utils.py不存在")
        return False
    
    try:
        with open(utils_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = ['predict', 'get_draw_box', 'load_model', 'xywh2xyxy', 'nms_boxes']
        
        for func in required_functions:
            if f'def {func}(' in content:
                print(f"✅ 函数 {func} 存在")
            else:
                print(f"❌ 函数 {func} 缺失")
                return False
        
        # 检查文件编码是否正常
        if content.startswith('import cv2'):
            print("✅ 文件编码正常")
        else:
            print("❌ 文件编码异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取utils.py失败: {e}")
        return False

def test_postprocess_content():
    """测试后处理内容"""
    print("\n🔍 检查后处理内容...")
    
    postprocess_path = Path('detect_face_alarm/v2_0_0/postprocess/post_process.py')
    if not postprocess_path.exists():
        print("❌ post_process.py不存在")
        return False
    
    try:
        with open(postprocess_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_methods = ['disassemble_recv_data_type', 'disassemble_algo_type', 
                          'disassemble_advset_value', 'run_process']
        
        for method in required_methods:
            if f'def {method}(' in content:
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 缺失")
                return False
        
        # 检查是否有标准的导入
        if 'from .utils.boxes_iou import polygon_IOU' in content:
            print("✅ 标准导入存在")
        else:
            print("❌ 标准导入缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取post_process.py失败: {e}")
        return False

def test_config_content():
    """测试配置文件内容"""
    print("\n🔍 检查配置文件内容...")
    
    config_path = Path('detect_face_alarm/v2_0_0/config/documentation.json')
    if not config_path.exists():
        print("❌ documentation.json不存在")
        return False
    
    try:
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = ['modelName', 'modelVersion', 'algoSet']
        for key in required_keys:
            if key in config:
                print(f"✅ 配置项 {key} 存在")
            else:
                print(f"❌ 配置项 {key} 缺失")
                return False
        
        # 检查人脸检测配置
        if len(config['algoSet']) > 0:
            algo = config['algoSet'][0]
            if 'faceDetect' in str(algo):
                print("✅ 人脸检测配置存在")
            else:
                print("❌ 人脸检测配置缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_import_fixes():
    """测试导入修复"""
    print("\n🔍 检查导入修复...")
    
    # 检查face_recognition.py的BasePlugin修复
    face_recognition_path = Path('detect_face_alarm/v2_0_0/utils/face_recognition.py')
    if face_recognition_path.exists():
        with open(face_recognition_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'class BasePlugin:' in content:
            print("✅ BasePlugin类定义存在")
        else:
            print("❌ BasePlugin类定义缺失")
            return False
    
    # 检查预处理的导入修复
    preprocess_path = Path('detect_face_alarm/v2_0_0/preprocess/utils/preprocess.py')
    if preprocess_path.exists():
        with open(preprocess_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'sys.path.append' in content:
            print("✅ 预处理导入修复存在")
        else:
            print("❌ 预处理导入修复缺失")
            return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 快速测试修复后的人脸算法")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("utils.py内容", test_utils_content),
        ("后处理内容", test_postprocess_content),
        ("配置文件内容", test_config_content),
        ("导入修复", test_import_fixes),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有修复验证通过！")
        print("\n📋 修复完成的内容:")
        print("  ✅ utils.py文件编码问题已修复")
        print("  ✅ 后处理工具文件已添加")
        print("  ✅ 导入路径问题已修复")
        print("  ✅ BasePlugin依赖问题已解决")
        print("  ✅ 文件结构完整")
        
        print("\n🚀 下一步可以:")
        print("  1. 运行实际的算法测试")
        print("  2. 使用真实图像验证检测效果")
        print("  3. 调试任何剩余的运行时问题")
    else:
        print("⚠️ 部分修复验证失败，需要进一步处理")

if __name__ == "__main__":
    main()
