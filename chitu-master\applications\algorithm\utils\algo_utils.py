import json
import os
import sys
from applications.models.admin_algorithm import Weights
from applications.algorithm.utils.truelist import TrueList

truelist = TrueList()
models = {}
weights = {}

with open("applications/algorithm/config.json", "r", encoding = 'utf-8') as f:
    conf = json.load(f)

videocapTimes = conf["videocap"]["videocapTimes"]
videocapGap = conf["videocap"]["videocapGap"]
drawframeRate = conf["videocap"]["drawframeRate"]

download_path = conf["install"]["download_path"]
support_model_type = conf["install"]["support_model_type"]
models_path = conf["install"]["models_path"]

alarm_img_path = conf["alarm_img_path"]
alarm_interval = int(conf["alarm_interval"])

def re_read_weights():
    global weights
    weights = {}
    for item in Weights.query.all():
        weights[item.id] = item

def get_infer_name(id):
    name = '' 
    path = weights[id].path
    for tmp in path.split('/'):
        name = name + tmp + '.'
    name = name + weights[id].infer_file[:-3]
    return name

def init_environment():
    if not os.path.exists(download_path):
        os.makedirs(download_path)

    if not os.path.exists(models_path):
        os.makedirs(models_path)  

    if not os.path.exists(alarm_img_path):
        os.makedirs(alarm_img_path)
    
    if os.path.exists("applications/algorithm/screen/test_pid.txt"):
        os.remove("applications/algorithm/screen/test_pid.txt")
    
    for tmp in os.listdir(models_path):
        sys.path.append(os.path.join(models_path, tmp))
        
init_environment()