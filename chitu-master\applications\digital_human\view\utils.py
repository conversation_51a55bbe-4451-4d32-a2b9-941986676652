import json
from log import mylog
from applications.websocket.websocket_server import ws

def ws_send_instruct_algo(msg):
    if "algorithm" in ws.client.keys():
        ws.send_msg(ws.client["algorithm"], msg)
    else:
        mylog.info("WEBSOCKET：收到机器人指令，但是算法客户端不在线。")

def ws_send_screen_msg(msg):
    if "screen" in ws.client.keys():
        ws.send_msg(ws.client["screen"], msg)
        mylog.info("WEBSOCKET：向前端发送消息 {}".format(str(msg)))
    else:
        mylog.info("WEBSOCKET：发送消息给前端时，前端客户端不在线。")
        
def ws_send_algo_msg(msg):
    if "algorithm" in ws.client.keys():
        ws.send_msg(ws.client["algorithm"], str(json.dumps(msg)))
    else:
        mylog.info("WEBSOCKET：收到平台消息，但是算法客户端不在线。")
        
def get_ws_client_keys():
    return ws.client.keys()