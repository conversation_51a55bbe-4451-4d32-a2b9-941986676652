from collections import deque
from threading import Timer, Lock
import time

class MessageQueue:
    def __init__(self, limit_per_minute, send_function):
        self.queue = deque()
        self.lock = Lock()
        self.limit_per_minute = limit_per_minute
        self.send_function = send_function
        self.messages_sent = 0
        self.start_time = time.time()

    def add_message(self, content, topic, webhook_url, template_type):
        print("add message")
        with self.lock:
            self.queue.append((content, topic, webhook_url, template_type))
            print("append ok")
            print(self.queue)
            self.process_queue()
            print("task start")

    def process_queue(self):

        # with self.lock:
 
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        if elapsed_time > 60:
            self.messages_sent = 0
            self.start_time = current_time

        if self.messages_sent < self.limit_per_minute:
            if len(self.queue) > 0:
                print("发送消息")
                content, topic, webhook_url, template_type = self.queue.popleft()
                self.send_function(content, topic, webhook_url, template_type)
                print("发送完成")
                self.messages_sent += 1
        print(2222)
        if len(self.queue) > 0:
            Timer(1, self.process_queue).start()

    def combine_messages(self):
        with self.lock:
            combined_content = ""
            combined_topic = ""
            for _ in range(min(len(self.queue), self.limit_per_minute)):
                content, topic, webhook_url, template_type = self.queue.popleft()
                combined_content += f"{content}\n"
                combined_topic += f"{topic}\n"
            
            if combined_content:
                self.send_function(combined_content.strip(), combined_topic.strip(), webhook_url, '2')
        self.schedule_combine()
    def schedule_combine(self):
        Timer(60, self.combine_messages).start()
