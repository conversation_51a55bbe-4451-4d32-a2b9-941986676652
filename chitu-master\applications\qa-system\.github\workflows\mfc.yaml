name: mfc

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/mfc.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'mfc-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/mfc.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'mfc-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'

  workflow_dispatch:

concurrency:
  group: mfc-${{ github.ref }}
  cancel-in-progress: true

jobs:
  mfc:
    name: MFC for ${{ matrix.arch }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        arch: [x64, x86]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Display MSBuild info
        shell: cmd
        run: |
          set path="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin"
          msbuild -help

      - name: Configure CMake
        shell: bash
        run: |
          mkdir build
          cd build
          arch=${{ matrix.arch }}
          if [[ $arch == "x86" ]]; then
            arch=Win32
          fi
          cmake -A $arch -D CMAKE_BUILD_TYPE=Release -D BUILD_SHARED_LIBS=OFF -DCMAKE_INSTALL_PREFIX=./install ..

      - name: Build sherpa-onnx for windows
        shell: bash
        run: |
          cd build
          cmake --build . --config Release -- -m:2
          cmake --build . --config Release --target install -- -m:2

          ls -lh install/*

          ls -lh install/lib
          ls -lh install/bin

      - name: Build MFC
        shell: cmd
        run: |
          set path="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin"

          cd mfc-examples

          msbuild .\mfc-examples.sln /property:Configuration=Release /property:Platform=${{ matrix.arch }}

      - name: Copy files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)
          arch=${{ matrix.arch }}
          if [[ $arch == "x86" ]]; then
            src=mfc-examples/Release
            ls -h $src
            dst=mfc-examples/$arch/Release

            mkdir -p $dst
            cp $src/* $dst
          fi

          cd mfc-examples/$arch/Release
          ls -lh

          cp -v StreamingSpeechRecognition.exe sherpa-onnx-streaming-asr-$arch-${SHERPA_ONNX_VERSION}.exe
          cp -v NonStreamingSpeechRecognition.exe sherpa-onnx-non-streaming-asr-$arch-${SHERPA_ONNX_VERSION}.exe
          cp -v NonStreamingTextToSpeech.exe ../sherpa-onnx-non-streaming-tts-$arch-${SHERPA_ONNX_VERSION}.exe
          ls -lh

      - name: Upload artifact tts
        uses: actions/upload-artifact@v4
        with:
          name: non-streaming-tts-${{ matrix.arch }}
          path: ./mfc-examples/${{ matrix.arch }}/sherpa-onnx-non-streaming-tts-*.exe

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: streaming-speech-recognition-${{ matrix.arch }}
          path: ./mfc-examples/${{ matrix.arch }}/Release/sherpa-onnx-streaming-asr-*.exe

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: non-streaming-speech-recognition-${{ matrix.arch }}
          path: ./mfc-examples/${{ matrix.arch }}/Release/sherpa-onnx-non-streaming-asr-*.exe

      - name: Release pre-compiled binaries and libs for Windows ${{ matrix.arch }}
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: ./mfc-examples/${{ matrix.arch }}/Release/sherpa-onnx-streaming-*.exe

      - name: Release pre-compiled binaries and libs for Windows ${{ matrix.arch }}
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: ./mfc-examples/${{ matrix.arch }}/Release/sherpa-onnx-non-streaming-*.exe

      - name: Release pre-compiled binaries and libs for Windows ${{ matrix.arch }}
        if: github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa' && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: ./mfc-examples/${{ matrix.arch }}/sherpa-onnx-non-streaming-*.exe
