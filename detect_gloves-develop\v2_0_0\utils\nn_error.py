# 错误码定义

from enum import Enum

class NNError(Enum):
    NN_SUCCESS                      = 0       # 成功
    NN_LOAD_MODEL_FAIL              = -1      # 模型加载失败
    NN_RKNN_INIT_FAIL               = -2      # rknn初始化失败
    NN_RKNN_RUNTIME_ERROR           = -3      # rknn运行时错误          
    NN_RKNN_MODEL_NOT_LOAD          = -4      # rknn模型未加载
    NN_STOPED                       = -5      # rknn模型已停止                      
    NN_TIMEOUT                      = -6      # rknn超时
    NN_MODEL_INFERENCE_FAIL         = -7      # 模型推理异常