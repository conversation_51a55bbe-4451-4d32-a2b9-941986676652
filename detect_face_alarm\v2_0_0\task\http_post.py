import requests
import time
import copy
import json
from threading import Thread
from ..utils.log import mylog


class HttpPost(object):
    def __init__(self, post_way):
        self.data = []
        self.post_url = None
        self.post_way = post_way
        mylog.info('加载人脸识别告警模块')      
    
    def post_message(self, message_data):
        """发送告警消息"""
        try:
            if self.post_url:
                if self.post_way == 'base64':
                    requests.post(self.post_url, json=message_data, timeout=10)
                    mylog.info('发送人脸识别告警成功')
                elif self.post_way == 'byte':
                    requests.post(self.post_url, files=message_data, timeout=10)
                    mylog.info('发送人脸识别告警成功')
        except Exception as e:
            mylog.error(f'发送人脸识别告警错误: {e}')
