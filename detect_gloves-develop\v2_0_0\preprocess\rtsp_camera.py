import cv2
import os
from multiprocessing import Process, Queue
from threading import Thread
from threading import Lock
import time
import json
import sys


class RtspCamera(object):
    def __init__(self, lock, rtsp, spaces):
        self.image = []
        self.lock = lock
        self.size_wh = []
        thread_ma = Thread(target=self.thread_manage, args=([rtsp, spaces]), daemon=True)
        thread_ma.start()

    def thread_manage(self, rtsp, spaces):
        while True:
            cap = cv2.VideoCapture(rtsp)
            cap_fps = cap.get(cv2.CAP_PROP_FPS)
            self.size_wh = [int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))]
            fps_save = cap_fps * 3
            number_fps = fps_save * 2
            if cap.isOpened():
                num_pass = 0
                num_loss = 0
                number = 0
                while True:
                    succ = cap.grab()
                    number += 1
                    judge_stop = False
                    judge_img = False
                    if succ:
                        num_pass += 1
                    else:
                        num_loss += 1
                    if num_loss >= fps_save:
                        judge_stop = True
                        num_loss = 0
                        number = 0
                    if num_pass >= spaces:
                        num_pass = 0
                        judge_img = True
                    if number >= number_fps:
                        num_loss = 0
                        number = 0
                    if judge_stop:
                        cap.release()
                        cv2.destroyAllWindows()
                        break
                    if judge_img:
                        success, im = cap.retrieve()
                        if not success:
                            continue
                        self.lock.acquire()
                        self.image = im
                        self.lock.release()
            else:
                time.sleep(10)


    def __iter__(self):
        return self

    def __next__(self):
        return self.image