name: wasm-simd-hf-space-en-tts

on:
  release:
    types:
      - published

  workflow_dispatch:

concurrency:
  group: wasm-simd-hf-space-en-tts-${{ github.ref }}
  cancel-in-progress: true

jobs:
  wasm-simd-hf-space-en-tts:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install emsdk
        uses: mymindstorm/setup-emsdk@v14

      - name: View emsdk version
        shell: bash
        run: |
          emcc -v
          echo "--------------------"
          emcc --check

      - name: Download model files
        shell: bash
        run: |
          cd wasm/tts/assets
          ls -lh
          echo "----------"
          wget -q https://github.com/k2-fsa/sherpa-onnx/releases/download/tts-models/vits-piper-en_US-libritts_r-medium.tar.bz2
          tar xf vits-piper-en_US-libritts_r-medium.tar.bz2
          rm vits-piper-en_US-libritts_r-medium.tar.bz2
          mv vits-piper-en_US-libritts_r-medium/en_US-libritts_r-medium.onnx ./model.onnx
          mv vits-piper-en_US-libritts_r-medium/tokens.txt ./
          mv vits-piper-en_US-libritts_r-medium/espeak-ng-data ./
          rm -rf vits-piper-en_US-libritts_r-medium

          ls -lh

      - name: Build sherpa-onnx for WebAssembly
        shell: bash
        run: |
          ./build-wasm-simd-tts.sh

      - name: collect files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          mv build-wasm-simd-tts/install/bin/wasm/tts sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts
          ls -lh sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts
          tar cjfv sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts.tar.bz2 ./sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts

      - name: Upload wasm files
        uses: actions/upload-artifact@v4
        with:
          name: sherpa-onnx-wasm-simd-en-tts
          path: ./sherpa-onnx-wasm-simd-*.tar.bz2

      - name: Publish to ModelScope
        # if: false
        env:
          MS_TOKEN: ${{ secrets.MODEL_SCOPE_GIT_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf ms
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://www.modelscope.cn/studios/k2-fsa/web-assembly-tts-sherpa-onnx-en.git ms
            cd ms
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://oauth2:${MS_TOKEN}@www.modelscope.cn/studios/k2-fsa/web-assembly-tts-sherpa-onnx-en.git

      - name: Publish to huggingface
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/spaces/k2-fsa/web-assembly-tts-sherpa-onnx-en huggingface
            cd huggingface
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-en-tts/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://csukuangfj:$<EMAIL>/spaces/k2-fsa/web-assembly-tts-sherpa-onnx-en main
