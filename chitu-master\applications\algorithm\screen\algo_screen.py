import os
import cv2
import signal
import importlib
import time
# from multiprocessing import Queue

from applications.algorithm.screen.videocap import VideoCap
from applications.algorithm.utils.algo_utils import get_infer_name, models
from log import mylog
# from applications.models import Weights
# from applications.algorithm.utils.load_model import loadmodel


def open_algo_screen(url, camera, weight_id, alarm_type, app, shared_que, result):
    with open("applications/algorithm/screen/test_pid.txt", "w") as f_pid:
        f_pid.write(str(os.getpid()))
        f_pid.close()
    
    camera = str(camera)
    cap = VideoCap(url, camera, app, rate=3)

    res = cap.capture()
    
    if not res:
        mylog.error("Failed to capture stream for camera: {}".format(camera))
        result.put("failed")
        return

    if not cap.isOpened:
        mylog.info(camera + " open camera failed.")
    else:
        mylog.info(camera + " open camera success.")
        cap.startDrawframe()
        
        # weight = Weights.query.filter_by(id=weight_id).first()
        # if len(eval(weight.name)):
        #     path = weight.path
        #     weight_type = weight.type
        #     tmp_model = []
        #     for w in eval(weight.name):
        #         res = loadmodel(os.path.join(path, w), weight_type)
        #         if res["msg"] == "success":
        #             tmp_model.append(res["model"])
        
        while True:
            
            if not cap.q.empty() and cap.isOpened:
                tmpFame = cap.q.get()
                frame = tmpFame.frame
                modelname = get_infer_name(weight_id)
                try:
                    # res = importlib.import_module(modelname).screen(frame, tmp_model, alarm_type)
                    res = importlib.import_module(modelname).screen(frame, models[weight_id], alarm_type)
                except Exception as e:
                    return {"msg": "{}.infer函数出现错误：{}".format(modelname, e)}
                
                ret, buffer = cv2.imencode('.jpg', res)
                frame_data = buffer.tobytes()
                if shared_que.full():
                    shared_que.get()
                shared_que.put(frame_data, block=False)
            else:
                time.sleep(0.01)  # 避免高CPU占用

def generate_frames(shared_que):
    while True:
        if not shared_que.empty():
            frame_data = shared_que.get()
            # 使用生成器生成视频帧
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
        time.sleep(0.001)

def stop_algo_screen():
    try:
        with open("applications/algorithm/screen/test_pid.txt", "r") as f:
            test_pid = int(f.read().strip())
        os.kill(test_pid, signal.SIGTERM)  # 或者使用 signal.SIGKILL 直接强制杀死进程
        os.waitpid(test_pid, 0)  # 等待子进程终止，防止僵尸进程
        os.remove("applications/algorithm/screen/test_pid.txt")

    except Exception as e:
        pass
        # mylog.error("Error stopping algo screen: {}".format(e))