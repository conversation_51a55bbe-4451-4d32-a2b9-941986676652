import numpy as np
from skimage.draw import polygon


def bboxes_iou(boxes1, boxes2):
    boxes1 = np.array(boxes1)
    boxes2 = np.array(boxes2)
    
    boxes1_area = (boxes1[2] - boxes1[0]) * (boxes1[3] - boxes1[1])
    boxes2_area = (boxes2[2] - boxes2[0]) * (boxes2[3] - boxes2[1])

    left_up = np.maximum(boxes1[:2], boxes2[:2])  # xy, xy,x与x相比,取最大值
    right_down = np.minimum(boxes1[2:], boxes2[2:])

    inter_section = np.maximum(right_down - left_up, 0.0)
    inter_area = inter_section[0] * inter_section[1]
    if boxes1_area < boxes2_area:
        ious = np.maximum(1.0 * inter_area / boxes1_area, np.finfo(np.float32).eps)
    else:
        ious = np.maximum(1.0 * inter_area / boxes2_area, np.finfo(np.float32).eps)
    return ious


def polygon_IOU(polygon_1, polygon_2):
    """
    计算两个多边形的IOU
    :param polygon_1: [[row1, col1], [row2, col2], ...]
    :param polygon_2: 同上
    :return:
    """
    # box_1 = np.array(boxes1)
    # polygon_1 = np.floor(box_1).astype(np.int64)
    # box_2 = np.array(boxes2)
    # polygon_2 = np.floor(box_2).astype(np.int64)
    
    rr1, cc1 = polygon(polygon_1[:, 0], polygon_1[:, 1])
    rr2, cc2 = polygon(polygon_2[:, 0], polygon_2[:, 1])

    try:
        r_max = max(rr1.max(), rr2.max()) + 1
        c_max = max(cc1.max(), cc2.max()) + 1
    except:
        return 0

    canvas = np.zeros((r_max, c_max))
    canvas[rr1, cc1] += 1
    canvas[rr2, cc2] += 1
    union = np.sum(canvas > 0)
    if union == 0:
        return 0
    
    canvas1 = np.zeros((r_max, c_max))
    canvas1[rr1, cc1] += 1
    area1 = np.sum(canvas1 > 0)
    
    canvas2 = np.zeros((r_max, c_max))
    canvas2[rr2, cc2] += 1
    area2 = np.sum(canvas2 > 0)
    
    area = min(area1, area2)    
    intersection = np.sum(canvas == 2)
    return intersection / area


def train_iou(boxes1, boxes2):
    boxes1 = np.array(boxes1)
    boxes2 = np.array(boxes2)

    boxes1_area = (boxes1[2] - boxes1[0]) * (boxes1[3] - boxes1[1])
    boxes2_area = (boxes2[2] - boxes2[0]) * (boxes2[3] - boxes2[1])

    left_up = np.maximum(boxes1[:2], boxes2[:2])  # xy, xy,x与x相比,取最大值
    right_down = np.minimum(boxes1[2:], boxes2[2:])

    inter_section = np.maximum(right_down - left_up, 0.0)
    inter_area = inter_section[0] * inter_section[1]
    ious = np.maximum(1.0 * inter_area / boxes1_area, np.finfo(np.float32).eps)
    ious2 = np.maximum(1.0 * inter_area / boxes2_area, np.finfo(np.float32).eps)

    if (ious > 0.95) and (ious2 > 0.6):
        ious = np.finfo(np.float32).eps

    return ious

