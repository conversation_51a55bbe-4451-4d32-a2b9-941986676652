import datetime
from applications.extensions import db

class Alarm_Record(db.Model):
    __tablename__ = 'ai_alarm_push_record'
    id = db.Column(db.Integer, primary_key=True)
    camera_label = db.Column(db.String(32))
    alarm_type = db.Column(db.String(8))
    pic_url = db.Column(db.String(256))
    description = db.Column(db.String(256))
    status = db.Column(db.String(1))
    alarm_time = db.Column(db.DateTime, default=datetime.datetime.now)

    def json(self):
        return {
            'id': self.id,
            'camera_label': self.camera_label,
            'alarm_type': self.alarm_type,
            'pic_url': self.pic_url,
            'description': self.description,
            'status': self.status,
            'alarm_time': self.alarm_time.isoformat()
        }