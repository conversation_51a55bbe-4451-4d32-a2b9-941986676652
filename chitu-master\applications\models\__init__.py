from .admin_dept import Dept
from .admin_dict import DictType, DictData
from .admin_log import AdminLog
from .admin_photo import Photo
from .admin_power import Power
from .admin_role import Role
from .admin_role_power import role_power
from .admin_user import User
from .admin_user_role import user_role
from .admin_mail import Mail
from .admin_algorithm import Algorithm, AlgorithmSetting, Weights, Camera, CameraAlarmType
from .admin_record import Alarm_Record
from .admin_send_mail import Email_Push_Set
from .admin_receive_mail import Email_Push_Receive
from .admin_ding_talk import DingTalkWebhookSettings
from .admin_push_mode import PushSettings
from .admin_push_record import AlarmPushRecord
from .admin_web_chat import WebChatWebhookSettings
from .admin_push_template import PushTemplate
from .admin_push_template_mode import TemplateMode
from .admin_network import NetworkConfig
# from applications.digital_human.models import DigitalHumanFaceBase, digital_human_interactive, digital_human_basic_setting