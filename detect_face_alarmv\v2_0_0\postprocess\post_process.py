from .utils.utils import make_json, draw_face_boxes
from ..preprocess.utils.utils import image_base64, image_byte
from ..utils.log import mylog
import cv2
import numpy as np


class PostProcess(object):
    def __init__(self, i18n, post_mess):
        self.post_mess = post_mess
        self.i18n = i18n
        mylog.info("加载人脸识别后处理模块")


    def disassemble_recv_data_type(self, data):
        """输出类型，改变输出数量，默认都开启"""
        recv_data_type = data["businessData"]["advsetValue"]["recvDataType"]
        message_bool = False
        source_img_bool = False
        draw_img_bool = False
        boxes_back_bool = False
        if "infer_results" in recv_data_type:
            message_bool = True
        if "draw_image" in recv_data_type:
            draw_img_bool = True
        if "source_image" in recv_data_type:
            source_img_bool = True
        if "infer_boxes" in recv_data_type:
            boxes_back_bool = True
        return message_bool, source_img_bool, draw_img_bool, boxes_back_bool


    def disassemble_algo_type(self, data, image, face_boxes):
        """
        人脸识别结果处理 - 按照标准算法格式，支持三种检测模式
        """
        post_http_bool = False
        json_dict = make_json(self.post_mess)
        message_bool, source_img_bool, draw_img_bool, boxes_back_bool = self.disassemble_recv_data_type(data)

        # 处理原始图像输出
        if source_img_bool:
            if "image" in data["businessData"]:
                json_dict["rawImage"] = data["businessData"]["image"]
            else:
                if data["businessData"]["imageType"] == "base64":
                    json_dict["rawImage"] = image_base64(image)
                if data["businessData"]["imageType"] == "byte":
                    json_dict["rawImage"] = image_byte(image)

        # 获取检测模式
        detection_mode = data["businessData"]["advsetValue"].get("faceDetect", "all")
        mylog.info(f"[FACE_ALARM] 检测模式: {detection_mode}")

        # 分类处理识别结果
        known_faces = []
        unknown_faces = []
        correct_boxes = []

        for face_box in face_boxes:
            # face_box格式: [x1,y1,x2,y2,conf,person_name,similarity]
            if len(face_box) >= 6:
                x1, y1, x2, y2, conf = face_box[:5]
                person_name = face_box[5]
                similarity = face_box[6] if len(face_box) > 6 else conf

                if person_name != "unknown":
                    # 已知人员
                    known_faces.append([x1, y1, x2, y2, similarity, person_name])
                else:
                    # 陌生人
                    unknown_faces.append([x1, y1, x2, y2, conf, "unknown"])
            else:
                # 如果格式不正确，当作陌生人处理
                if len(face_box) >= 5:
                    x1, y1, x2, y2, conf = face_box[:5]
                    unknown_faces.append([x1, y1, x2, y2, conf, "unknown"])

        # 添加分类结果日志
        mylog.info(f"[FACE_ALARM] 已知人员: {len(known_faces)} 个")
        mylog.info(f"[FACE_ALARM] 陌生人员: {len(unknown_faces)} 个")

        # 根据检测模式决定是否报警
        if detection_mode == "all":
            # 检测到任何人脸都报警
            if len(known_faces) > 0 or len(unknown_faces) > 0:
                post_http_bool = True
                if message_bool:
                    if len(known_faces) > 0:
                        json_dict["values"].append({"type": "known_face", "value": self.i18n.get("known_person")})
                    if len(unknown_faces) > 0:
                        json_dict["values"].append({"type": "unknown_face", "value": self.i18n.get("unknown_person")})

                # 合并所有检测框
                correct_boxes.extend(known_faces)
                correct_boxes.extend(unknown_faces)

        elif detection_mode == "authorized":
            # 只有检测到已知人员才报警
            if len(known_faces) > 0:
                post_http_bool = True
                if message_bool:
                    json_dict["values"].append({"type": "known_face", "value": self.i18n.get("authorized_person")})
                correct_boxes = known_faces

        elif detection_mode == "unauthorized":
            # 只有检测到陌生人才报警
            if len(unknown_faces) > 0:
                post_http_bool = True
                if message_bool:
                    json_dict["values"].append({"type": "unknown_face", "value": self.i18n.get("unauthorized_person")})
                correct_boxes = unknown_faces

        # 添加检测框信息
        if boxes_back_bool:
            json_dict["boxes"]["face"] = correct_boxes

        # 绘制检测结果
        if draw_img_bool:
            image = draw_face_boxes(image, correct_boxes)
            if data["businessData"]["imageType"] == "base64":
                json_dict["image"] = image_base64(image)
            if data["businessData"]["imageType"] == "byte":
                json_dict["image"] = image_byte(image)

        return json_dict, post_http_bool

    def disassemble_advset_value(self, data, image, boxes):
        """
        解析高级配置参数，处理区域过滤 - 按照标准算法格式
        """
        face_boxes = []
        h, w, _ = image.shape
        business_data = data["businessData"]
        flag = True

        if "area" in business_data["advsetValue"]:
            if "areaType" in business_data["advsetValue"]["area"]:
                area_type = business_data["advsetValue"]["area"]["areaType"]
                if area_type == "POLYGON" and "positions" in business_data["advsetValue"]["area"]:
                    positions = business_data["advsetValue"]["area"]["positions"]
                    if len(positions) != 0:
                        flag = False
                        box_in_index = []
                        for region in positions:
                            if len(region) >= 3:
                                coordinate = []
                                for i in region:
                                    coordinate.append(np.array([i[0]*w, i[1]*h]))
                                box_1 = np.array(coordinate)

                                polygon_1 = np.floor(box_1).astype(np.int32)
                                # 绘制多边形
                                image = cv2.polylines(image, [polygon_1], True, (0, 0, 250), thickness=2)

                                for i in range(len(boxes)):
                                    if len(boxes[i]) != 0:
                                        # 检查人脸中心点是否在区域内
                                        x1, y1, x2, y2 = boxes[i][:4]
                                        center_x = int((x1 + x2) / 2)
                                        center_y = int((y1 + y2) / 2)

                                        if cv2.pointPolygonTest(polygon_1, (center_x, center_y), False) >= 0:
                                            box_in_index.append(i)

                        boxes_in_index = list(set(box_in_index))
                        for j in boxes_in_index:
                            face_boxes.append(boxes[j])
                    else:
                        for b in boxes:
                            if len(b) != 0:
                                face_boxes.append(b)

        # 没有划定区域，默认区域不存在
        if flag:
            for b in boxes:
                if len(b) != 0:
                    face_boxes.append(b)

        return face_boxes, image

    def run_process(self, data, boxes, image):
        """
        运行后处理流程 - 按照标准算法格式
        """
        # 先进行区域过滤
        face_boxes, image = self.disassemble_advset_value(data, image, boxes)

        # 添加检测结果统计日志
        mylog.info(f"原始检测框数量: {len(boxes)}")
        mylog.info(f"过滤后检测框数量: {len(face_boxes)}")
        if len(face_boxes) > 0:
            for i, box in enumerate(face_boxes):
                if len(box) >= 5:  # 确保有置信度信息
                    mylog.info(f"检测框{i+1}: 置信度={box[4]:.3f}")

        # 再进行算法类型处理
        back_json, post_bool = self.disassemble_algo_type(data, image, face_boxes)
        mylog.info(f"是否触发报警: {post_bool}")

        return back_json, post_bool
