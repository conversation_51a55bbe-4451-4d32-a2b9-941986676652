name: run-java-test

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/run-java-test.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'java-api-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/jni/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/run-java-test.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'java-api-examples/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/jni/*'
  workflow_dispatch:

concurrency:
  group: run-java-test-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  run_java_test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}

      - name: Display java version
        shell: bash
        run: |
          java -version
          echo "JAVA_HOME is: ${JAVA_HOME}"

      - name:  Run java test
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          cd ./java-api-examples
          ./runtest.sh
