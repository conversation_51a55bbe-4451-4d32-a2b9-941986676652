import requests
from applications.extensions import db
from applications.digital_human.models import digital_human_interactive
from log import mylog
import json
import os
import socket
from threading import Timer

s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
s.connect(('8.8.8.8',1))
resp = s.getsockname()[0]
res = resp + ':9620'

def download_video(questionId, fileType, fileUrl=None, videoUrl=None):
    try:
        if videoUrl is not None:
            response = requests.get(videoUrl, timeout=5)
            filename = videoUrl.split('/')[-1] 
            if not os.path.exists('/mnt/keep/workspace/digital_human_data/digital_human_videos/qa_videos'):
                os.makedirs('/mnt/keep/workspace/digital_human_data/digital_human_videos/qa_videos')
            save_path = f'/mnt/keep/workspace/digital_human_data/digital_human_videos/qa_videos/{filename}'
            nginx_path = f'qa_videos/{filename}'

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
            else:
                return [500, questionId]
        else:
            nginx_path = None
        
        if fileUrl is not None:
            if fileType in ['0', '1', 0, 1]:
                annexname = fileUrl.split('/')[-1]
                annex_path = f'/mnt/keep/workspace/digital_human_data/digital_human_videos/qa_videos/{annexname}'
                nginx_annex_path = f'qa_videos/{annexname}'
                response = requests.get(fileUrl, timeout=5)
                if response.status_code == 200:
                    with open(annex_path, 'wb') as f:
                        f.write(response.content)
                else:
                    return [500, questionId]
            else:
                nginx_annex_path = ""
        else:
            nginx_annex_path = None
        return [nginx_path, nginx_annex_path, questionId]
    except:
        return [500, questionId]


def remove_no_use_videos(answer_path=None, file_path=None):
    if answer_path is not None:
        if os.path.exists(answer_path):
            os.remove(answer_path)
    if file_path != "" and file_path is not None:
        if os.path.exists(file_path):
            os.remove(file_path)


def check_relate_question(relation_list, exist_dict, app):
    add_relation_ids = []
    del_relation_ids = []
    exist_list = list(exist_dict.keys())
    with app.app_context():
        for qu_id in list(set(relation_list) | set(exist_list)):
            if qu_id not in exist_list and qu_id in relation_list:
                obj = digital_human_interactive.query.filter_by(questionId=qu_id).first()
                if not obj:
                    return False, None, None
                else:
                    add_relation_ids.append(obj)
            elif qu_id in exist_list and qu_id not in relation_list:
                del_relation_ids.append(exist_dict[qu_id])
        return True, add_relation_ids, del_relation_ids


# def op_interactive_video_to_db(question, answer, videoUrl, questionId, enabled, similarQuestionList, videoDuration, fileType, fileUrl, app):
def op_interactive_video_to_db(item, videoUrl, app):
    question = item["question"]
    answer = item["answer"]
    questionId = item["questionId"]
    enabled = item["enabled"]
    similarQuestionList = json.dumps(item.get("similarQuestionList", []), ensure_ascii=False)
    videoDuration = item["videoDuration"]
    
    fileType = item.get("fileType", "")
    fileUrl = item.get("fileUrl", "")
    if fileUrl != "":
        fileUrl = "http://" + app.config['MINIO_ADDRESS'] + fileUrl
    associationIds = item.get("associationIds", [])
    
    with app.app_context():
        obj = digital_human_interactive.query.filter_by(questionId=questionId).first()
        if not obj:
            returnlist = download_video(questionId, fileType, fileUrl, videoUrl)
            if isinstance(returnlist[0], int):
                return returnlist
            else:
                new_entry = digital_human_interactive(
                    question=question,
                    answer=answer,
                    interactive_video=returnlist[0],
                    questionId = questionId,
                    enabled = enabled,
                    similarQuestionList = similarQuestionList,
                    videoDuration = videoDuration,
                    fileType=str(fileType),
                    fileUrl=returnlist[1]
                )
                res, add_relation_ids, del_relation_ids = check_relate_question(associationIds, {}, app)
                if res:
                    for re_id in add_relation_ids:
                        new_entry.correlations.append(re_id)
                    try:
                        db.session.add(new_entry)
                        db.session.commit()
                    except Exception as e:
                        db.session.rollback()
                        mylog.info(e)
                    return questionId
                else:
                    return [500, questionId]
        else:
            if obj.interactive_video.split('/')[-1] == videoUrl.split('/')[-1]:
                videoUrl = None
            if obj.fileUrl is not None:
                if obj.fileUrl != "" and fileUrl != "" and obj.fileUrl.split('/')[-1] == fileUrl.split('/')[-1]:
                    fileUrl = None
            
            returnlist = [None, None, questionId]
            
            if fileUrl is not None or videoUrl is not None:
                returnlist = download_video(questionId, fileType, fileUrl, videoUrl)
            if isinstance(returnlist[0], int):
                return returnlist
            else:
                exist_ids = {}
                for i in obj.correlations:
                    exist_ids[i.questionId] = i
                res, add_relation_ids, del_relation_ids = check_relate_question(associationIds, exist_ids, app)
                if res:
                    try:
                        answer_path = None
                        annex_path = None
                        obj.question = question
                        obj.similarQuestionList = similarQuestionList
                        obj.answer = answer
                        if returnlist[0] is not None:
                            answer_path = os.path.join("/mnt/keep/workspace/digital_human_data/digital_human_videos", obj.interactive_video)
                            obj.interactive_video = returnlist[0]
                        obj.enabled = enabled
                        obj.videoDuration = videoDuration
                        obj.fileType = str(fileType)
                        if returnlist[1] is not None:
                            if obj.fileUrl != "" and obj.fileUrl is not None:
                                annex_path = os.path.join("/mnt/keep/workspace/digital_human_data/digital_human_videos", obj.fileUrl)
                            obj.fileUrl = returnlist[1]

                        for item in add_relation_ids:
                            obj.correlations.append(item)
                        for item in del_relation_ids:
                            obj.correlations.remove(item)
                        db.session.commit()
                        if annex_path is not None and answer_path is not None:
                            Timer(300, remove_no_use_videos, (answer_path, annex_path)).start()
                    except Exception as e:
                        mylog.info(e)
                        return [500, questionId]
                    return questionId
                else:
                    return [500, questionId]

def main(msg, app):
    successlist = []
    errorlist = []
    for item in msg["messageBody"]:
        if item["videoUrl"] in ["null", ""]:
            errorlist.append(item["questionId"])
            continue
        else:
            videoUrl = "http://" + app.config['MINIO_ADDRESS'] + item["videoUrl"]
        list1 = op_interactive_video_to_db(item, videoUrl, app)
        if isinstance(list1, int):
            successlist.append(list1)
        else:
            dict = {
                "questionId": list1[1],
                "message": list1[0]
            }
            errorlist.append(dict)
            
    for i in errorlist:
        if i["message"] == 500:
            i["message"] = "服务器断网"
            
    return [successlist, errorlist]


def delete_interactive(deletedIds, app):
    successlist = []
    noExistlist = []
    errorlist = []
    # with app.app_context():
    #     all_ids = db.session.query(digital_human_interactive.questionId).all()
    with app.app_context():
        for questionId in deletedIds:
            
            obj = digital_human_interactive.query.filter_by(questionId=questionId).first()
            if not obj:
                noExistlist.append(questionId)
            else:
                annex_path = None
                answer_path = os.path.join("/mnt/keep/workspace/digital_human_data/digital_human_videos", obj.interactive_video)
                if obj.fileUrl != "" and obj.fileUrl is not None:
                    annex_path = os.path.join("/mnt/keep/workspace/digital_human_data/digital_human_videos", obj.fileUrl)
                Timer(600, remove_no_use_videos, (answer_path, annex_path)).start()
                
                try:
                    db.session.delete(obj)
                    db.session.commit()
                    successlist.append(questionId)
                except:
                    errorlist.append(questionId)

    if len(errorlist) > 0:
        return 500, "操作失败", errorlist
    else:
        return 200, "操作成功", successlist
    
def async_interactive(releasedIds, releaseingIds, deletedIds, app):
    toReleaseIds = []
    abnormalIds = []
    need_delete_ids = []
    with app.app_context():
        all_ids = db.session.query(digital_human_interactive.questionId).all()
    for item in all_ids:
        if item[0] in releasedIds:
            releasedIds.remove(item[0])
        elif item[0] in releaseingIds:
            toReleaseIds.append(item[0])
            releaseingIds.remove(item[0])
        elif item[0] in deletedIds:
            need_delete_ids.append(item[0])
        else:
            abnormalIds.append(item[0])
    mylog.info("问答库同步：已发布列表（平台存在，赤兔不存在）：" + str(releasedIds))
    mylog.info("问答库同步：发布中列表（平台发布中，赤兔已发布）：" + str(toReleaseIds))
    mylog.info("问答库同步：发布中列表（平台发布中，赤兔不存在）：" + str(releaseingIds))
    mylog.info("问答库同步：异常列表（平台不存在，赤兔存在）：" + str(abnormalIds))
    
    releaseingIds.extend(releasedIds)
    if len(need_delete_ids) > 0:
        res_code, _, returnIds = delete_interactive(need_delete_ids, app)
        if res_code == 200:
            return 200, "操作成功", toReleaseIds, releaseingIds
        else:
            return 500, "操作失败", returnIds, []
    else:
        return 200, "操作成功", toReleaseIds, releaseingIds
    
if __name__ == "__main__":
    main()