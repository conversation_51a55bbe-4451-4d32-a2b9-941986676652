from flask_login import <PERSON><PERSON><PERSON>anager
from flask import jsonify



def init_login_manager(app):
    login_manager = LoginManager()
    login_manager.init_app(app)

    # login_manager.login_view = 'system.passport.login'
    # login_manager.login_message ="请登录"
    # login_manager.login_message_category = 'info'
    

    @login_manager.user_loader
    def load_user(user_id):
        from applications.models import User
        user = User.query.get(int(user_id))
        return user

    @login_manager.unauthorized_handler
    def unauthorized():
        return {"code":401}
