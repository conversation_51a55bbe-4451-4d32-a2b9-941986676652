import yaml
import os
import sys
from ..utils.log import mylog

class ModelCheck(object):
    """算法推理部分统一接口.

        # Argument:
            root_path: 项目所在地址目录，应包含log，weight，config.
            label: 标签，训练使用的英文标签，list类型
            model: 模型名称，例yolov5,在torch中会被合成为yolov5.pth，在rknn中会被合成为yolov5.rknn
        """
    def __init__(self, label, model_name, number):
        current_path = os.path.dirname(os.path.abspath(__file__))
        weight_path = os.path.join(current_path.replace('inference', ''), 'weights')
        weight_list = sorted(os.listdir(weight_path))
        model_type = ''
        for w in weight_list:  # 多个模型，取匹配到的第一个名字相同的模型
            weight_name_type = w.split('.')
            weight_name = weight_name_type[0]
            weight_type = weight_name_type[1]
            if model_name == weight_name:
                model_type = weight_type
        if model_type == 'pt':
            from .inference_torch.inference_torch import InferenceTorch
            self.model = InferenceTorch(label, weight_path, model_name, number)
        elif model_type == 'engine':
            from .inference_trt.inference_trt import InferenceTrt
            self.model = InferenceTrt(label, weight_path, model_name, number)
        elif model_type == 'rknn':
            from .inference_rknn.inference_rknn import InferenceRknn
            self.model = InferenceRknn(label, weight_path, model_name, number)
        else:
            mylog.error('inference way error')
            sys.exit(0)
        mylog.info('加载算法推理模块')

    def run(self, image, gain):
        """
        输入：opencv读取后的ndarray格式原图
        输出：list，包含[x1,y1,x2,y2,con,class], 没有box输出空的list，[]
        """
        return self.model.run(image, gain)


