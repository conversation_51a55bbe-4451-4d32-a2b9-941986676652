# utils/i18n.py
import json
import os

class I18n:
    def __init__(self):
        self.language =  'en'
        self.language_dict = self.__get_all_language()
        self.translations = self.__load_translations()
    
    def __get_all_language(self):
        languages = []
        language_dir = os.path.join(os.path.dirname(__file__), '../i18n')
        for item in os.listdir(language_dir):
            languages.append(item.split(".json")[0])
        return languages

    def __load_translations(self):
        language_file = os.path.join(os.path.dirname(__file__), '../i18n', f'{self.language}.json')
        if not os.path.exists(language_file):
            language_file = os.path.join(os.path.dirname(__file__), '../i18n', 'en.json')  # 默认语言
        with open(language_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def reload(self, language):
        if language in self.language_dict:
            self.language = language
            self.translations = self.__load_translations()
            return 200
        else:
            return 500

    def get(self, key):
        return self.translations[key]


if __name__ == '__main__':
    i18n = I18n()
    a = i18n.get('otherColor')
    print('************', a)

    mess = i18n.reload('zh')
    print('mess', mess)
    b = i18n.get('otherColor')
    print('************', b)


