"""
人脸检测多框问题配置文件
用于优化detect_face_alarm算法的检测参数，减少误检和多框问题
"""

class MultiBoxConfig:
    """多框问题优化配置"""
    
    def __init__(self):
        # 基础检测参数
        self.confidence_threshold = 0.8      # 提高置信度阈值，减少误检
        self.nms_threshold = 0.2             # 降低NMS阈值，更严格抑制重叠框
        
        # 人脸大小过滤参数
        self.min_face_ratio = 0.008          # 最小人脸比例（0.8%）
        self.max_face_ratio = 0.7            # 最大人脸比例（70%）
        self.min_face_size = 40              # 最小人脸框尺寸（像素）
        
        # 多框抑制参数
        self.max_faces_per_image = 8         # 单张图像最大人脸数量
        self.enable_additional_nms = True    # 启用额外的NMS处理
        
        # 坐标转换参数
        self.coordinate_precision = 1        # 坐标精度（小数位数）
        self.enable_boundary_check = True    # 启用边界检查
        
        # 调试参数
        self.enable_debug_logs = True        # 启用调试日志
        self.save_debug_images = False       # 保存调试图像
        
    def get_optimized_params(self, image_size=None):
        """
        根据图像尺寸获取优化后的参数
        Args:
            image_size: 图像尺寸 (width, height)
        Returns:
            dict: 优化后的参数字典
        """
        params = {
            'confidence_threshold': self.confidence_threshold,
            'nms_threshold': self.nms_threshold,
            'min_face_ratio': self.min_face_ratio,
            'max_face_ratio': self.max_face_ratio,
            'min_face_size': self.min_face_size,
            'max_faces_per_image': self.max_faces_per_image,
        }
        
        # 根据图像尺寸调整参数
        if image_size:
            width, height = image_size
            image_area = width * height
            
            # 对于高分辨率图像，适当放宽限制
            if image_area > 1920 * 1080:  # 大于1080p
                params['min_face_ratio'] = 0.005
                params['max_faces_per_image'] = 12
            elif image_area < 640 * 480:  # 小于VGA
                params['min_face_ratio'] = 0.015
                params['max_faces_per_image'] = 4
        
        return params
    
    def get_detection_modes_config(self):
        """获取不同检测模式的配置"""
        return {
            'all': {
                'description': '检测所有人脸',
                'confidence_boost': 0.0,
                'max_faces_multiplier': 1.0
            },
            'authorized': {
                'description': '只检测已知人员',
                'confidence_boost': 0.1,  # 提高置信度要求
                'max_faces_multiplier': 0.8
            },
            'unauthorized': {
                'description': '只检测陌生人',
                'confidence_boost': 0.05,
                'max_faces_multiplier': 1.2
            }
        }
    
    def get_performance_config(self):
        """获取性能优化配置"""
        return {
            'enable_gpu_acceleration': True,
            'batch_processing': False,
            'memory_optimization': True,
            'parallel_processing': True,
            'max_processing_threads': 3
        }
    
    def apply_to_inference(self, inference_instance):
        """
        将配置应用到推理实例
        Args:
            inference_instance: InferenceRknn实例
        """
        try:
            if hasattr(inference_instance, 'update_detection_params'):
                inference_instance.update_detection_params(
                    confidence=self.confidence_threshold,
                    nms_threshold=self.nms_threshold,
                    min_face_ratio=self.min_face_ratio,
                    max_faces=self.max_faces_per_image,
                    min_size=self.min_face_size
                )
                print(f"✅ 配置已应用到推理实例")
            else:
                print(f"⚠️ 推理实例不支持参数更新")
        except Exception as e:
            print(f"❌ 配置应用失败: {e}")

# 预定义的配置方案
class ConfigPresets:
    """预定义配置方案"""
    
    @staticmethod
    def get_high_precision_config():
        """高精度配置 - 减少误检，可能漏检一些边缘情况"""
        config = MultiBoxConfig()
        config.confidence_threshold = 0.85
        config.nms_threshold = 0.15
        config.min_face_ratio = 0.01
        config.max_faces_per_image = 6
        return config
    
    @staticmethod
    def get_high_recall_config():
        """高召回配置 - 尽可能检测所有人脸，可能有误检"""
        config = MultiBoxConfig()
        config.confidence_threshold = 0.6
        config.nms_threshold = 0.3
        config.min_face_ratio = 0.005
        config.max_faces_per_image = 12
        return config
    
    @staticmethod
    def get_balanced_config():
        """平衡配置 - 精度和召回的平衡"""
        config = MultiBoxConfig()
        config.confidence_threshold = 0.75
        config.nms_threshold = 0.25
        config.min_face_ratio = 0.008
        config.max_faces_per_image = 8
        return config
    
    @staticmethod
    def get_realtime_config():
        """实时配置 - 优化性能，适合视频流"""
        config = MultiBoxConfig()
        config.confidence_threshold = 0.8
        config.nms_threshold = 0.2
        config.min_face_ratio = 0.01
        config.max_faces_per_image = 6
        config.enable_additional_nms = False  # 关闭额外NMS以提高速度
        return config

def load_config_from_file(config_file="face_detection_config.json"):
    """
    从文件加载配置
    Args:
        config_file: 配置文件路径
    Returns:
        MultiBoxConfig: 配置实例
    """
    import json
    import os
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config = MultiBoxConfig()
            for key, value in config_data.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            print(f"✅ 从文件加载配置: {config_file}")
            return config
        else:
            print(f"⚠️ 配置文件不存在，使用默认配置: {config_file}")
            return MultiBoxConfig()
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return MultiBoxConfig()

def save_config_to_file(config, config_file="face_detection_config.json"):
    """
    保存配置到文件
    Args:
        config: MultiBoxConfig实例
        config_file: 配置文件路径
    """
    import json
    
    try:
        config_data = {
            'confidence_threshold': config.confidence_threshold,
            'nms_threshold': config.nms_threshold,
            'min_face_ratio': config.min_face_ratio,
            'max_face_ratio': config.max_face_ratio,
            'min_face_size': config.min_face_size,
            'max_faces_per_image': config.max_faces_per_image,
            'enable_additional_nms': config.enable_additional_nms,
            'coordinate_precision': config.coordinate_precision,
            'enable_boundary_check': config.enable_boundary_check,
            'enable_debug_logs': config.enable_debug_logs,
            'save_debug_images': config.save_debug_images,
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已保存到文件: {config_file}")
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")

if __name__ == "__main__":
    # 示例用法
    print("人脸检测配置示例")
    print("=" * 40)
    
    # 创建默认配置
    config = MultiBoxConfig()
    print(f"默认置信度阈值: {config.confidence_threshold}")
    print(f"默认NMS阈值: {config.nms_threshold}")
    
    # 获取优化参数
    params = config.get_optimized_params((1920, 1080))
    print(f"1080p图像优化参数: {params}")
    
    # 保存配置
    save_config_to_file(config)
    
    # 加载配置
    loaded_config = load_config_from_file()
    print(f"加载的配置置信度阈值: {loaded_config.confidence_threshold}")
