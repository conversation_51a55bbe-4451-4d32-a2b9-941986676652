import datetime
from applications.extensions import db

class PushTemplate(db.Model):
    __tablename__ = 'admin_push_templates'

    id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, primary_key=True, autoincrement=True, comment='主键Id')
    push_type = db.Column(db.String(1), nullable=False, comment='模板类型 1：微信 2：钉钉 3：邮箱')
    #template_type = db.Column(db.String(1), nullable=False, comment='模板类型 1：文本类型 2：图文类型')
    template_type = db.Column(db.String(1), db.<PERSON><PERSON>ey('admin_template_mode.template_type'), nullable=False, comment='模板类型 1：文本类型 2：图文类型')
    content = db.Column(db.String(128), nullable=False, comment='内容')
    message_sample_url = db.Column(db.String(256), nullable=True, comment='消息示例图片地址')
    status = db.Column(db.String(1), nullable=False, comment='状态 0：关闭 1：开启')
    topic = db.Column(db.String(128), nullable=True, comment='主题')
    
    template_mode = db.relationship('TemplateMode', backref='push_templates', lazy='joined')
    def json(self):
        return {
            'id': self.id,
            'push_type': self.push_type,
            'template_type': self.template_type,
            'content': self.content,
            'message_sample_url': self.message_sample_url,
            'status': self.status,
            'topic': self.topic
        }