import datetime
from applications.extensions import db



class DingTalkWebhookSettings(db.Model):
    __tablename__ = 'admin_dingtalk_settings'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键Id')
    webhook_url = db.Column(db.String(128), nullable=False, comment='webhook推送地址')
    customize_key_word = db.Column(db.String(128), nullable=True, comment='自定义关键词')
    secret_key = db.Column(db.String(128), nullable=True, comment='加签密钥')

    def json(self):
        return {
            'id': self.id,
            'webhook_url': self.webhook_url,
            'customize_key_word': self.customize_key_word,
            'secret_key': self.secret_key
        }