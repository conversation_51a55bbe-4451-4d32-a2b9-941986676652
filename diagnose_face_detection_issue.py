#!/usr/bin/env python3
"""
诊断人脸检测问题 - 检查每个环节
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
import time

def create_test_image_with_face():
    """创建包含明显人脸的测试图像"""
    img = np.ones((480, 640, 3), dtype=np.uint8) * 128
    
    # 绘制一个更明显的"人脸"
    cv2.rectangle(img, (200, 150), (400, 350), (220, 220, 220), -1)  # 脸部
    cv2.circle(img, (250, 200), 20, (50, 50, 50), -1)  # 左眼
    cv2.circle(img, (350, 200), 20, (50, 50, 50), -1)  # 右眼
    cv2.rectangle(img, (290, 230), (310, 270), (180, 180, 180), -1)  # 鼻子
    cv2.rectangle(img, (260, 290), (340, 320), (100, 100, 100), -1)  # 嘴巴
    
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    return base64.b64encode(buffer).decode('utf-8')

def test_model_loading():
    """测试模型加载状态"""
    print("🔍 检查模型加载状态")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建RKNN推理实例
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        print("✅ RKNN推理实例创建成功")
        print(f"  - 检测模型状态: {'✅ 已加载' if rknn_inference.detection_model else '❌ 未加载'}")
        print(f"  - 识别模型状态: {'✅ 已加载' if rknn_inference.recognition_model else '❌ 未加载'}")
        print(f"  - 置信度阈值: {rknn_inference._confidence}")
        print(f"  - NMS阈值: {rknn_inference._nms_threshold}")
        
        return rknn_inference, True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def test_direct_rknn_inference(rknn_inference):
    """直接测试RKNN推理"""
    print("\n🔍 直接测试RKNN推理")
    print("=" * 50)
    
    if not rknn_inference or not rknn_inference.detection_model:
        print("❌ RKNN模型未加载，跳过测试")
        return False
    
    try:
        # 创建测试图像
        test_img = create_test_image_with_face()
        print(f"  - 测试图像尺寸: {test_img.shape}")
        
        # 直接调用predict_face函数
        from inference.inference_rknn.utils_rknn.utils import predict_face
        
        print("  - 开始RKNN推理...")
        face_detections = predict_face(
            rknn_inference._confidence, 
            rknn_inference._nms_threshold, 
            test_img, 
            rknn_inference.wh, 
            rknn_inference.detection_model
        )
        
        print(f"✅ RKNN推理完成")
        print(f"  - 检测结果数量: {len(face_detections)}")
        
        for i, detection in enumerate(face_detections):
            if len(detection) >= 5:
                x1, y1, x2, y2, conf = detection[:5]
                print(f"  - 检测框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")
        
        return len(face_detections) > 0
        
    except Exception as e:
        print(f"❌ RKNN推理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_inference_pipeline(rknn_inference):
    """测试完整推理流程"""
    print("\n🔍 测试完整推理流程")
    print("=" * 50)
    
    if not rknn_inference:
        print("❌ RKNN实例未创建，跳过测试")
        return False
    
    try:
        # 创建测试图像
        test_img = create_test_image_with_face()
        gain = [1.0, 0, 0]  # 简单的gain参数
        
        print("  - 开始完整推理流程...")
        
        # 调用完整的run方法
        result = rknn_inference.run(test_img, gain, test_img.shape)
        
        print(f"✅ 完整推理流程完成")
        print(f"  - 推理结果数量: {len(result)}")
        
        for i, detection in enumerate(result):
            if len(detection) >= 5:
                x1, y1, x2, y2, conf = detection[:5]
                if len(detection) >= 7:
                    person_name, similarity = detection[5], detection[6]
                    print(f"  - 检测框{i+1}: [{x1:.0f},{y1:.0f},{x2:.0f},{y2:.0f}], 置信度: {conf:.3f}, 人员: {person_name}, 相似度: {similarity:.3f}")
                else:
                    print(f"  - 检测框{i+1}: [{x1:.0f},{y1:.0f},{x2:.0f},{y2:.0f}], 置信度: {conf:.3f}")
        
        return len(result) > 0
        
    except Exception as e:
        print(f"❌ 完整推理流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_interface():
    """测试算法接口"""
    print("\n🔍 测试算法接口")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from utils.face_recognition import DetectFaceAlarm
        
        # 创建算法实例
        detector = DetectFaceAlarm("detect_face_alarm", "v2.0.0", "face_alarm")
        
        print("✅ 算法实例创建成功")
        
        # 创建测试数据
        test_data = {
            "transactionNumber": f"test_debug_{int(time.time())}",
            "businessData": {
                "image": image_to_base64(create_test_image_with_face()),
                "imageType": "base64",
                "advsetValue": {
                    "recvDataType": ["infer_results", "draw_image", "infer_boxes"],
                    "interval": 5
                }
            }
        }
        
        print("  - 调用推理接口...")
        result = detector.infer(test_data, "models.model_manager")
        print(f"  - 推理调用结果: {result}")
        
        # 等待推理完成
        print("  - 等待推理完成...")
        time.sleep(5)
        
        # 检查输出队列
        if not detector.out_que.empty():
            back_json, post_bool, data, model_way = detector.out_que.get()
            print(f"✅ 获取到推理结果:")
            print(f"  - 是否触发报警: {post_bool}")
            if back_json:
                print(f"  - 结果类型: {type(back_json)}")
                if isinstance(back_json, dict):
                    boxes = back_json.get('boxes', {})
                    values = back_json.get('values', [])
                    print(f"  - 检测框数量: {sum(len(box_list) for box_list in boxes.values())}")
                    print(f"  - 消息数量: {len(values)}")
                    
                    # 详细输出检测框
                    for box_type, box_list in boxes.items():
                        print(f"  - {box_type}检测框: {len(box_list)} 个")
                        for i, box in enumerate(box_list[:3]):  # 只显示前3个
                            print(f"    框{i+1}: {box}")
            
            return True
        else:
            print("⚠️ 输出队列为空，可能推理失败或还在进行中")
            return False
        
    except Exception as e:
        print(f"❌ 算法接口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_threshold_settings():
    """检查阈值设置"""
    print("\n🔍 检查阈值设置")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建实例并检查阈值
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        print(f"✅ 当前阈值设置:")
        print(f"  - 置信度阈值: {rknn_inference._confidence}")
        print(f"  - NMS阈值: {rknn_inference._nms_threshold}")
        
        # 检查utils中的默认阈值
        from inference.inference_rknn.utils_rknn.utils import _decode_outputs
        import inspect
        
        # 获取函数签名
        sig = inspect.signature(_decode_outputs)
        print(f"  - _decode_outputs默认阈值:")
        for param_name, param in sig.parameters.items():
            if param.default != inspect.Parameter.empty:
                print(f"    {param_name}: {param.default}")
        
        return True
        
    except Exception as e:
        print(f"❌ 阈值检查失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🚀 人脸检测问题诊断")
    print("=" * 70)
    
    # 初始化检测器
    rknn_inference, model_loaded = test_model_loading()
    
    tests = [
        ("模型加载", lambda: model_loaded),
        ("阈值设置", check_threshold_settings),
        ("直接RKNN推理", lambda: test_direct_rknn_inference(rknn_inference)),
        ("完整推理流程", lambda: test_full_inference_pipeline(rknn_inference)),
        ("算法接口", test_algorithm_interface),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出诊断总结
    print("\n" + "=" * 70)
    print("诊断总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    # 分析问题
    if not results.get("模型加载", False):
        print("\n🔧 问题分析: 模型加载失败")
        print("  - 检查权重文件是否存在")
        print("  - 检查RKNN环境是否正常")
    elif not results.get("直接RKNN推理", False):
        print("\n🔧 问题分析: RKNN推理失败")
        print("  - 可能是置信度阈值过高")
        print("  - 可能是输入图像格式问题")
    elif not results.get("完整推理流程", False):
        print("\n🔧 问题分析: 推理流程问题")
        print("  - 可能是坐标转换问题")
        print("  - 可能是人脸识别环节问题")
    elif not results.get("算法接口", False):
        print("\n🔧 问题分析: 算法接口问题")
        print("  - 可能是队列处理问题")
        print("  - 可能是异步处理问题")
    else:
        print("\n🎉 所有测试通过，人脸检测应该正常工作")

if __name__ == "__main__":
    main()
