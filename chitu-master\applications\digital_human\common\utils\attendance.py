import cv2
import json
import os
import base64
from datetime import datetime, timedelta
from applications.digital_human.common.utils.face import *

# 保存配置的文件路径
# CONFIG_FILE_PATH = "attendance_config.json"
# 程序启动时读取本地配置文件
# def load_attendance_config():
#     if os.path.exists(CONFIG_FILE_PATH):
#         with open(CONFIG_FILE_PATH, "r") as config_file:
#             config_data = json.load(config_file)
#             # print("读取配置文件---------------")
#             return config_data
#     else:
#         return ''

# attendance_data = load_attendance_config()

def extract_face_region(frame, largest_face_box):
    # 假设这个函数已经实现，提取人脸区域
    return frame[largest_face_box[1]:largest_face_box[3], largest_face_box[0]:largest_face_box[2]]

def correct_box(box, image_width, image_height):
    x1, y1, x2, y2 = box
    # 将坐标限制在图像边界内
    x1 = max(0, x1)
    y1 = max(0, y1)
    x2 = min(image_width, x2)
    y2 = min(image_height, y2)

    return [x1, y1, x2, y2]


def crop_to_bounding_rect(image, center_x, center_y, radius):
    """
    将图像按照圆形区域的最小外接矩形进行裁剪。
    
    参数:
    - image (ndarray): 输入的原始图像。
    - center_x (int): 圆心的 x 坐标。
    - center_y (int): 圆心的 y 坐标。
    - radius (int): 圆的半径。
    
    返回:
    - 矩形裁剪后的图像。
    """
    # 计算最小外接矩形的左上角和右下角
    x1 = max(center_x - radius, 0)  # 防止越界
    y1 = max(center_y - radius, 0)  # 防止越界
    x2 = min(center_x + radius, image.shape[1])  # 防止越界
    y2 = min(center_y + radius, image.shape[0])  # 防止越界

    # 裁剪图像到最小外接矩形
    cropped_image = image[y1:y2, x1:x2]

    return cropped_image




def attendance_rec(frame):
    """
    处理考勤模式的启动，进行人脸检测和打卡识别。
    
    参数:
    frame (ndarray): 输入的图像帧，用于人脸检测。

    返回:
        - 状态码 (int): 
            - 0: 没有检测到人脸
            - 1: 人脸不符合占比要求
            - 2: 成功检测到人脸，返回识别结果
        - matched_person (dict): 识别到的匹配人员信息，若无匹配则为 None。
        - confidence (float): 识别的置信度，若无匹配则为 None。
        - frame_data (bytes): 返回编码后的图像数据，用于传输到前端。
    """

    # 设定摄像头分辨率
    camera_width, camera_height = frame.shape[1],frame.shape[0]

    # 加载本地的配置文件，包含前端传递的 attendanceCircle 信息
    # global attendance_data
    # if attendance_data is None:
    #     print("未能加载考勤配置")
    #     return 0, None, None, None  # 无法加载配置，无法进行检测
    # return_img = frame.copy()
    # 将圆形区域映射到摄像头分辨率
    # center_x, center_y, radius = map_to_camera_circle(attendance_data, camera_width, camera_height)
    
    center_x, center_y, radius = frame.shape[1]//2,frame.shape[0]//2,frame.shape[1]//2
    face_img = crop_to_bounding_rect(frame,center_x,center_y,radius)
    
    # 人脸检测
    _, or_img, boxes = detect_faces(face_img)
    largest_face_box = None
    # cv2.circle(return_img,(center_x, center_y),radius,(255,0,0),2)
    _, buffer = cv2.imencode('.jpg', face_img)
    if boxes is not None and len(boxes) > 0:
        # 找到最大的人脸框
        face_box = get_largest_face(boxes)
        largest_face_box = list(map(int, face_box[:4]))  # 只取前四个元素作为坐标

        # 校正人脸框的坐标
        image_height, image_width = frame.shape[:2]
        largest_face_box = correct_box(largest_face_box, image_width, image_height)

        # 判断人脸中心点是否在映射的圆形区域内
        face_center_x = (largest_face_box[0] + largest_face_box[2]) // 2
        face_center_y = (largest_face_box[1] + largest_face_box[3]) // 2

        if is_point_in_circle(face_center_x, face_center_y, center_x, center_y, radius):
            
            # 人脸在圆形范围内，继续执行识别和打卡逻辑
            refine_box = refine_face_bbox(largest_face_box, or_img)
            # face_region = extract_face_region(or_img, refine_box)
            # _, buffer = cv2.imencode('.jpg', face_region)

            # 计算人脸占比
            frame_area = frame.shape[0] * frame.shape[1]
            face_area = (refine_box[2] - refine_box[0]) * (refine_box[3] - refine_box[1])
            face_ratio = face_area / frame_area

            # 如果人脸占比符合要求，则继续识别逻辑
            if face_ratio > 0.02:  # 设定人脸占比的阈值
                matched_person, confidence = face_recognition.recognize_face(face_box, or_img)
                # _, buffer = cv2.imencode('.jpg', face_img)
                return 2, matched_person, confidence, buffer.tobytes()  # 返回检测结果
                # return 2, matched_person, confidence
            else:
                # 人脸占比不符合要求
                # _, buffer = cv2.imencode('.jpg', face_img)
                #print("人脸占比不符合要求")
                return 1, None, None, buffer.tobytes()
                # return 1, None, None
        else:
            # 人脸不在圆形区域内
            # _, buffer = cv2.imencode('.jpg', face_img)
            #print("人脸不在圆形区域内")
            return 0, None, None, buffer.tobytes()
            # return 0, None, None
    else:
        # 没有检测到人脸
        # _, buffer = cv2.imencode('.jpg', face_img)
        # print("没有检测到人脸")
        return 0, None, None, buffer.tobytes()
        # return 0, None, None
    






# 处理前端传来的消息并保存配置
# def handle_attendance_circle_message(data):
#     try:
#         global attendance_data
#         attendance_data = data["data"]
#         # 将数据保存到本地文件
#         with open(CONFIG_FILE_PATH, "w") as config_file:
#             json.dump(attendance_data, config_file, indent=4)  
#         return True
#     except json.JSONDecodeError as e:
#         return False




# 用于从前端传递的 JSON 数据映射圆形区域到摄像头图像上
# def map_to_camera_circle(attendance_data, camera_width, camera_height):
#     """
#     将前端传递的圆形区域映射到摄像头图像（640x480）上。

#     参数:
#     attendance_data (dict): 前端传递的圆形区域数据，包括中心和半径。
#     camera_width (int): 摄像头的宽度（如 640）。
#     camera_height (int): 摄像头的高度（如 480）。

#     返回:
#     tuple: 映射后的圆心坐标 (center_x, center_y) 和半径 radius。
#     """
#     screen_width = attendance_data['screenWidth']
#     screen_height = attendance_data['screenHeight']
    
#     # 计算宽高比例
#     width_ratio = camera_width / screen_width
#     height_ratio = camera_height / screen_height
    
#     # 将前端的圆心和半径映射到摄像头分辨率
#     mapped_center_x = attendance_data['center']['x'] * width_ratio
#     mapped_center_y = attendance_data['center']['y'] * height_ratio
#     mapped_radius = attendance_data['radius'] * width_ratio  # 以宽度的比例映射半径

#     return int(mapped_center_x), int(mapped_center_y), int(mapped_radius)

# 用于检测某个点是否在圆形区域内
def is_point_in_circle(x, y, center_x, center_y, radius):
    """
    判断某个点是否位于给定的圆形区域内。

    参数:
    x (int): 点的 x 坐标。
    y (int): 点的 y 坐标。
    center_x (int): 圆心的 x 坐标。
    center_y (int): 圆心的 y 坐标。
    radius (int): 圆的半径。

    返回:
    bool: 点是否在圆形区域内。
    """
    return (x - center_x) ** 2 + (y - center_y) ** 2 <= radius ** 2


# 示例使用
# if __name__ == "__main__":
    # 模拟接收到的 JSON 消息
    # pass
    # json_message = '''
    # {
    #     "messageType": "attendanceCircle",
    #     "clientType": "screen",
    #     "data": {
    #         "radius": 756,
    #         "center": {
    #             "x": 1080,
    #             "y": 1404
    #         },
    #         "screenWidth": 2160,
    #         "screenHeight": 3840
    #     }
    # }
    # '''
    
    # # 加载本地配置（如果存在）
    # load_attendance_config()
    
    # # 处理并保存从前端传来的 JSON 消息
    # handle_attendance_circle_message(json_message)
    
    # # 再次加载保存的配置
    # config = load_attendance_config()
    # print("最终加载的配置:", config)