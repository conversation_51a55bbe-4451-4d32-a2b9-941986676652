name: windows-x64

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/windows-x64.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/windows-x64.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'

  workflow_dispatch:

concurrency:
  group: windows-x64-${{ github.ref }}
  cancel-in-progress: true

jobs:
  windows_x64:
    name: shared-${{ matrix.shared_lib }} tts-${{ matrix.with_tts }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        shared_lib: [ON, OFF]
        with_tts: [ON, OFF]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure CMake
        shell: bash
        run: |
          mkdir build
          cd build
          cmake -A x64 -DSHERPA_ONNX_ENABLE_TTS=${{ matrix.with_tts }} -D CMAKE_BUILD_TYPE=Release -D BUILD_SHARED_LIBS=${{ matrix.shared_lib }} -DCMAKE_INSTALL_PREFIX=./install ..

      - name: Build sherpa-onnx for windows
        shell: bash
        run: |
          cd build
          cmake --build . --config Release -- -m:2
          cmake --build . --config Release --target install -- -m:2

          ls -lh ./bin/Release/sherpa-onnx.exe

      - name: Test C API
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export SLID_EXE=spoken-language-identification-c-api.exe
          export SID_EXE=speaker-identification-c-api.exe

          .github/scripts/test-c-api.sh

      - name: Test spoken language identification (C++ API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline-language-identification.exe

          .github/scripts/test-spoken-language-identification.sh

      - name: Test online CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-ctc.sh

      - name: Test offline TTS
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline-tts.exe

          .github/scripts/test-offline-tts.sh

      - name: Test online paraformer for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-paraformer.sh

      - name: Test offline Whisper for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-whisper.sh

      - name: Test offline CTC for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-ctc.sh

      - name: Test offline transducer for Windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-transducer.sh

      - name: Test online transducer for Windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-transducer.sh

      - name: Test online transducer (C API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=decode-file-c-api.exe

          .github/scripts/test-online-transducer.sh

      - name: Copy files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          shared_lib=${{ matrix.shared_lib }}
          if [[ $shared_lib == "ON" ]]; then
            suffix=shared
          else
            suffix=static
          fi

          if [[ ${{ matrix.with_tts }} ]]; then
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-win-x64-$suffix
          else
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-win-x64-$suffix-no-tts
          fi

          mkdir $dst

          cp -a build/install/bin $dst/
          cp -a build/install/lib $dst/
          cp -a build/install/include $dst/

          tar cjvf ${dst}.tar.bz2 $dst

      # https://huggingface.co/docs/hub/spaces-github-actions
      - name: Publish to huggingface
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && (github.event_name == 'push' || github.event_name == 'workflow_dispatch')
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-libs huggingface

            cd huggingface
            git lfs pull
            mkdir -p win64

            cp -v ../sherpa-onnx-*.tar.bz2 ./win64

            git status
            git lfs track "*.bz2"

            git add .

            git commit -m "upload sherpa-onnx-${SHERPA_ONNX_VERSION}"

            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-libs main

      - name: Release pre-compiled binaries and libs for Windows x64
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*-win-x64*.tar.bz2
