"""
集成了对 Pear Admin Flask 二次开发的的邮件操作，并给了相对应的示例。
"""
from flask import current_app
from flask_mail import Message

from applications.common.curd import model_to_dicts
from applications.common.helper import ModelFilter
from applications.extensions import db, flask_mail
from applications.extensions.init_mail import mail
from applications.models import Mail
from applications.schemas import MailOutSchema
from log import mylog


def get_all(receiver=None, subject=None, content=None):
    """
    获取邮件

    返回的列表中的字典构造如下::

        {
            "content": "",  # html内容
            "create_at": "2022-12-25T10:51:17",  # 时间
            "id": 17,  # 邮件ID
            "realname": "超级管理",  # 创建者
            "receiver": "",  # 接收者
            "subject": ""  # 主题
        }

    :param receiver: 发送者
    :param subject: 邮件标题
    :param content: 邮件内容
    :return: 列表
    """
    # 查询参数构造
    mf = ModelFilter()
    if receiver:
        mf.contains(field_name="receiver", value=receiver)
    if subject:
        mf.contains(field_name="subject", value=subject)
    if content:
        mf.exact(field_name="content", value=content)
    # orm查询
    # 使用分页获取data需要.items
    mail = Mail.query.filter(mf.get_filter(Mail)).layui_paginate()
    return model_to_dicts(schema=MailOutSchema, data=mail.items)


def add(receiver, subject, content, user_id):
    """
    发送一封邮件，若发送成功立刻提交数据库。

    :param receiver: 接收者 多个用英文分号隔开
    :param subject: 邮件主题
    :param content: 邮件 html
    :param user_id: 发送用户ID（谁发送的？） 可以用 from flask_login import current_user ; current_user.id 来表示当前登录用户
    :return: 成功与否
    """
    try:
        msg = Message(subject=subject, recipients=receiver.split(";"), html=content)
        flask_mail.send(msg)
    except BaseException as e:
        current_app.log_exception(e)
        return False

    mail = Mail(receiver=receiver, subject=subject, content=content, user_id=user_id)

    db.session.add(mail)
    db.session.commit()
    return True


def delete(id):
    """
    删除邮件记录，立刻写入数据库。

    :param id: 邮件ID
    :return: 成功与否
    """
    res = Mail.query.filter_by(id=id).delete()
    if not res:
        return False
    db.session.commit()
    return True

def send_mail(subject, recipients, content):
    """原发送邮件函数，不会记录邮件发送记录

    失败报错，请注意使用 try 拦截。

    :param subject: 主题
    :param recipients: 接收者 多个用英文分号隔开
    :param content: 邮件 html
    """
    message = Message(subject=subject, recipients=recipients, html=content)
    mail.send(message)


def validate_email(mail_server, mail_port, mail_use_ssl, mail_use_tls, mail_username, mail_password):
    
    try:
            # 发送测试邮件
            msg = Message('测试邮件', sender=mail_username, recipients=[mail_username])
            msg.body = '这是一封测试邮件。'
            mail.server = mail_server
            mail.port = mail_port
            mail.username = mail_username
            mail.password = mail_password
            mail.send(msg)
            return "邮箱验证成功"
    except Exception as e:
            error_message = str(e)
            return "邮箱验证失败"


def get_email_config_by_type(email_type):
    # print("获取邮箱类型")
    # return None

    for config in current_app.config['EMAIL_CONFIGURATIONS']:
        if config.get('type') == email_type:
            return config
    return None



import imaplib
import email
from email.header import decode_header
from datetime import datetime, timedelta

def decode_subject(encoded_subject):
    decoded_fragments = decode_header(encoded_subject)
    decoded_subject = ''
    for fragment, encoding in decoded_fragments:
        if isinstance(fragment, bytes):
            if encoding == 'unknown-8bit':
                try:
                    fragment = fragment.decode('utf-8', errors='ignore')
                except UnicodeDecodeError:
                    fragment = fragment.decode('latin1', errors='ignore')
            else:
                fragment = fragment.decode(encoding or 'utf-8', errors='ignore')
        decoded_subject += fragment
    return decoded_subject

async def check_bounced_emails(email_address,email_password,email_type,recipient_emails):
    # QQ邮箱IMAP服务器地址
    try:
        imap_config = get_email_config_by_type(email_type)
        imap_server = imap_config['imap_server']
        imap_port = 993

        mylog.info("开始登录")
        mail = imaplib.IMAP4_SSL(imap_server, imap_port)
        mail.login(email_address, email_password)
        mylog.info("登录成功")
        mail.select('inbox')

        current_time = datetime.utcnow()
        time_10_seconds_ago = current_time - timedelta(seconds=1)
        since_time = time_10_seconds_ago.strftime("%d-%b-%Y")

        status, messages = mail.search(None, f'SINCE {since_time}')
        email_ids = messages[0].split()[-5:]
        mylog.info(email_ids)

        for email_id in email_ids:
            mylog.info(email_id)
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            for response_part in msg_data:
                if isinstance(response_part, tuple):
                    msg = email.message_from_bytes(response_part[1])
                    # mylog.info(f"msg: {msg}")
                    subject = decode_subject(msg['Subject'])
                    mylog.info(f"subject: {subject}")

                    if "退信" in subject or "Undelivered Mail Returned to Sender" in subject:
                        if msg.is_multipart():
                            for part in msg.walk():
                                content_type = part.get_content_type()
                                content_disposition = str(part.get("Content-Disposition"))
                                if "attachment" not in content_disposition:
                                    if content_type == "text/plain":
                                        try:
                                            body = part.get_payload(decode=True).decode(part.get_content_charset(), errors='ignore')
                                            mylog.info(body)
                                            
                                            if recipient_emails in body:
                                                    mylog.info(f"Bounced email address: {recipient_emails}")
                                                    return recipient_emails
                                        except LookupError:
                                            body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                            mylog.info("Body (with ignored errors):", body)
                                    elif content_type == "text/html":
                                        try:
                                            body = part.get_payload(decode=True).decode(part.get_content_charset(), errors='ignore')
                                            # mylog.info("HTML Body:", body)
                                            
                                            if recipient_emails in body:
                                                    mylog.info(f"Bounced email address: {recipient_emails}")
                                                    return recipient_emails
                                        except LookupError:
                                            body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                            mylog.info("HTML Body (with ignored errors):", body)
                        else:
                            body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                            mylog.info("Body:", body)
                           
                            if recipient_emails in body:
                                    mylog.info(f"Bounced email address: {recipient_emails}")
                                    return recipient_emails
    except Exception as e:
        mylog.info(e)
    finally:
        mail.logout()
    
    return None