import sounddevice as sd
import soundfile as sf
import numpy as np

# 设置录音参数
samplerate = 44100
channels = 2
devices = sd.query_devices()
sd.default.device[0] = 0
default_input_device_idx = sd.default.device[0]

# 创建输入流
stream = sd.InputStream(samplerate=samplerate, dtype="float32", channels=channels)

# 开始流
stream.start()

# 准备用于存储音频数据的列表
audio_data = []

try:
    print("正在录制...")
    while True:
         # 从流中读取数据块
        data, overflowed = stream.read(4410)
        audio_data.append(data)
except KeyboardInterrupt:
    print("录制停止。")    

# 停止流
stream.stop()

# 将音频数据合并为一个数组
recording = np.concatenate(audio_data, axis=0)

# 保存录音文件
filename = "output.wav"
sf.write(filename, recording, samplerate)

print(f"录音已保存为 {filename}。")
