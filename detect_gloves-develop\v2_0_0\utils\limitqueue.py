import multiprocessing
from queue import Empty 
from multiprocessing.queues import Queue

class LimitedQueue(Queue):
    """
        任务队列类，任务队列满时自动弹出队首元素
    """
    def __init__(self, maxsize, *args, **kwargs):
        ctx = kwargs.get('ctx', multiprocessing.get_context())
        super().__init__(maxsize, ctx=ctx)
        self.maxsize = maxsize
    
    def put(self, item, *args, **kwargs):
        try:
            if self.qsize() >= self.maxsize:
                self.get_nowait()   # 队列满时弹出队首元素
        except Empty:
            pass                    # 队列为空时直接跳过
        super().put(item, *args, **kwargs)