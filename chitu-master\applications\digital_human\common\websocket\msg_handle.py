import time
import json
from log import mylog

from applications.mqtt.mqtt_connect import mqtt_client, SN
from applications.digital_human.common.utils.stranger_rec import stranger_detect, basic_settings_config_path
from applications.digital_human.common.utils.qa_service import start_qa_service
from applications.digital_human.common.websocket.get_basic_info import read_config, get_question_guide, read_version_info
from applications.digital_human.common.utils.mode_change import enter_assistant_mode, enter_unattended_mode, enter_propagate_mode, exit_propagate_mode

def update_safeguard_status(status):
    conf = read_config()
    if status:
        if "safeguard" not in conf["currentStatus"]:
            conf["currentStatus"].append("safeguard")
    else:
        if "safeguard" in conf["currentStatus"]:
            conf["currentStatus"].remove("safeguard")
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)


async def message_handle(recv_text, clients, app):
    
    # 屏幕端消息处理
    if recv_text["clientType"] == "screen":
        
        if recv_text["messageType"] == "replyStatus":
            if "algorithm" in clients.keys():
                await clients["algorithm"].send(json.dumps(recv_text))
            else:
                mylog.info("WEBSOCKET: 接收到前端回复消息，但是算法客户端不在线.")
                
        elif recv_text["messageType"] == "attendanceCircle":
            # from applications.digital_human.view.attendance import handle_attendance_circle_message
            # handle_attendance_circle_message(recv_text)
            pass
        
        elif recv_text["messageType"] == "enterDevOps":
            stranger_detect.stopAllDet()
            if "algorithm" in clients.keys():
                await clients["algorithm"].send(json.dumps(recv_text))
            # 向前端发送版本信息
            version_info = read_version_info()
            send_verison_info = {
                "messageType": "devOpsMessageData",
                "messageBody": {
                    "versionInfo": {
                        "version": version_info["currentVersion"],
                        "newVersion": version_info["newVersion"]["version"]
                    }
                }
            }
            await clients["screen"].send(json.dumps(send_verison_info))
            update_safeguard_status(True)
        
        elif recv_text["messageType"] == "exitDevOps":
            stranger_detect.recoverDet(clients)
            if "algorithm" in clients.keys():
                await clients["algorithm"].send(json.dumps(recv_text))
            update_safeguard_status(False)
        
        elif recv_text["messageType"] == "updateVersion":
            from applications.digital_human.common.utils.system_update import update_digitalHuman_system
            await update_digitalHuman_system(clients)
        
        elif recv_text["messageType"] == "ping":
            mylog.info("WEBSOCKET: 接收到前端心跳.")
        
        else:
            mylog.info("WEBSOCKET: message error.")
    
    # 算法端消息处理
    elif recv_text["clientType"] == "algorithm":

        if recv_text["messageType"] == "exitQaService":
            if "propagate" not in read_config()["currentStatus"]:
                stranger_detect.startPeopleDet(clients)
                mylog.info("人脸唤醒功能开启...")
            else:
                mylog.info("目前处于宣传模块，人脸唤醒功能不开启...")
            
            sleepy_msg = {
                "messageType": "messageToast",
                "messageBody": {
                    "mode": "9",
                    "code": 200
                }
            }
            await clients["screen"].send(json.dumps(sleepy_msg))
        
        elif recv_text["messageType"] == "restartQaService":
            need_msg = start_qa_service()
            if need_msg:
                warnMsg = {
                    "messageType": "messageToast",
                    "messageBody": {
                        "mode": "7",
                        "code": 300,
                        "text": "服务正在启动..."
                    }
                }
                await clients["screen"].send(json.dumps(warnMsg))
            
        elif recv_text["messageType"] == "startQaService":
            stranger_detect.stopPeopleDet()
          
        elif recv_text["messageType"] == "keyWordPush" and read_config()["currentMode"] == "UnattendedMode":
            # 关键词触发
            push_msg = {
                "messageType": "keyWordPush",
                "messageBody": {
                    "serialNumber": SN,
                    "alarmTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()),
                    "content": recv_text["content"]
                }
            }
            mqtt_client.publish("digitalHumanTopic", push_msg)
            mylog.info("语音识别：触发关键词......")
            
        elif recv_text["messageType"] in ["questionPush", "answerPush", "awakenGreeting", "muteItGreeting", "unmuteItGreeting", "attendanceMode", "sceenExitAttendance", "screenErrorNone", "messageToast", "questionPushEnd", "questionPushRetract", "questionGuide"]:
            if "screen" in clients.keys():
                await clients["screen"].send(json.dumps(recv_text))
                if recv_text["messageType"] == "answerPush":
                    await clients["screen"].send(str(get_question_guide(recv_text["messageBody"]["id"], app)))
                if recv_text["messageType"] == "attendanceMode":
                    stranger_detect.startAttendanceDet(clients)
                if recv_text["messageType"] == "sceenExitAttendance":
                    stranger_detect.stopAttendanceDet()
                if recv_text["messageType"] == "messageToast":
                    mode = str(recv_text["messageBody"]["mode"])
                    conf = read_config()
                    last_mode = conf["currentMode"]
                    if mode == "1":
                        _ = enter_unattended_mode(last_mode)
                    elif mode == "2":
                        _ = enter_assistant_mode(last_mode)
                    elif mode == "5":
                        enter_propagate_mode()
                        enter_propagate_msg = {
                            "messageType": "replyStatus",
                            "propagandaStatus": "1",
                            "propagandaMessage": "进入宣传模式"
                        }
                        await clients["algorithm"].send(json.dumps(enter_propagate_msg))
                    elif mode == "6":
                        if "propagate" in conf["currentStatus"]:
                            exit_propagate_mode(clients)
                        exit_propagate_msg = {
                            "messageType": "replyStatus",
                            "propagandaStatus": "0",
                            "propagandaMessage": "退出宣传模式"
                        }
                        await clients["algorithm"].send(json.dumps(exit_propagate_msg))
                    else:
                        pass

            # else:
            #     mylog.info("WEBSOCKET: screen client is not online.")
        elif recv_text["messageType"] == "muteSetting":
            if "screen" in clients.keys():
                recv_text["clientType"] = "screen"
                await clients["screen"].send(json.dumps(recv_text))
            else:
                mylog.info("WEBSOCKET: 前端客户端不在线。")
                
        elif recv_text["messageType"] == "ping":
            mylog.info("WEBSOCKET: 接收到语音算法的心跳。")
        else:
            mylog.info("WEBSOCKET: message error.")
    else:
        mylog.info("WEBSOCKET: clientType is error.")