#!/usr/bin/env python3
"""
测试人脸识别功能
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
import time

def create_test_image_with_face():
    """创建包含人脸的测试图像"""
    img = np.ones((480, 640, 3), dtype=np.uint8) * 128
    
    # 绘制一个简单的"人脸"
    cv2.rectangle(img, (200, 150), (400, 350), (220, 220, 220), -1)
    cv2.circle(img, (250, 200), 15, (50, 50, 50), -1)
    cv2.circle(img, (350, 200), 15, (50, 50, 50), -1)
    cv2.rectangle(img, (295, 230), (305, 260), (180, 180, 180), -1)
    cv2.rectangle(img, (270, 290), (330, 310), (100, 100, 100), -1)
    
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    return base64.b64encode(buffer).decode('utf-8')

def test_face_database_loading():
    """测试人脸库加载"""
    print("🔍 测试人脸库加载")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建RKNN推理实例
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        print("✅ RKNN推理实例创建成功")
        
        # 检查人脸库状态
        has_faiss = hasattr(rknn_inference, 'faiss_index') and rknn_inference.faiss_index is not None
        has_id_map = hasattr(rknn_inference, 'id_name_map') and rknn_inference.id_name_map
        
        print(f"  - FAISS索引状态: {'✅ 已加载' if has_faiss else '❌ 未加载'}")
        if has_faiss:
            print(f"  - FAISS索引包含: {rknn_inference.faiss_index.ntotal} 个人脸特征")
        
        print(f"  - ID映射状态: {'✅ 已加载' if has_id_map else '❌ 未加载'}")
        if has_id_map:
            print(f"  - ID映射包含: {len(rknn_inference.id_name_map)} 个人员")
            for idx, (key, value) in enumerate(list(rknn_inference.id_name_map.items())[:3]):
                print(f"    - 人员{idx+1}: ID={key}, 信息={value}")
        
        print(f"  - 人脸识别模型: {'✅ 已加载' if rknn_inference.recognition_model else '❌ 未加载'}")
        
        return rknn_inference, True
        
    except Exception as e:
        print(f"❌ 人脸库加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def test_face_recognition_modes(detector):
    """测试不同的人脸识别模式"""
    print("\n🔍 测试人脸识别模式")
    print("=" * 50)
    
    if not detector:
        print("❌ 检测器未初始化，跳过测试")
        return False
    
    modes = ["authorized", "unauthorized"]
    
    for mode in modes:
        try:
            print(f"\n  测试模式: {mode}")
            
            test_data = {
                "transactionNumber": f"test_{mode}_{int(time.time())}",
                "businessData": {
                    "image": image_to_base64(create_test_image_with_face()),
                    "imageType": "base64",
                    "advsetValue": {
                        "faceAlarm": mode,
                        "recvDataType": ["infer_results", "draw_image", "infer_boxes"],
                        "interval": 5
                    }
                }
            }
            
            # 调用推理接口
            result = detector.infer(test_data, "models.model_manager")
            print(f"    推理调用结果: {result}")
            
            # 等待推理完成
            time.sleep(3)
            
            # 检查输出队列
            if not detector.out_que.empty():
                back_json, post_bool, data, model_way = detector.out_que.get()
                print(f"    是否触发报警: {post_bool}")
                if back_json:
                    values = back_json.get('values', [])
                    print(f"    检测结果: {[v.get('value', 'unknown') for v in values]}")
                    
                    # 检查检测框信息
                    boxes = back_json.get('boxes', {})
                    for box_type, box_list in boxes.items():
                        print(f"    {box_type}检测框: {len(box_list)} 个")
            else:
                print("    ⚠️ 输出队列为空")
                
        except Exception as e:
            print(f"    ❌ 模式 {mode} 测试失败: {e}")
    
    return True

def test_face_feature_extraction():
    """测试人脸特征提取"""
    print("\n🔍 测试人脸特征提取")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建RKNN推理实例
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        # 创建测试图像和人脸框
        test_img = create_test_image_with_face()
        test_face_box = [200, 150, 400, 350, 0.9]  # [x1, y1, x2, y2, conf]
        
        print("✅ 开始特征提取测试")
        print(f"  - 测试图像尺寸: {test_img.shape}")
        print(f"  - 测试人脸框: {test_face_box}")
        
        # 测试特征提取
        feature_vector = rknn_inference._extract_face_features(test_img, test_face_box)
        
        if feature_vector is not None:
            print(f"✅ 特征提取成功")
            print(f"  - 特征向量维度: {feature_vector.shape}")
            print(f"  - 特征向量范围: [{np.min(feature_vector):.3f}, {np.max(feature_vector):.3f}]")
            
            # 测试人脸库搜索
            if hasattr(rknn_inference, 'faiss_index') and rknn_inference.faiss_index:
                match_result = rknn_inference._search_face_database(feature_vector)
                if match_result:
                    print(f"✅ 人脸库匹配成功: {match_result}")
                else:
                    print("✅ 人脸库搜索完成，未找到匹配（陌生人）")
            else:
                print("⚠️ 人脸库未加载，跳过搜索测试")
        else:
            print("❌ 特征提取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 特征提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 人脸识别功能测试")
    print("=" * 70)
    
    # 初始化检测器
    detector, init_success = test_face_database_loading()
    
    tests = [
        ("人脸库加载", lambda: init_success),
        ("特征提取", test_face_feature_extraction),
        ("识别模式", lambda: test_face_recognition_modes(detector)),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 人脸识别功能正常！")
        print("\n📋 功能确认:")
        print("  ✅ 人脸库正确加载")
        print("  ✅ 特征提取正常工作")
        print("  ✅ 授权/非授权模式正常")
    else:
        print("⚠️ 部分功能存在问题")

if __name__ == "__main__":
    main()
