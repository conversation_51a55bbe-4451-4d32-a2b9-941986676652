#!/usr/bin/env python3
"""
测试导入修复的脚本
"""

import sys
import os
import importlib

def test_main_import():
    """测试main.py的导入"""
    print("🔍 测试main.py导入...")
    
    try:
        # 模拟registry.py的导入方式
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        # 尝试导入main模块
        main_module = importlib.import_module('main')
        print("✅ main模块导入成功")
        
        # 尝试调用load函数
        if hasattr(main_module, 'load'):
            print("✅ load函数存在")
            # 不实际调用，因为可能需要模型文件
            # result = main_module.load()
            # print(f"✅ load函数调用成功: {result}")
        else:
            print("❌ load函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ main模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utils_import():
    """测试utils模块导入"""
    print("\n🔍 测试utils模块导入...")
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        # 尝试导入utils模块
        utils_module = importlib.import_module('utils')
        print("✅ utils模块导入成功")
        
        # 检查关键类
        if hasattr(utils_module, 'DetectFaceAlarm'):
            print("✅ DetectFaceAlarm类存在")
        else:
            print("❌ DetectFaceAlarm类不存在")
            return False
        
        if hasattr(utils_module, 'mylog'):
            print("✅ mylog存在")
        else:
            print("❌ mylog不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ utils模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_modules():
    """测试各个模块的单独导入"""
    print("\n🔍 测试各个模块单独导入...")
    
    modules_to_test = [
        'utils.log',
        'utils.i18n',
        'preprocess.detect_preprocess',
        'postprocess.post_process',
        'inference.model_check',
        'task.image_alarm_interval'
    ]
    
    sys.path.insert(0, 'detect_face_alarm/v2_0_0')
    
    all_passed = True
    for module_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name} 导入成功")
        except Exception as e:
            print(f"❌ {module_name} 导入失败: {e}")
            all_passed = False
    
    return all_passed

def test_face_recognition_class():
    """测试DetectFaceAlarm类的导入"""
    print("\n🔍 测试DetectFaceAlarm类导入...")
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from utils.face_recognition import DetectFaceAlarm
        print("✅ DetectFaceAlarm类直接导入成功")
        
        # 检查类的方法
        required_methods = ['infer', 'start_task', 'stop_task', 'get_task_list']
        for method in required_methods:
            if hasattr(DetectFaceAlarm, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ DetectFaceAlarm类导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 测试导入修复")
    print("=" * 60)
    
    tests = [
        ("main.py导入", test_main_import),
        ("utils模块导入", test_utils_import),
        ("各个模块单独导入", test_individual_modules),
        ("DetectFaceAlarm类导入", test_face_recognition_class),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有导入测试通过！")
        print("\n📋 修复完成的内容:")
        print("  ✅ main.py可以正常导入")
        print("  ✅ utils模块可以正常导入")
        print("  ✅ 所有子模块可以正常导入")
        print("  ✅ DetectFaceAlarm类可以正常导入")
        
        print("\n🚀 现在应该可以:")
        print("  1. 正常启动app.py")
        print("  2. 注册模型成功")
        print("  3. 运行人脸检测算法")
    else:
        print("⚠️ 部分导入测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
