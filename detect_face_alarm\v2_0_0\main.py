import json
import time
import os
import sys

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 添加当前目录到Python路径
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 直接导入face_recognition模块，避免通过utils包
sys.path.insert(0, os.path.join(current_dir, 'utils'))
from face_recognition import DetectFaceAlarm

def load():
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config/documentation.json'), 'r', encoding='utf-8') as file:
        docu = json.load(file)
    model_name = docu["modelName"]
    model_version = docu["modelVersion"]
    algo_name = docu["algoSet"][0]["algoName"]
    return {model_name+ ":" + model_version + ":" + algo_name: DetectFaceAlarm(model_name, model_version, algo_name)}


