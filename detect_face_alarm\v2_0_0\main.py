import json
import time
import os
import sys

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 添加当前目录到Python路径
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 调试信息 - 帮助诊断问题
print(f"[DEBUG] main.py 当前目录: {current_dir}")
print(f"[DEBUG] main.py utils目录存在: {os.path.exists(os.path.join(current_dir, 'utils'))}")
print(f"[DEBUG] main.py face_recognition.py存在: {os.path.exists(os.path.join(current_dir, 'utils', 'face_recognition.py'))}")

# 尝试导入DetectFaceAlarm
try:
    from utils.face_recognition import DetectFaceAlarm
    print("[DEBUG] DetectFaceAlarm导入成功")
except ImportError as e:
    print(f"[ERROR] DetectFaceAlarm导入失败: {e}")
    # 尝试备用导入方法
    try:
        import utils.face_recognition
        DetectFaceAlarm = utils.face_recognition.DetectFaceAlarm
        print("[DEBUG] 使用备用方法导入DetectFaceAlarm成功")
    except Exception as e2:
        print(f"[ERROR] 备用导入方法也失败: {e2}")
        raise ImportError(f"无法导入DetectFaceAlarm: {e}")
except Exception as e:
    print(f"[ERROR] 导入DetectFaceAlarm时发生未知错误: {e}")
    raise

def load():
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config/documentation.json'), 'r', encoding='utf-8') as file:
        docu = json.load(file)
    model_name = docu["modelName"]
    model_version = docu["modelVersion"]
    algo_name = docu["algoSet"][0]["algoName"]
    return {model_name+ ":" + model_version + ":" + algo_name: DetectFaceAlarm(model_name, model_version, algo_name)}


