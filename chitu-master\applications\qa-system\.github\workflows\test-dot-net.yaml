name: test-dot-net

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/test-dot-net.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'dotnet-examples/**'
      - 'scripts/dotnet/**'

  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/test-dot-net.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'dotnet-examples/**'
      - 'scripts/dotnet/**'

  workflow_dispatch:

concurrency:
  group: test-dot-net
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build-libs:
    name: ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        python-version: ["3.8"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-release-shared

      - name: Build sherpa-onnx
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          mkdir build
          cd build
          cmake -DBUILD_SHARED_LIBS=ON -DCMAKE_INSTALL_PREFIX=./install -DCMAKE_BUILD_TYPE=Release ..
          cmake --build . --target install --config Release

      - name: Build sherpa-onnx for windows x86
        if: matrix.os == 'windows-latest'
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          mkdir build-win32
          cd build-win32
          cmake -A Win32 -DBUILD_SHARED_LIBS=ON -DCMAKE_INSTALL_PREFIX=./install -DCMAKE_BUILD_TYPE=Release ..
          cmake --build . --target install --config Release

      - uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.os }}
          path: ./build/install/lib/

      - uses: actions/upload-artifact@v4
        if: matrix.os == 'windows-latest'
        with:
          name: ${{ matrix.os }}-win32
          path: ./build-win32/install/lib/

  test-dot-net:
    runs-on: ${{ matrix.os }}
    needs: [build-libs]
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest] #, windows-latest]
        python-version: ["3.8"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Python dependencies
        shell: bash
        run: |
          python3 -m pip install --upgrade pip Jinja2

      - name: Retrieve artifact from ubuntu-latest
        uses: actions/download-artifact@v4
        with:
          name: ubuntu-latest
          path: /tmp/linux

      - name: Retrieve artifact from macos-latest
        uses: actions/download-artifact@v4
        with:
          name: macos-latest
          path: /tmp/macos

      - name: Retrieve artifact from windows-latest
        uses: actions/download-artifact@v4
        with:
          name: windows-latest
          path: /tmp/windows-x64

      - name: Retrieve artifact from windows-latest
        uses: actions/download-artifact@v4
        with:
          name: windows-latest-win32
          path: /tmp/windows-x86

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: |
            6.0.x
            7.0.x

      - name: Check dotnet
        run: dotnet --info

      - name: Display files
        shell: bash
        run: |
          echo "----------/tmp/----------"
          ls -lh /tmp/

          echo "----------/tmp/linux----------"
          ls -lh /tmp/linux

          echo "----------/tmp/macos----------"
          ls -lh /tmp/macos

          echo "----------/tmp/windows-x64----------"
          ls -lh /tmp/windows-x64

          echo "----------/tmp/windows-x86----------"
          ls -lh /tmp/windows-x86

      - name: Build
        shell: bash
        run: |
          cd scripts/dotnet
          ./run.sh

      - name: Copy files
        shell: bash
        run: |
          cp -v scripts/dotnet/examples/offline-tts.csproj dotnet-examples/offline-tts/
          cp -v scripts/dotnet/examples/offline-decode-files.csproj dotnet-examples/offline-decode-files/
          cp -v scripts/dotnet/examples/online-decode-files.csproj dotnet-examples/online-decode-files/
          cp -v scripts/dotnet/examples/speech-recognition-from-microphone.csproj dotnet-examples/speech-recognition-from-microphone/
          cp -v scripts/dotnet/examples/spoken-language-identification.csproj dotnet-examples/spoken-language-identification/
          cp -v scripts/dotnet/examples/streaming-hlg-decoding.csproj dotnet-examples/streaming-hlg-decoding

          ls -lh /tmp

      - name: Run tests
        shell: bash
        run: |
          .github/scripts/test-dot-net.sh

      - uses: actions/upload-artifact@v4
        with:
          name: dot-net-tts-generated-test-files-${{ matrix.os }}
          path: tts
