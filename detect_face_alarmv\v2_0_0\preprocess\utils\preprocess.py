import numpy as np
import cv2
from ...utils.log import mylog

class PreProcess(object):
    
    def __init__(self):
        pass
        
    def auto_resize(self, img, new_w, new_h):
        """按照detect_play_phone的auto_resize实现"""
        h, w = img.shape[:2]
        scale = min(new_w / w, new_h / h)
        new_size = tuple(map(int, np.array(img.shape[:2][::-1]) * scale))
        return cv2.resize(img, new_size), scale

    def letterbox(self, img, new_wh=(640, 640), color=(128, 128, 128)):
        """完全按照detect_play_phone的letterbox实现"""
        mylog.info(f"[LETTERBOX] 输入图像: {img.shape}, 目标尺寸: {new_wh}")

        # 先进行颜色空间转换（与detect_play_phone保持一致）
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 使用detect_play_phone的resize方式
        new_img, scale = self.auto_resize(img_rgb, *new_wh)
        nh, nw, _ = new_img.shape
        iw, ih = new_wh
        dw, dh = (iw - nw) / 2, (ih - nh) / 2

        # 使用detect_play_phone的padding方式
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        image_paded = cv2.copyMakeBorder(new_img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)

        mylog.info(f"[LETTERBOX] 缩放比例: {scale}, 填充: dw={dw}, dh={dh}")
        mylog.info(f"[LETTERBOX] 输出图像: {image_paded.shape}")

        return image_paded, [scale, dw, dh]
