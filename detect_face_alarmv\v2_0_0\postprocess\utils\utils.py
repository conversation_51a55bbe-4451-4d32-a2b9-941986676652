import numpy as np 
import json
import time
import cv2

# 生成告警信息，
# 绘制图片
# 封装json

def make_json(post_mess):
    """创建标准的JSON输出格式 - 按照其他算法的格式"""
    model_name, model_version, algo_name = post_mess
    payload = {
        "modelName": model_name,
        "modelVersion": model_version,
        "algoName": algo_name,
        "dateTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        "values": [],
        "boxes": {
            "face": []    # 人脸检测框
        },
        "image": "",
        "rawImage": ""
    }
    return payload

def draw_face_boxes(image, draw_boxes):
    """
    在图像上绘制人脸检测框 - 按照标准坐标格式
    Args:
        image: 原始图像
        draw_boxes: 检测框列表，格式为 [x1, y1, x2, y2, score, class_name]
    Returns:
        image: 绘制后的图像
    """
    for box in draw_boxes:
        if len(box) >= 6:
            x1, y1, x2, y2, score, cl = box[:6]

            # 确保坐标为整数并进行边界检查
            h, w = image.shape[:2]
            x1 = max(0, min(int(x1), w - 1))
            y1 = max(0, min(int(y1), h - 1))
            x2 = max(0, min(int(x2), w - 1))
            y2 = max(0, min(int(y2), h - 1))

            # 确保坐标有效性
            if x2 > x1 and y2 > y1:
                # 绘制矩形框 - 使用标准坐标顺序 (x1,y1) -> (x2,y2)
                image = cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制标签文本
                label = '{0} {1:.2f}'.format(cl, score)
                text_y = y1 - 10 if y1 > 20 else y1 + 20
                image = cv2.putText(image, label, (x1, text_y),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    return image
