name: riscv64-linux

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/riscv64-linux.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
      - 'toolchains/riscv64-linux-gnu.toolchain.cmake'
      - 'build-riscv64-linux-gnu.sh'
    tags:
      - '*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/riscv64-linux.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'toolchains/riscv64-linux-gnu.toolchain.cmake'
      - 'sherpa-onnx/c-api/*'
      - 'build-riscv64-linux-gnu.sh'

  workflow_dispatch:

concurrency:
  group: riscv64-linux-${{ github.ref }}
  cancel-in-progress: true

jobs:
  riscv64_linux:
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os }} ${{ matrix.lib_type }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        lib_type: [shared] #, static]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-riscv64-${{ matrix.lib_type }}

      - name: cache-qemu
        id: cache-qemu
        uses: actions/cache@v4
        with:
          path: qemu-install
          key: qemu-riscv-xuantie-install-20240306

      - name: qemu
        if: steps.cache-qemu.outputs.cache-hit != 'true'
        run: |
          # https://pypi.org/project/xuantie-qemu/#files
          wget -q https://files.pythonhosted.org/packages/21/f4/733f29c435987e8bb264a6504c7a4ea4c04d0d431b38a818ab63eef082b9/xuantie_qemu-20230825-py3-none-manylinux1_x86_64.whl
          unzip xuantie_qemu-20230825-py3-none-manylinux1_x86_64.whl
          mkdir -p qemu-install/bin

          cp -v ./qemu/qemu-riscv64 ./qemu-install/bin

      - name: cache-toolchain
        id: cache-toolchain
        uses: actions/cache@v4
        with:
          path: toolchain
          key: Xuantie-900-gcc-linux-5.10.4-glibc-x86_64-V2.6.1-20220906.tar.gz

      - name: Download toolchain
        if: steps.cache-toolchain.outputs.cache-hit != 'true'
        shell: bash
        run: |
          wget -q https://occ-oss-prod.oss-cn-hangzhou.aliyuncs.com/resource//1663142514282/Xuantie-900-gcc-linux-5.10.4-glibc-x86_64-V2.6.1-20220906.tar.gz

          mkdir $GITHUB_WORKSPACE/toolchain

          tar xvf ./Xuantie-900-gcc-linux-5.10.4-glibc-x86_64-V2.6.1-20220906.tar.gz --strip-components 1 -C $GITHUB_WORKSPACE/toolchain
          ls -lh $GITHUB_WORKSPACE/toolchain/bin

      - name: Display toolchain info
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          riscv64-unknown-linux-gnu-gcc --version

      - name: Display qemu-riscv64 -h
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/sysroot
          qemu-riscv64 -h

      - name: build riscv64-linux
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH

          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"

          cmake --version

          lib_type=${{ matrix.lib_type }}

          if [[ $lib_type == "shared" ]]; then
            export BUILD_SHARED_LIBS=ON
          else
            export BUILD_SHARED_LIBS=OFF
          fi

          ./build-riscv64-linux-gnu.sh

          ls -lh build-riscv64-linux-gnu/bin
          ls -lh build-riscv64-linux-gnu/lib

          file build-riscv64-linux-gnu/bin/sherpa-onnx

      - name: Test sherpa-onnx
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/sysroot
          export LD_LIBRARY_PATH=$GITHUB_WORKSPACE/toolchain/sysroot/lib

          ls -lh ./build-riscv64-linux-gnu/bin

          echo "----------sherpa-onnx----------"
          qemu-riscv64 ./build-riscv64-linux-gnu/bin/sherpa-onnx --help
          readelf -d ./build-riscv64-linux-gnu/bin/sherpa-onnx

          echo "----------sherpa-onnx-offline----------"
          qemu-riscv64 ./build-riscv64-linux-gnu/bin/sherpa-onnx-offline --help
          readelf -d ./build-riscv64-linux-gnu/bin/sherpa-onnx-offline

          echo "----------sherpa-onnx-offline-tts----------"
          qemu-riscv64 ./build-riscv64-linux-gnu/bin/sherpa-onnx-offline-tts --help
          readelf -d ./build-riscv64-linux-gnu/bin/sherpa-onnx-offline-tts

      - name: Test streaming speech recognition
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/sysroot
          export LD_LIBRARY_PATH=$GITHUB_WORKSPACE/toolchain/sysroot/lib

          wget -q https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23.tar.bz2
          tar xvf sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23.tar.bz2
          rm sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23.tar.bz2

          qemu-riscv64 ./build-riscv64-linux-gnu/bin/sherpa-onnx \
            --tokens=./sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23/tokens.txt \
            --encoder=./sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23/encoder-epoch-99-avg-1.onnx \
            --decoder=./sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23/decoder-epoch-99-avg-1.onnx \
            --joiner=./sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23/joiner-epoch-99-avg-1.onnx \
            ./sherpa-onnx-streaming-zipformer-zh-14M-2023-02-23/test_wavs/0.wav

      - name: Test offline tts
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/sysroot
          export LD_LIBRARY_PATH=$GITHUB_WORKSPACE/toolchain/sysroot/lib

          wget -q https://github.com/k2-fsa/sherpa-onnx/releases/download/tts-models/vits-piper-en_US-lessac-medium.tar.bz2
          tar xf vits-piper-en_US-lessac-medium.tar.bz2
          rm vits-piper-en_US-lessac-medium.tar.bz2

          qemu-riscv64 ./build-riscv64-linux-gnu/bin/sherpa-onnx-offline-tts \
            --vits-model=./vits-piper-en_US-lessac-medium/en_US-lessac-medium.onnx \
            --vits-data-dir=./vits-piper-en_US-lessac-medium/espeak-ng-data \
            --vits-tokens=./vits-piper-en_US-lessac-medium/tokens.txt \
            --output-filename=./liliana-piper-en_US-lessac-medium.wav \
            'liliana, the most beautiful and lovely assistant of our team!'

      - name: Copy files
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          riscv64-unknown-linux-gnu-strip --version

          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-riscv64-${{ matrix.lib_type }}
          mkdir $dst

          cp -v $GITHUB_WORKSPACE/toolchain/sysroot/lib/ld-linux-riscv64xthead-lp64d.so.1 build-riscv64-linux-gnu/install/lib/

          ls -lh build-riscv64-linux-gnu/install/lib

          cp -a build-riscv64-linux-gnu/install/bin $dst/
          ls -lh $dst/bin/*
          riscv64-unknown-linux-gnu-strip $dst/bin/*
          ls -lh $dst

          lib_type=${{ matrix.lib_type }}
          if [[ $lib_type == "shared" ]]; then
            cp -a build-riscv64-linux-gnu/install/lib $dst/
            rm -fv $dst/lib/libasound.so
            rm -fv $dst/lib/libonnxruntime.so
            rm -fv $dst/lib/libsherpa-onnx-fst.so
            rm -fv $dst/lib/libsherpa-onnx-fstfar.so
          fi

          tree $dst

          tar cjvf ${dst}.tar.bz2 $dst

      - uses: actions/upload-artifact@v4
        if: matrix.lib_type == 'shared'
        with:
          name: sherpa-onnx-linux-riscv64-shared
          path: sherpa-onnx-*linux-riscv64-shared.tar.bz2

      # https://huggingface.co/docs/hub/spaces-github-actions
      - name: Publish to huggingface
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && (github.event_name == 'push' || github.event_name == 'workflow_dispatch')
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface

            GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-libs huggingface

            cd huggingface
            git lfs pull
            mkdir -p riscv64

            cp -v ../sherpa-onnx-*-shared.tar.bz2 ./riscv64

            git status
            git lfs track "*.bz2"

            git add .

            git commit -m "upload sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-riscv64-shared.tar.bz2"

            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-libs main

      - uses: actions/upload-artifact@v4
        if: matrix.lib_type == 'shared'
        with:
          name: wave
          path: ./*.wav

      - uses: actions/upload-artifact@v4
        if: matrix.lib_type == 'static'
        with:
          name: sherpa-onnx-linux-riscv64-static
          path: sherpa-onnx-*linux-riscv64-static.tar.bz2

      - name: Release pre-compiled binaries and libs for riscv64 linux ${{ matrix.lib_type }}
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*linux-riscv64*.tar.bz2
