import numpy as np 
import json
import time
import cv2

# 生成告警信息，
# 绘制图片
# 封装json

def make_json(post_mess):
    model_name, model_version, algo_name = post_mess
    payload ={
        "modelName": model_name,
        "modelVersion" : model_version,
        "algoName" : algo_name, 
        "dateTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        "values": [],
        "boxes": {
            "person": [],
            "fallperson":[]
        },
        "image": "",
        "rawImage": ""
    }

    # json.dumps(payload)
    return payload

def draw_image_boxes(image, draw_boxes):
    """Draw the boxes on the image.

    # Argument:
        image: original image.
        boxes: ndarray, boxes of objects.
        classes: ndarray, classes of objects.
        scores: ndarray, scores of objects.
        all_classes: all classes name.
        [scale, dw, dh], [1.3333333333333333, 84.0, 0.0]
    """

    for top, left, right, bottom, score, cl in draw_boxes:
        image = cv2.rectangle(image, (top, left), (right, bottom), (255, 255, 0), 2)
        image = cv2.putText(image, '{0} {1:.2f}'.format(cl, score),
                    (top, left + 20),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6, (0, 255, 0), 2)
    return image