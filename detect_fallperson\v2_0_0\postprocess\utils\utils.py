import numpy as np 
import json
import time
import cv2

# 生成告警信息，
# 绘制图片
# 封装json

def make_json(post_mess):
    model_name, model_version, algo_name = post_mess
    payload ={
        "modelName": model_name,
        "modelVersion" : model_version,
        "algoName" : algo_name, 
        "dateTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        "values": [],
        "boxes": {
            "person": [],
            "fallperson":[]
        },
        "image": "",
        "rawImage": ""
    }

    # json.dumps(payload)
    return payload

def draw_image_boxes(image, draw_boxes):
    """Draw the boxes on the image with different colors for different classes.

    # Argument:
        image: original image.
        boxes: ndarray, boxes of objects.
        classes: ndarray, classes of objects.
        scores: ndarray, scores of objects.
        all_classes: all classes name.
        [scale, dw, dh], [1.3333333333333333, 84.0, 0.0]
    """

    # 定义不同类别的颜色
    class_colors = {
        "person": (0, 255, 0),      # 绿色 - 正常人员
        "fallperson": (0, 0, 255),  # 红色 - 跌倒人员
    }

    # 定义文字颜色
    text_colors = {
        "person": (0, 255, 0),      # 绿色文字
        "fallperson": (255, 255, 255),  # 白色文字（红色背景上更清晰）
    }

    for top, left, right, bottom, score, cl in draw_boxes:
        # 根据类别选择颜色
        box_color = class_colors.get(cl, (255, 255, 0))  # 默认黄色
        text_color = text_colors.get(cl, (0, 255, 0))    # 默认绿色

        # 绘制检测框
        image = cv2.rectangle(image, (top, left), (right, bottom), box_color, 2)

        # 绘制类别和置信度文字
        label = '{0} {1:.2f}'.format(cl, score)
        image = cv2.putText(image, label,
                    (top, left + 20),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6, text_color, 2)
    return image