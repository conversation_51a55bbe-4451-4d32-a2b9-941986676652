#!/usr/bin/env python3
"""
测试detect_face_alarm模块导入
"""

import sys
import os
import importlib

def test_face_alarm_import():
    """测试人脸算法导入"""
    print("🔍 测试detect_face_alarm模块导入")
    print("=" * 60)
    
    try:
        # 尝试导入main模块
        print("1. 尝试导入main模块...")
        main_module = importlib.import_module("models.detect_face_alarm.v2_0_0.main")
        print("✅ main模块导入成功")
        
        # 检查load函数
        print("2. 检查load函数...")
        if hasattr(main_module, 'load'):
            print("✅ load函数存在")
            
            # 尝试调用load函数
            print("3. 尝试调用load函数...")
            try:
                result = main_module.load()
                print("✅ load函数调用成功")
                print(f"返回结果类型: {type(result)}")
                print(f"返回结果: {result}")
                
                # 检查返回的算法实例
                if isinstance(result, dict):
                    for key, instance in result.items():
                        print(f"算法实例: {key} -> {type(instance)}")
                        
                        # 检查算法实例的方法
                        required_methods = ['infer', 'start_task', 'stop_task', 'get_task_list']
                        for method in required_methods:
                            if hasattr(instance, method):
                                print(f"  ✅ 方法 {method} 存在")
                            else:
                                print(f"  ❌ 方法 {method} 缺失")
                
                return True
                
            except Exception as e:
                print(f"❌ load函数调用失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("❌ load函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_registry_simulation():
    """模拟registry.py的注册过程"""
    print("\n🔍 模拟registry.py的注册过程")
    print("=" * 60)
    
    try:
        # 模拟registry.py中的代码
        version_modname = "models.detect_face_alarm.v2_0_0"
        print(f"1. 尝试导入: {version_modname}.main")
        
        main_module = importlib.import_module(version_modname + ".main")
        print("✅ 导入成功")
        
        print("2. 调用load函数...")
        model_instances = main_module.load()
        print("✅ load函数调用成功")
        
        print("3. 注册模型实例...")
        for model_key, model_instance in model_instances.items():
            print(f"注册模型: {model_key}")
            print(f"模型类型: {type(model_instance)}")
            
            # 检查必要的方法
            methods = ['infer', 'start_task', 'stop_task', 'get_task_list']
            for method in methods:
                if hasattr(model_instance, method):
                    print(f"  ✅ {method}")
                else:
                    print(f"  ❌ {method}")
        
        print("✅ 模拟注册过程完成")
        return True
        
    except Exception as e:
        print(f"❌ 模拟注册过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖项"""
    print("\n🔍 检查依赖项")
    print("=" * 60)
    
    dependencies = [
        'cv2',
        'numpy', 
        'json',
        'time',
        'os',
        'sys',
        'multiprocessing',
        'importlib'
    ]
    
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep}")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️ 缺失依赖: {missing_deps}")
        return False
    else:
        print("\n✅ 所有依赖都可用")
        return True

def main():
    """主测试函数"""
    print("🚀 测试detect_face_alarm模块")
    print("=" * 70)
    
    # 显示环境信息
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[0]}")
    
    tests = [
        ("依赖项检查", check_dependencies),
        ("模块导入测试", test_face_alarm_import),
        ("注册过程模拟", test_registry_simulation),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！detect_face_alarm模块可以正常使用")
        print("\n📋 现在可以:")
        print("  1. 正常运行 python3 app.py")
        print("  2. 人脸算法应该能够正常注册和工作")
        print("  3. 进行实际的人脸检测测试")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        
        if not results.get("依赖项检查", True):
            print("\n💡 建议: 安装缺失的依赖项")
        if not results.get("模块导入测试", True):
            print("\n💡 建议: 检查模块文件是否完整")
        if not results.get("注册过程模拟", True):
            print("\n💡 建议: 检查算法类的实现")

if __name__ == "__main__":
    main()
