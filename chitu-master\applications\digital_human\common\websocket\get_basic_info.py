import os
import base64
import json
from log import mylog
from applications.view.broadcast.live import get_local_ip
from applications.digital_human.common.common_utils import read_config, SLEEPY_PICS_DIR, PROPAGANDA_PICS_DIR, read_version_info

def get_back_ground():
    msg = {
        "messageType": "backGround",
        "messageBody": "background.jpg"
    }
    return json.dumps(msg)

def get_speaker():
    msg = {
        "messageType": "speaker",
        "messageBody": "speaker.jpg"
    }
    return json.dumps(msg)

def get_interaction():
    conf = read_config()
    msg = {
        "messageType": "interaction",
        "messageBody": {
            "interactionDefault": str(conf["interactionDefault"]),
            "interactionVal": conf["interactionVal"]
        }
    }
    return json.dumps(msg)

def get_screen_dirction():
    conf = read_config()
    msg = {
        "messageType": "screenInfo",
        "messageBody": {
            "screenDirection": conf.get("screenDirection", "0")
        }
    }
    return json.dumps(msg)

import random
from applications.digital_human.models import digital_human_interactive
def get_question_guide(id=None, app=None):
    que_guide_all = []
    if id is not None and id != "":
        with app.app_context():
            obj = digital_human_interactive.query.filter_by(questionId=id).first()
            relations = obj.correlations
            for item in relations:
                if int(item.enabled) == 0:
                    que_guide_all.append(item.question)
    
    if len(que_guide_all) == 0:
        conf = read_config()
        que_guide_all = conf["questionGuide"]
            
    if len(que_guide_all) > 4:
        que_guide = random.sample(que_guide_all, 4)
    else:
        que_guide = que_guide_all
        
    msg = {
        "messageType": "questionGuide",
        "messageBody": que_guide
    }
    return json.dumps(msg)

def get_propaganda_pics():
    images = []
    if os.path.exists(PROPAGANDA_PICS_DIR):
        if len(os.listdir(PROPAGANDA_PICS_DIR)) > 0:
            for img in os.listdir(PROPAGANDA_PICS_DIR):
                images.append("propaganda_images/" + img)
        else:
            images.append("propaganda_default.jpg")
    else:
        images.append("propaganda_default.jpg")
        
    msg = {
        "messageType": "propaganda",
        "messageBody": {
            "pics": images
        }
    }
    return json.dumps(msg)

def get_sleepy_pics():
    images = []
    if os.path.exists(SLEEPY_PICS_DIR):
        if len(os.listdir(SLEEPY_PICS_DIR)) > 0:
            for img in os.listdir(SLEEPY_PICS_DIR):
                images.append("sleepy_images/" + img)
        else:
            images.append("sleepy_default.png")
    else:
        images.append("sleepy_default.png")
        
    msg = {
        "messageType": "sleep",
        "messageBody": {
            "pics": images
        }
    }
    return json.dumps(msg)