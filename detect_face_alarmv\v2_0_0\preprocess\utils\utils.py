import cv2
import base64
import numpy as np

def image_base64(image):
    """
    将图像转换为base64编码
    Args:
        image: OpenCV图像
    Returns:
        base64_str: base64编码字符串
    """
    try:
        if image is None:
            return ""
        
        # 编码为JPEG格式
        _, buffer = cv2.imencode('.jpg', image)
        
        # 转换为base64
        base64_str = base64.b64encode(buffer).decode('utf-8')
        
        return base64_str
        
    except Exception as e:
        print(f"图像base64编码失败: {e}")
        return ""

def image_byte(image):
    """
    将图像转换为字节数据
    Args:
        image: OpenCV图像
    Returns:
        bytes_data: 字节数据
    """
    try:
        if image is None:
            return b""
        
        # 编码为JPEG格式
        _, buffer = cv2.imencode('.jpg', image)
        
        return buffer.tobytes()
        
    except Exception as e:
        print(f"图像字节编码失败: {e}")
        return b""
