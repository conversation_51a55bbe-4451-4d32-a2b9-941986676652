# Modified from https://github.com/Tencent/ncnn/blob/master/.github/workflows/linux-arm-cpu-gcc.yml
name: aarch64-linux-gnu-static

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/aarch64-linux-gnu-static.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
      - 'toolchains/aarch64-linux-gnu.toolchain.cmake'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/aarch64-linux-gnu-static.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
      - 'toolchains/aarch64-linux-gnu.toolchain.cmake'

  workflow_dispatch:

concurrency:
  group: aarch64-linux-gnu-static-${{ github.ref }}
  cancel-in-progress: true

jobs:
  aarch64_linux_gnu_static:
    runs-on: ${{ matrix.os }}
    name: aarch64 static lib test
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-aarch64-static

      - name: cache-qemu
        id: cache-qemu
        uses: actions/cache@v4
        with:
          path: qemu-install
          key: qemu-aarch64-install-20220908

      - name: install-qemu-build-deps
        if: steps.cache-qemu.outputs.cache-hit != 'true'
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake autotools-dev ninja-build

      - name: checkout-qemu
        if: steps.cache-qemu.outputs.cache-hit != 'true'
        uses: actions/checkout@v3
        with:
          repository: qemu/qemu
          path: qemu
          ref: f5643914a9e8f79c606a76e6a9d7ea82a3fc3e65

      - name: qemu
        if: steps.cache-qemu.outputs.cache-hit != 'true'
        run: |
          cd qemu
          ./configure --prefix=$GITHUB_WORKSPACE/qemu-install --target-list=aarch64-linux-user --disable-system
          make -j2
          make install

      - name: cache-toolchain
        id: cache-toolchain
        uses: actions/cache@v4
        with:
          path: toolchain
          key: gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu.tar.xz

      - name: Download toolchain
        if: steps.cache-toolchain.outputs.cache-hit != 'true'
        shell: bash
        run: |
          wget -qq https://huggingface.co/csukuangfj/sherpa-ncnn-toolchains/resolve/main/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu.tar.xz

          mkdir $GITHUB_WORKSPACE/toolchain
          tar xf ./gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu.tar.xz --strip-components 1 -C $GITHUB_WORKSPACE/toolchain

      - name: Display toolchain info
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          aarch64-none-linux-gnu-gcc --version

      - name: Display qemu-aarch64 -h
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/aarch64-none-linux-gnu/libc
          qemu-aarch64 -h

      - name: build aarch64-linux-gnu
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH

          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"

          cmake --version

          export BUILD_SHARED_LIBS=OFF

          ./build-aarch64-linux-gnu.sh

          ls -lh build-aarch64-linux-gnu/bin
          ls -lh build-aarch64-linux-gnu/lib

          file build-aarch64-linux-gnu/bin/sherpa-onnx

      - name: Test sherpa-onnx
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          export PATH=$GITHUB_WORKSPACE/qemu-install/bin:$PATH
          export QEMU_LD_PREFIX=$GITHUB_WORKSPACE/toolchain/aarch64-none-linux-gnu/libc

          ls -lh ./build-aarch64-linux-gnu/bin

          qemu-aarch64 ./build-aarch64-linux-gnu/bin/sherpa-onnx --help

          readelf -d ./build-aarch64-linux-gnu/bin/sherpa-onnx

      - name: Copy files
        shell: bash
        run: |
          export PATH=$GITHUB_WORKSPACE/toolchain/bin:$PATH
          aarch64-none-linux-gnu-strip --version

          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-aarch64-static
          mkdir $dst

          ls -lh build-aarch64-linux-gnu/install/lib

          cp -a build-aarch64-linux-gnu/install/bin $dst/
          ls -lh $dst/bin/
          echo "strip"
          aarch64-none-linux-gnu-strip $dst/bin/*
          ls -lh $dst/bin/

          tree $dst

          tar cjvf ${dst}.tar.bz2 $dst

      - uses: actions/upload-artifact@v4
        with:
          name: sherpa-onnx-linux-aarch64-static
          path: sherpa-onnx-*linux-aarch64-static.tar.bz2

      # https://huggingface.co/docs/hub/spaces-github-actions
      - name: Publish to huggingface
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && (github.event_name == 'push' || github.event_name == 'workflow_dispatch')
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-libs huggingface

            cd huggingface
            git lfs pull
            mkdir -p aarch64

            cp -v ../sherpa-onnx-*-static.tar.bz2 ./aarch64

            git status
            git lfs track "*.bz2"

            git add .

            git commit -m "upload sherpa-onnx-${SHERPA_ONNX_VERSION}-linux-aarch64-static.tar.bz2"

            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-libs main

      - name: Release pre-compiled binaries and libs for aarch64 linux
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*linux-aarch64*.tar.bz2
