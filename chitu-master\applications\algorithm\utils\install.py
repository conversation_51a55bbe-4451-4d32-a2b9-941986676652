import sys
import os
import zipfile
import tarfile
import shutil
import json
from applications.extensions import db

sys.path.append(os.path.dirname(__file__))

from applications.algorithm.utils.algo_utils import support_model_type, download_path, models_path, re_read_weights, models, weights
from applications.models import Algorithm, Weights
from applications.algorithm.utils.load_model import loadmodel

support_type = ["zip", "tar"]

'''
获取数据库中已经存在的所有算法的id
'''
def get_exist_ability():
    exist_ability = []
    data = Algorithm.query.all()
    for item in data:
        exist_ability.append(item.id)
    return exist_ability

'''
安装算法时，检查用户的config.json书写是否正确以及对应的权重、推理文件是否存在。
'''
def check_ability_config(weight, dezip_file_path):
    for w in weight:
        try:
            weight = w['weight']
            weight_type = w['weight_type']
            infer_file = w['infer_file']
            ability = w['ability']
        except:
            return {"msg": "缺少必要参数，请联系售后处理"}

        if len(weight):
            
            if weight_type not in support_model_type:
                return {"msg": "该智能盒不支持此类模型，请联系售后处理"}
            
            if not os.path.exists(os.path.join(dezip_file_path, infer_file)):
                return {"msg": "缺少推理代码，请联系售后处理"}
            
            for tmp in weight:
                if not os.path.exists(os.path.join(dezip_file_path, tmp)):
                    return {"msg": "缺少权重文件，请联系售后处理"}
        else:

            if not os.path.exists(os.path.join(dezip_file_path, infer_file)):
                return {"msg": "缺少推理代码，请联系售后处理"}
        
        if not len(ability):
            return {"msg": "ability不可以为空，请联系售后处理"}
        
        for ab in ability:
            try:
                ab["alarmType"]
            except:
                return {"msg": "alarmType不可缺少，请联系售后处理"}
                  
    return {"msg": "success"}

def get_all_aux_dir_name():
    names = []
    if len(os.listdir(models_path)):
        for dir in os.listdir(models_path):
            if len(os.listdir(os.path.join(models_path, dir))):
                for d in os.listdir(os.path.join(models_path, dir)):
                    if (d != "weight" or d != "weights") and os.path.isdir(os.path.join(models_path, dir,d)):
                        names.append(d)
    return names 


def install(filename):
    
    zip_file_path = os.path.join(download_path, filename)
    dezip_file_path = os.path.join(download_path, filename.split('.')[0])
      
    if not os.path.exists(dezip_file_path):
        os.makedirs(dezip_file_path)
        
    if filename.split('.')[-1] == "zip":
        try:
            f_zip = zipfile.ZipFile(zip_file_path)
            f_zip.extractall(dezip_file_path)
            f_zip.close()
        except:
            shutil.rmtree(dezip_file_path)
            return {"msg": "解压失败，请重试"}
    else:
        try:
            f_targz = tarfile.open(zip_file_path)
            f_targz.extractall(dezip_file_path)
            f_targz.close()
        except:
            shutil.rmtree(dezip_file_path)
            return {"msg": "解压失败，请重试"}
    
    if len(os.listdir(dezip_file_path)) == 1 and os.path.isdir(os.path.join(dezip_file_path, os.listdir(dezip_file_path)[0])):
        old_dezip_path = dezip_file_path
        dezip_file_path = os.path.join(dezip_file_path, os.listdir(dezip_file_path)[0])
    
    if not os.path.exists(os.path.join(dezip_file_path, "config.json")):
        shutil.rmtree(old_dezip_path)
        return {"msg": "缺少配置文件，请联系售后处理"}
    
    else:
        with open(os.path.join(dezip_file_path, "config.json"), "r", encoding="utf-8") as conf_f:
            weight_conf = json.load(conf_f)

        if len(weight_conf.keys()) > 1:
            shutil.rmtree(old_dezip_path)
            return {"msg": "请按照模板架构组织文件，请联系售后处理"}

        folder = list(weight_conf.keys())[0]
        
        if os.path.exists(os.path.join(models_path, folder)):
            shutil.rmtree(old_dezip_path)
            return {"msg": "文件已安装，请勿重复安装"}
        else:
            os.makedirs(os.path.join(models_path, folder))

        weight_info = weight_conf[folder]
        
        # 检查文件内容是否正确
        res = check_ability_config(weight_info, dezip_file_path)
        if res["msg"] != "success":
            shutil.rmtree(old_dezip_path)
            shutil.rmtree(os.path.join(models_path, folder))
            return res
        
        exists_dir_name = get_all_aux_dir_name()
        for tmp in os.listdir(dezip_file_path):
            if tmp != "weight" and tmp != "weights" and tmp != "__pycache__" and os.path.isdir(os.path.join(dezip_file_path, tmp)) and tmp in exists_dir_name:
                shutil.rmtree(old_dezip_path)
                shutil.rmtree(os.path.join(models_path, folder))
                return {"msg": "文件夹命名可能产生冲突，请联系售后处理"}    
        
        for tmp in os.listdir(dezip_file_path):
            shutil.move(os.path.join(dezip_file_path, tmp), os.path.join(models_path, folder))
        
        exist_ability = get_exist_ability()
        old_weight_ids = []
        
        for tmp in weight_info:
            weight = tmp['weight']
            weight_type = tmp['weight_type']
            infer_file = tmp['infer_file']
            ability = tmp['ability']

            weight_info = Weights(
                type = weight_type,
                path = os.path.join(models_path, folder),
                name = str(weight),
                infer_file = infer_file
            )
            try:
                db.session.add(weight_info)
                db.session.flush()
                weight_id = weight_info.id
                db.session.commit()
            except:
                shutil.rmtree(old_dezip_path)
                shutil.rmtree(os.path.join(models_path, folder))
                return {"msg": "入库失败，请联系售后处理"}
            
            new_weight_need_open = False
            for ab in ability:
                aid = ab['alarmType']
                name = ""
                description = ""
                try:    
                    name = ab['name']
                    description = ab['description']
                except:
                    pass
                    
                if aid in exist_ability:
                    new_info = {
                        "name": name,
                        "description": description,
                        "weight": weight_id
                    }
                    
                    old_info = Algorithm.query.filter_by(id=aid).first()
                    if old_info.weight not in old_weight_ids:
                        old_weight_ids.append(old_info.weight)
                    if old_info.status:
                        new_weight_need_open = True
                        
                    try:
                        Algorithm.query.filter_by(id=aid).update(new_info)
                        db.session.commit()
                    except:
                        shutil.rmtree(old_dezip_path)
                        shutil.rmtree(os.path.join(models_path, folder))
                        return {"msg": "入库失败，请联系售后处理"}
                    
                else:
                    algo = Algorithm(
                        id = aid,
                        name = name,
                        description = description,
                        weight = weight_id
                    )
                    try:
                        db.session.add(algo)
                        db.session.commit()
                    except:
                        shutil.rmtree(old_dezip_path)
                        shutil.rmtree(os.path.join(models_path, folder))
                        return {"msg": "入库失败，请联系售后处理"}
            
            if new_weight_need_open:
                if len(weight):
                    new_tmp_model = []
                    for n_w in weight:
                        new_weight_path = os.path.join(models_path, folder, n_w)
                        new_res = loadmodel(new_weight_path, weight_type)
                        if new_res["msg"] == "success":
                            new_tmp_model.append(new_res["model"])
                        else:
                            shutil.rmtree(dezip_file_path)
                            shutil.rmtree(os.path.join(models_path, folder))
                            return new_res
                    models[weight_id] = new_tmp_model
                    Weights.query.filter_by(id=weight_id).update({"status": True})
                    db.session.commit()
        
        # 删除无用的文件夹
        if len(old_weight_ids):
            for tid in old_weight_ids:  
                if not len(Algorithm.query.filter_by(weight=tid).all()):
                    if tid in list(models.keys()):
                        del models[tid]
                        
                    weight_path = Weights.query.filter_by(id=tid).first().path
                    Weights.query.filter_by(id=tid).delete()
                    db.session.commit()
                    
                    if not len(Weights.query.filter_by(path=weight_path).all()):
                        shutil.rmtree(weight_path)
                    
        sys.path.append(os.path.join(models_path, folder))
        re_read_weights()
        shutil.rmtree(download_path)
        
        return {'msg': "success"}