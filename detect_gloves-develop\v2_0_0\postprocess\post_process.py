from .utils.boxes_iou import polygon_IOU
from .utils.utils import make_json, draw_image_boxes
from ..preprocess.utils.utils import image_base64, image_byte
import cv2
import numpy as np
from ..utils.log import mylog


class PostProcess(object):
    def __init__(self, i18n, post_mess):
        self.post_mess = post_mess
        self.i18n = i18n
        self.iou_threshold = 0.75  # 提高到75%，要求更精确的区域匹配
        mylog.info("加载算法后处理模块")
        mylog.info(f"[PLAY_PHONE_POST] IOU阈值设置为: {self.iou_threshold}")
    

    def disassemble_recv_data_type(self, data):
        """输出类型，改变输出数量，默认都开启"""
        recv_data_type = data["businessData"]["advsetValue"]["recvDataType"]
        message_bool = False
        source_img_bool = False
        draw_img_bool = False
        boxes_back_bool = False
        if "infer_results" in recv_data_type:
            message_bool = True
        if "draw_image" in recv_data_type:
            draw_img_bool = True
        if "source_image" in recv_data_type:
            source_img_bool = True
        if "infer_boxes" in recv_data_type:
            boxes_back_bool = True
        return message_bool, source_img_bool, draw_img_bool, boxes_back_bool


    def disassemble_algo_type(self, data, image, in_region_boxes):
        """类别判断，根据类别，给定输出，叠加输出要求"""

        # 正常这里好像是hand_in_region_box, glove_in_......你先打印看一下吧，我没看你上一个函数
        mylog.info(f"in region boxes : {in_region_boxes}")
        post_http_bool = False
        json_dict = make_json(self.post_mess)
        message_bool, source_img_bool, draw_img_bool, boxes_back_bool = self.disassemble_recv_data_type(data)
        
        if source_img_bool:
            if "image" in data["businessData"]:
                json_dict["rawImage"] = data["businessData"]["image"]
            else:
                if data["businessData"]["imageType"] == "base64":
                    json_dict["rawImage"] = image_base64(image)
                if data["businessData"]["imageType"] == "byte":
                    json_dict["rawImage"] = image_byte(image)
            
        gloves_boxes, hand_boxes = [], []
        algo_type = data["businessData"]["advsetValue"].get("gloves", "gloves")
        if algo_type == "all":
            if len(in_region_boxes) != 0:
                post_http_bool = True
                if message_bool:
                    json_dict["values"].append({"type": "gloves", "value": self.i18n.get("gloves")})
                    json_dict["values"].append({"type": "hand", "value": self.i18n.get("hand")})

            for i in in_region_boxes:
                ix1, iy1, ix2, iy2, iscore, icl = i
                if icl == "gloves":
                    gloves_boxes.append([ix1, iy1, ix2, iy2, iscore, "gloves"])
                else:
                    hand_boxes.append([ix1, iy1, ix2, iy2, iscore, "hand"])
            if boxes_back_bool:
                json_dict["boxes"]["gloves"] = gloves_boxes
                json_dict["boxes"]["hand"] = hand_boxes
        
        if algo_type == "gloves":
            for i in in_region_boxes:
                ix1, iy1, ix2, iy2, iscore, icl = i
                if icl == "gloves":
                    gloves_boxes.append([ix1, iy1, ix2, iy2, iscore, "gloves"])  
                              
            if len(gloves_boxes) != 0:
                post_http_bool = True
                if message_bool:
                    json_dict["values"].append({"type": "gloves", "value": self.i18n.get("gloves")})
                    
            if boxes_back_bool:
                json_dict["boxes"]["gloves"] = gloves_boxes
        
        if algo_type == "hand":
            for i in in_region_boxes:
                ix1, iy1, ix2, iy2, iscore, icl = i
                if icl == "hand":
                    hand_boxes.append([ix1, iy1, ix2, iy2, iscore, "hand"])
                    
            if len(hand_boxes) != 0:
                post_http_bool = True
                if message_bool:
                    json_dict["values"].append({"type": "hand", "value": self.i18n.get("hand")})
                                
            if boxes_back_bool:
                json_dict["boxes"]["hand"] = hand_boxes
        
        if draw_img_bool:
            corrent_boxes = gloves_boxes + hand_boxes
            # mylog.info(f"corrent_boxes : {corrent_boxes}")
            image = draw_image_boxes(image, corrent_boxes)
            # cv2.imwrite("1.png", image)
            if data["businessData"]["imageType"] == "base64":
                json_dict["image"] = image_base64(image)
            if data["businessData"]["imageType"] == "byte":
                json_dict["image"] = image_byte(image)
        # mylog.info(f"post http bool : {post_http_bool}")        
        return json_dict, post_http_bool


    def disassemble_advset_value(self, data, image, boxes):
        in_region_boxes = []
        h, w, _ = image.shape
        business_data = data["businessData"]
        flag = True
        if "area" in business_data["advsetValue"]:
            if "areaType" in business_data["advsetValue"]["area"]:
                area_type = business_data["advsetValue"]["area"]["areaType"]
                if area_type == "POLYGON" and "positions" in business_data["advsetValue"]["area"]:
                    positions = business_data["advsetValue"]["area"]["positions"]
                    if len(positions) != 0:
                        flag = False
                        box_in_index = []
                        for region in positions:
                            if len(region) >= 3:
                                coordinate = []
                                for i in region:
                                    coordinate.append(np.array([i[0]*w, i[1]*h]))
                                box_1 = np.array(coordinate)
                               
                                polygon_1 = np.floor(box_1).astype(np.int32)
                                # 绘制多边形
                                image = cv2.polylines(image, [polygon_1], True, (0, 0, 250), thickness=2)
                                for i in range(len(boxes)):                            
                                    if len(boxes[i]) != 0:
                                        polygon_2 = np.array([[boxes[i][0], boxes[i][1]], [boxes[i][0], boxes[i][3]], [boxes[i][2], boxes[i][3]], [boxes[i][2], boxes[i][1]]])
                                        iou = polygon_IOU(polygon_1, polygon_2)
                                        if iou > self.iou_threshold:
                                            box_in_index.append(i)
                                            
                        boxes_in_index = list(set(box_in_index))
                        for j in boxes_in_index:
                            in_region_boxes.append(boxes[j])
                    else:
                        for b in boxes:
                            if len(b) != 0:
                                in_region_boxes.append(b)
        else:
            # 没有划定区域，默认区域不存在
            if flag:
                for b in boxes:
                    if len(b) != 0:
                        in_region_boxes.append(b)
        return in_region_boxes, image

    def run_process(self, data, boxes, image):
        in_region_boxes, image = self.disassemble_advset_value(data, image, boxes)

        # 添加检测结果统计日志
        # mylog.info(f"[detect gloves] 原始检测框数量: {len(boxes)}")
        # mylog.info(f"[detect gloves] 过滤后检测框数量: {len(in_region_boxes)}")
        # if len(in_region_boxes) > 0:
        #     for i, box in enumerate(in_region_boxes):
        #         if len(box) >= 5:  # 确保有置信度信息
        #             mylog.info(f"[detect gloves] 检测框{i+1}: 置信度={box[4]:.3f}")

        back_json, post_bool = self.disassemble_algo_type(data, image, in_region_boxes)
        # mylog.info(f"[PLAY_PHONE_DETECT] 是否触发报警: {post_bool}")

        return back_json, post_bool
