import onnxruntime as ort
from utils.log import mylog
from utils.nn_error import NNError


try:
    
    import torch 
    
except Exception as e:
    
     mylog.error("gpu不存在")




class InferenceOnnx(object):
    
    def __init__(self):
        
        self.session_options = ort.SessionOptions()  
        try:
            if torch.cuda.is_available():
                
                self.session_options.use_cuda = True
            else:
                
                self.session_options.use_cuda = False
                
        except Exception as e:
            
            self.session_options.use_cuda = False
        
        self.session = None    
        self.height = 640
        self.width = 640
        self.input_name = None
        self.output_name = None
        
    def load(self, model_path, height, width):
        try:
            
            self.session = ort.InferenceSession(model_path, self.session_options) 
            self.height = height
            self.width = width
            self.input_name = self.session.get_inputs()[0].name
            self.output_name = self.session.get_outputs()[0].name
            
            mylog.info("model load success")
            return NNError.NN_SUCCESS
        except Exception as e:
            
            mylog.error("onnx model load fail")
            return NNError.NN_LOAD_MODEL_FAIL
        
        
    def predict(self, image):

        try:
            input_data = {self.input_name : image}
            result = self.session.run([self.output_name], input_data)

            return NNError.NN_SUCCESS, result
        except Exception as e:
            
            mylog.error("onnx inference error")
            return NNError.NN_MODEL_INFERENCE_FAIL, None










