from tools.extract_table_info import (get_sqlite_table_info,
                                      expand_lists,
                                      expand_question_lists)
#参数定义
class VarsUpdate:
    def __init__(self,model_ort,tokenizer):
        self.model_ort = model_ort
        self.tokenizer = tokenizer
        self.sentences_q = []
        self.sentences_a = []
        self.qa_video_url = []
        self.qa_video_duration = []
        self.fileTypeList = []
        self.fileUrlList = []
        self.model_output_1 = []
        self.sentence_embeddings_1 = []

    #更新数据库配置    
    def update_sentences_embedding(self,args):
        self.sentences_q = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['question'].tolist()
        self.similarQuestionList = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['similarQuestionList'].tolist()
        self.fileTypeList = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['fileType'].tolist()
        self.fileUrlList = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['fileUrl'].tolist()
        self.sentences_q = expand_question_lists(self.similarQuestionList,self.sentences_q)
        self.fileTypeList = expand_lists(self.similarQuestionList,self.fileTypeList)
        self.fileTypeList.append("")
        self.fileUrlList = expand_lists(self.similarQuestionList,self.fileUrlList)
        self.fileUrlList.append("")
        self.wakeup_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['awaken_greeting'][0]
        self.sentences_q.append(self.wakeup_words)
        self.sentences_a = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['answer'].tolist()
        self.sentences_a = expand_lists(self.similarQuestionList,self.sentences_a)
        self.qa_video_url = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['interactive_video'].tolist()
        self.qa_video_url = expand_lists(self.similarQuestionList,self.qa_video_url)
        self.qa_ids = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['questionId'].tolist()
        self.qa_ids = expand_lists(self.similarQuestionList,self.qa_ids)
        self.qa_video_duration = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['videoDuration'].tolist()
        self.qa_video_duration = expand_lists(self.similarQuestionList,self.qa_video_duration)
        self.welcome_greeting = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_greeting'][0]
        self.welcome_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_video_url'][0]
        self.welcomeVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcomeVideoDuration'][0]
        self.sentences_a.append(self.welcome_greeting)
        self.qa_video_url.append(self.welcome_video_url)
        self.qa_ids.append("")
        self.qa_video_duration.append(self.welcomeVideoDuration)
        self.encoded_input_1 = self.tokenizer(self.sentences_q, padding=True, truncation=True, return_tensors='pt')
        self.model_output_1 = self.model_ort(self.encoded_input_1["input_ids"],self.encoded_input_1["token_type_ids"],self.encoded_input_1["attention_mask"])
        self.sentence_embeddings_1 = self.model_output_1[0][:, 0]   
      
