#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Name: qa.py

Description: This is an Real-time qa system Python script.
Real-time speech recognition from a microphone with sherpa-onnx Python API
with endpoint detection.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

#shepa-onnx 
import argparse
import sys
from pathlib import Path
import numpy as np
import soundfile as sf
import multiprocessing
from concurrent.futures import ThreadPoolExecutor
from scipy.io import wavfile

#bge
from optimum.onnxruntime import ORTModelForFeatureExtraction
from transformers import AutoTokenizer, AutoModel
from tools.websocket_client import WebSocketClient
from tools.extract_table_info import get_sqlite_table_info
from src.qa_model import create_recognizer
from tools.message_struct import (answer_push_json_message_struct,
                                  question_push_json_message_struct,
                                  command_word_push_json_message_struct)  
import torch
import time
import pandas as pd
import sqlite3
import asyncio
import ast


try:
    import sounddevice as sd
except ImportError:
    print("Please install sounddevice first. You can use")
    print()
    print("  pip install sounddevice")
    print()
    print("to install it")
    sys.exit(-1)

import sherpa_onnx
import json


def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--tokens",
        type=str,
        required=True,
        help="Path to tokens.txt",
    )

    parser.add_argument(
        "--encoder",
        type=str,
        required=True,
        help="Path to the encoder model",
    )

    parser.add_argument(
        "--decoder",
        type=str,
        required=True,
        help="Path to the decoder model",
    )

    parser.add_argument(
        "--joiner",
        type=str,
        required=True,
        help="Path to the joiner model",
    )

    parser.add_argument(
        "--decoding-method",
        type=str,
        default="greedy_search",
        help="Valid values are greedy_search and modified_beam_search",
    )

    parser.add_argument(
        "--provider",
        type=str,
        default="cpu",
        help="Valid values: cpu, cuda, coreml",
    )
    
    parser.add_argument(
        "--db_path",
        type=str,
        #required=True,
        default="/data/zhichao/chitu/pear.db",
        help="Path to the pear.db",
    )   

    parser.add_argument(
        "--basic_setting_table_name",
        type=str,
        #required=True,
        default="admin_digital_human_basic_setting",
        help="This table contains some basic settings for digital humans.",
    )

    parser.add_argument(
        "--qa_pairs_table_name",
        type=str,
        #required=True,
        default="admin_digital_human_interactive",
        help="This table contains some QA pairs for digital humans.",
    )

    parser.add_argument(
        "--hotwords-file",
        type=str,
        default="",
        help="""
        The file containing hotwords, one words/phrases per line, and for each
        phrase the bpe/cjkchar are separated by a space. For example:

        ▁HE LL O ▁WORLD
        你 好 世 界
        """,
    )
    
    parser.add_argument(
        "--hotwords-score",
        type=float,
        default=1.5,
        help="""
        The hotword score of each token for biasing word/phrase. Used only if
        --hotwords-file is given.
        """,
    )

    parser.add_argument(
        "--blank-penalty",
        type=float,
        default=0.0,
        help="""
        The penalty applied on blank symbol during decoding.
        Note: It is a positive value that would be applied to logits like
        this `logits[:, 0] -= blank_penalty` (suppose logits.shape is
        [batch_size, vocab] and blank id is 0).
        """,
    )

    return parser.parse_args()


#唤醒后问答交互
async def rec_pipeline(s, recognizer, model_ort, tokenizer, model_output_1, sentences_1, client, args):
    #创建识别音频输入流
    sample_rate = 44100
    samples_per_read = int(0.1 * sample_rate)  # 0.1 second = 100 ms
    stream = recognizer.create_stream()

    from scipy.io import wavfile
    #从数据库中读取问答对、兜底话术、敏感词、命令词配置
    sentences_a = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['answer'].tolist()
    qa_video_url = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['interactive_video'].tolist()
    qa_video_duration = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['videoDuration'].tolist()
    print(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0],type(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0]))
    command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
    sensitive_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words'].tolist()
    sensitive_words_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words_video_url'].tolist()[0]
    sensitive_words_reply = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words_reply'].tolist()[0]
    sensitiveWordsVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitiveWordsVideoDuration'][0]
    bottom_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_video_url'][0]
    bottom_line_reply = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_line_reply'][0]
    bottomVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottomVideoDuration'][0]

    last_result = ""
    segment_id = 0
    sentence_embeddings_1 = model_output_1[0][:, 0]
    rec_time = time.time()
    init_array = np.array([])

    #多轮对话循环
    while True:
        #判断前端播放视频结束后开始拾音识别
        if client.get_status() == "1":
            while True:
                samples, _ = s.read(samples_per_read)  # a blocking read
                samples = samples[:,0]
                samples = samples.reshape(-1)
                stream.accept_waveform(sample_rate, samples)
                while recognizer.is_ready(stream):
                    recognizer.decode_stream(stream)
                is_endpoint = recognizer.is_endpoint(stream)
                result = recognizer.get_result(stream)
                init_array =  np.concatenate((init_array,samples))
                if result and (last_result != result):
                    last_result = result
                    print("\r{}:{}".format(segment_id, result), end="", flush=True)
                if is_endpoint:
                    if result:
                        found = False
                        #敏感词回复
                        for word in sensitive_words:
                            if word in result:
                                found = True
                                await client.send_message(question_push_json_message_struct(result.replace(sensitive_word,"*"*len(word))))
                                await client.send_message(answer_push_json_message_struct(sensitive_words_video_url,sensitive_words_reply, sensitiveWordsVideoDuration))
                                break

                        if found:
                            pass
                        else:
                            #命令词回复
                            for messageType,word in command_words_dict.items():
                                if word in result:
                                    found = True
                                    await client.send_message(command_word_push_json_message_struct(messageType,result,word))
                                    break
                                    #client.change_status()
                       
                        if found:
                            pass
                        else:
                            #用户问答对相似度匹配
                            sentences_2 = [result]
                            rec_time = time.time()
                            encoded_input_2 = tokenizer(sentences_2, padding=False, truncation=True, return_tensors='pt')
                            model_output_2 = model_ort(encoded_input_2["input_ids"],encoded_input_2["token_type_ids"],encoded_input_2["attention_mask"])
                            # Perform pooling. In this case, cls pooling.
                            sentence_embeddings_2 = model_output_2[0][:, 0]
                            # normalize embeddings
                            #sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
                            scores = sentence_embeddings_1 @ sentence_embeddings_2.T
                            max_value = max(scores.view(-1).tolist())

                            if max_value > 30:
                                max_index = max(enumerate(scores), key=lambda x: x[1][0])[0]
                                await client.send_message(question_push_json_message_struct(result))
                                await client.send_message(answer_push_json_message_struct(qa_video_url[max_index],result,qa_video_duration[max_index]))
                                client.change_status()
                            else:
                                #兜底话术回复
                                await client.send_message(question_push_json_message_struct(result))
                                await client.send_message(answer_push_json_message_struct(bottom_video_url, bottom_line_reply, bottomVideoDuration))
                                client.change_status()

                        print("\r{}:{}".format(segment_id, result), flush=True)
                        wav_file = f'./wav/{segment_id}_{result}_{time.time()}.wav'
                        wavfile.write(wav_file, 44100, init_array)
                        segment_id += 1
                        #init_array = np.array([])
                        recognizer.reset(stream)

                    if time.time() - rec_time > 180:    
                        print("rec end...")
                        return
                    break
        else:

            if time.time() - rec_time > 180:
                print("rec end...")
                return
            else:
                continue

#问答主进程            
async def main():
    args = get_args()
    devices = sd.query_devices()
    if len(devices) == 0:
        print("No microphone devices found")
        sys.exit(0)
    print(devices)
    sd.default.device[0] = 0
    default_input_device_idx = sd.default.device[0]
    print(f'Use default device: {devices[default_input_device_idx]["name"]}')
    recognizer,model_ort,tokenizer = create_recognizer(args)
    #与赤兔建立WebSocket连接
    client = WebSocketClient("ws://192.168.3.200:8765")
    client.connect()

    #配置数字人问题、欢迎语
    sentences_q = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['question'].tolist()
    wakeup_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['awaken_greeting'][0]
    welcome_greeting = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_greeting'][0]
    welcome_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_video_url'][0]
    welcomeVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcomeVideoDuration'][0]

    #初始化问题列表的sentences的embedding
    encoded_input_1 = tokenizer(sentences_q, padding=True, truncation=True, return_tensors='pt')
    model_output_1 = model_ort(encoded_input_1["input_ids"],encoded_input_1["token_type_ids"],encoded_input_1["attention_mask"])
    sentence_embeddings_1 = model_output_1[0][:, 0]

    print("Started! Please speak")

    # The model is using 16 kHz, we use 48 kHz here to demonstrate that
    # sherpa-onnx will do resampling inside.

    #创建识别音频输入流格式
    sample_rate = 44100
    samples_per_read = int(0.1 * sample_rate)  # 0.1 second = 100 ms
    stream = recognizer.create_stream()
    last_result = ""
    segment_id = 0
    init_array = np.array([])
   
    with sd.InputStream(channels=2, dtype="float32", samplerate=sample_rate) as s:
        #开启唤醒循环
        while True:
            #提取单声道
            samples, _ = s.read(samples_per_read)
            samples = samples[:,0]
            samples = samples.reshape(-1)
            stream.accept_waveform(sample_rate, samples)
            while recognizer.is_ready(stream):
                recognizer.decode_stream(stream)
            is_endpoint = recognizer.is_endpoint(stream)
            result = recognizer.get_result(stream)
            print(result,is_endpoint)
            init_array =  np.concatenate((init_array,samples))  
            if result and (last_result != result):
                last_result = result
                print("\r{}:{}".format(segment_id, result), end="", flush=True)
            if is_endpoint:
                if result:
                    print("\r{}:{}".format(segment_id, result), flush=True)
                    #唤醒回复
                    if wakeup_words in result:
                        print("wake up end.")
                        print("rec start ...")
                        await client.send_message(question_push_json_message_struct(result))
                        await client.send_message(answer_push_json_message_struct(welcome_video_url,welcome_greeting,welcomeVideoDuration))
                        client.change_status()
                        await rec_pipeline(s, recognizer, model_ort, tokenizer, model_output_1, sentences_q, client, args)
                    """    
                    for messageType,word in command_words_dict.items():
                        if word in result:
                            await client.send_message(command_word_push_json_message_struct(messageType,result,word))
                            break
                    """
                    #保存录制音频仅调试用  
                    wav_file = f'./wav/{segment_id}_{result}_{time.time()}.wav'
                    wavfile.write(wav_file, 44100, init_array)  
                    segment_id += 1 
                init_array = np.array([])  
                recognizer.reset(stream)


if __name__=="__main__":
    asyncio.run(main())
