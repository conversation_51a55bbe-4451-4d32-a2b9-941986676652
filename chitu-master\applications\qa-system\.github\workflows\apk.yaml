name: apk

on:
  push:
    tags:
      - '*'

  workflow_dispatch:

concurrency:
  group: apk-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  apk:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # https://github.com/actions/setup-java
      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin' # See 'Supported distributions' for available options
          java-version: '21'

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-android

      - name: Display NDK HOME
        shell: bash
        run: |
          echo "ANDROID_NDK_LATEST_HOME: ${ANDROID_NDK_LATEST_HOME}"
          ls -lh ${ANDROID_NDK_LATEST_HOME}

      - name: build APK
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          export ANDROID_NDK=$ANDROID_NDK_LATEST_HOME
          ./build-apk-vad.sh
          ./build-apk-two-pass.sh
          ./build-apk.sh

      - name: Display APK
        shell: bash
        run: |
          ls -lh ./apks/

      - uses: actions/upload-artifact@v4
        with:
          path: ./apks/*.apk

      - name: Release APK
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          file: apks/*.apk
          overwrite: true
