import os
import cv2
import json
import time
import pyudev
import queue
from datetime import datetime, timedelta
from collections import deque
from threading import Event, Thread
import asyncio

from log import mylog
from applications.mqtt.mqtt_connect import mqtt_client, SN
from applications.digital_human.common.utils.face import face_recognition
from applications.digital_human.common.utils.attendance import attendance_rec
from applications.digital_human.common.utils.track_record import people_track
from applications.digital_human.common.websocket.get_basic_info import get_question_guide
from applications.digital_human.common.common_utils import read_config, basic_settings_config_path, getMinioPicUrl


RESULT_BUFFER_SIZE = 31
ATTENDANCE_MAX_WAIT_TIME = timedelta(seconds=read_config()['exitAttendanceDuration'])

exit_msg = {
  "messageBody":{"text":"退出考勤打卡模式"},
  "messageType": "sceenExitAttendance",
}

exit_algo_msg = {
    "messageType": "replyStatus",
    "attendanceStatus": "0",
    "clientType": "screen",
}

def is_valid_checkin_time():
    return  True

def reload_ATTENDANCE_MAX_WAIT_TIME(interval):
    global ATTENDANCE_MAX_WAIT_TIME
    ATTENDANCE_MAX_WAIT_TIME = timedelta(seconds=interval)
    
def crop_to_bounding_rect(image, center_x, center_y, radius):
    """
    将图像按照圆形区域的最小外接矩形进行裁剪。
    
    参数:
    - image (ndarray): 输入的原始图像。
    - center_x (int): 圆心的 x 坐标。
    - center_y (int): 圆心的 y 坐标。
    - radius (int): 圆的半径。
    
    返回:
    - 矩形裁剪后的图像。
    """
    # 计算最小外接矩形的左上角和右下角
    x1 = max(center_x - radius, 0)  # 防止越界
    y1 = max(center_y - radius, 0)  # 防止越界
    x2 = min(center_x + radius, image.shape[1])  # 防止越界
    y2 = min(center_y + radius, image.shape[0])  # 防止越界

    # 裁剪图像到最小外接矩形
    cropped_image = image[y1:y2, x1:x2]
    return cropped_image


def alarm_to_platform(new_img):
    minio_upload_sta, minio_img_url = getMinioPicUrl(new_img, SN)
    if minio_upload_sta == 0:
        push_msg = {
            "messageType": "strangerAlarm",
            "messageBody": {
                "serialNumber": SN,
                "alarmTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()),
                "picUrl": minio_img_url
            }
        }
        mqtt_client.publish("digitalHumanTopic", push_msg)
        mylog.info("陌生人识别：报警......")
    else:
        mylog.info("陌生人识别：上传报警图片失败...")


class strangerIdentify:

    def __init__(self, url, push_interval=60):
        self.url = url
        self.event = Event()
        self.is_detecting = False
        self.det_task = []
        self.websockets = None
        
        self.push_interval = push_interval
        self.similar_count = 0
        self.stranger_count = 0
        self.no_people_count = 0
        self.last_alert_time = None
        
        self.attendance_start_time = None
        # self.attendance_face_not_detected = False
        self.attendance_msg_send = False
        self.attendance_result_buffer = deque(maxlen=RESULT_BUFFER_SIZE)
        
        self.det_th = Thread(target=self.asyn_detect)
        self.det_th.start()

    
    async def __detect(self):
        cap = cv2.VideoCapture(self.url)
        if cap.isOpened():
            while True:
                
                if self.event.is_set():
                    self.is_detecting = False
                    time.sleep(0.04)
                    ret, frame = cap.read()
                    continue
                
                ret, frame = cap.read()
                if not ret:
                    mylog.info("人脸识别：拉流失败，正在重新拉流.....")
                    for i in range(5):
                        self.url = get_first_available_video_device()
                        cap = cv2.VideoCapture(self.url)
                        if cap.isOpened() or self.event.is_set():
                            break
                        else:
                            time.sleep(1)
                        i = i + 1

                    self.event.set()
                    continue
                
                self.is_detecting = True
        
                try:
                    # 陌生人识别功能
                    if "stranger" in self.det_task:
                        res = face_recognition.recognize_stranger(frame)                
                        if len(res["known"]) + len(res["stranger"]) != 0:
                            self.no_people_count = 0
                            
                        if len(res["known"]) != 0:
                            self.similar_count += 1
                        else:
                            if len(res["stranger"]) != 0:
                                self.stranger_count += 1
                                if self.last_alert_time is None or (self.last_alert_time is not None and time.time() - self.last_alert_time > self.push_interval):
                                    if self.stranger_count > self.similar_count and self.stranger_count > 10:
                                        new_img = frame.copy()
                                        for box in res["stranger"]:
                                            cv2.rectangle(new_img, (int(box[0]), int(box[1])), (int(box[2]), int(box[3])), (255, 0, 0), 2)
                                            cv2.putText(new_img, "unknow", (int(box[0]), int(box[1]) - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                                        alarm_th = Thread(target=alarm_to_platform, args=(new_img,))
                                        alarm_th.start()
                                        self.similar_count = 0
                                        self.stranger_count = 0
                                        self.last_alert_time = time.time()
                                    
                            else:
                                self.no_people_count += 1
                                if self.no_people_count > 10:
                                    self.similar_count = 0
                                    self.stranger_count = 0
                                    self.no_people_count = 0
                                    # if self.last_alert_time is not None:
                                    #     self.last_alert_time = time.time()
                    
                                
                        # 带追踪的报警
                        # res = face_recognition.recognize_stranger(frame)
                        # if len(res) > 0:
                        #     alarm_res = people_track.trigger_alert(res)
                        #     if alarm_res[0]:
                        #         box = alarm_res[1]
                        #         new_img = frame.copy()
                        #         cv2.rectangle(new_img, (int(box[0]), int(box[1])), (int(box[2]), int(box[3])), (255, 0, 0), 2)
                        #         cv2.putText(new_img, "unknow", (int(box[0]), int(box[1]) - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                                
                        #         alarm_th = Thread(target=alarm_to_platform, args=(new_img,))
                        #         alarm_th.start()
                    
                    # 考勤打卡功能
                    if "attendance" in self.det_task:
                        res_type, matched_person, confidence, frame_base64 = attendance_rec(frame)
                        # 情况一：未识别到人脸
                        if res_type == 0:
                            
                            # if not self.attendance_face_not_detected:
                            msg = {
                                "messageType": "screenAttendanceMode",
                                "messageBody": {
                                    # "image": str(frame_base64),
                                    "code": 0,
                                    "statusmessage": "请将头像置于圆框内"
                                }
                            }
                            await self.websockets['screen'].send(json.dumps(msg))
                            await self.websockets['screen'].send(frame_base64)
                            # self.attendance_face_not_detected = True
                            self.attendance_msg_send = True

                            if self.attendance_start_time is None:
                                self.attendance_start_time = datetime.now()
                            
                            elif datetime.now() - self.attendance_start_time > ATTENDANCE_MAX_WAIT_TIME:
                                self.stopAttendanceDet()
                                await self.websockets['screen'].send(json.dumps(exit_msg))
                                await self.websockets['algorithm'].send(json.dumps(exit_algo_msg))
                                mylog.info("退出考勤打卡.....")
                        
                        # 考勤打卡：人脸占比不足
                        elif res_type == 1:
                            msg = {
                                "messageType": "screenAttendanceMode",
                                "messageBody": {
                                    # "image": str(frame_base64),
                                    "code": 0,
                                    "statusmessage": "请靠近摄像头"
                                }
                            }
                            await self.websockets['screen'].send(json.dumps(msg))
                            await self.websockets['screen'].send(frame_base64)
                            # self.attendance_face_not_detected = True
                            self.attendance_msg_send = True
                            self.attendance_start_time = None
                        
                        # 考勤打卡：识别到正常人脸信息
                        elif res_type == 2:
                            self.attendance_start_time = None
                            if confidence >= 0.4:
                                self.attendance_result_buffer.append("success")
                            else:
                                self.attendance_result_buffer.append("fail")

                            success_count = self.attendance_result_buffer.count("success")
                            fail_count = self.attendance_result_buffer.count("fail")
                            
                            if success_count > RESULT_BUFFER_SIZE // 2:
                                if is_valid_checkin_time():
                                    respond_info = {
                                        "messageType": "attendanceRecord",
                                        "messageBody": {
                                            'idNumber': matched_person['person_id'],
                                            'clockTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                            'serialNumber': SN,
                                        }
                                    }
                                    # publish_mqtt_msg(mqtt_client, "digitalHumanTopic", respond_info)
                                    mqtt_client.publish("digitalHumanTopic", respond_info)
                                    self.attendance_result_buffer.clear()
                                    msg = {
                                        "messageType": "screenAttendanceMode",
                                        "messageBody": {
                                            # "image": str(frame_base64),
                                            "code": 2,
                                            "statusmessage": "考勤打卡成功"
                                        }
                                    }
                                    await self.websockets['screen'].send(json.dumps(msg))
                                    await self.websockets['screen'].send(frame_base64)
                                    mylog.info("考勤打卡：推送考勤打卡成功")
                                    await asyncio.sleep(1)
                                else:
                                    pass
                                    # status_message = "考勤打卡成功，未在打卡时间打卡"
                                    # status_value = 4
                                    # self.attendance_result_buffer.clear()
                                    
                            elif fail_count > RESULT_BUFFER_SIZE // 2:
                                self.attendance_result_buffer.clear()
                                msg = {
                                    "messageType": "screenAttendanceMode",
                                    "messageBody": {
                                        # "image": str(frame_base64),
                                        "code": 3,
                                        "statusmessage": "考勤打卡失败，请联系管理员"
                                    }
                                }
                                await self.websockets['screen'].send(json.dumps(msg))
                                await self.websockets['screen'].send(frame_base64)
                                mylog.info("考勤打卡：推送打卡失败信息")
                                await asyncio.sleep(1)
                            else:
                                msg = {
                                    "messageType": "screenAttendanceMode",
                                    "messageBody": {
                                        # "image": str(frame_base64),
                                        "code": 1,
                                        "statusmessage": "正在识别头像信息"
                                    }
                                }
                                await self.websockets['screen'].send(json.dumps(msg))
                                await self.websockets['screen'].send(frame_base64)
                            self.attendance_start_time = None
                        else:
                            mylog.info("考勤打卡：识别结果代号不正确。")
                    
                    # 人脸识别（要求此时不处于打卡模式）
                    if "people" in self.det_task and "attendance" not in self.det_task:
                        res_code = face_recognition.recognize_face_wake(frame)
                        if res_code in [1, 3, 5]:
                            msg = {
                                "messageType": "replyStatus",
                                "faceWakeUpStatus": "1",
                                "faceWakeUpMessage": "播放欢迎语视频"
                            }
                            await self.websockets['algorithm'].send(json.dumps(msg))
                            await self.websockets['screen'].send(str(get_question_guide()))
                            mylog.info("人脸识别：识别到人脸，唤醒语音....")
                            self.stopPeopleDet()

                        elif res_code in [2, 4]:
                            msg = {
                                "messageType": "replyStatus",
                                "faceWakeUpStatus": "2",
                                "faceWakeUpMessage": "不播放欢迎语视频"
                            }
                            await self.websockets['algorithm'].send(json.dumps(msg))
                            mylog.info("人脸识别：识别到人脸，但是已经唤醒过了....")
                            self.stopPeopleDet()
                            
                        elif res_code == 0:
                            continue
                                                
                except Exception as e:
                    pass
                    # mylog.info(e)
        
        else:
            mylog.info("人脸识别：摄像头打开失败.....")
            if "attendance" in self.det_task:
                msg = {
                    "messageType": "screenAttendanceMode",
                    "messageBody": {
                        # "image": "",
                        "code": 5,
                        "statusmessage": "摄像头异常"
                    }
                }
                await self.websockets['screen'].send(json.dumps(msg))
                self.det_task.remove("attendance")
                time.sleep(1.5)
                await self.websockets['screen'].send(json.dumps(exit_msg))
                await self.websockets['algorithm'].send(json.dumps(exit_algo_msg))
                mylog.info("退出考勤打卡.....")
    
    def asyn_detect(self):
        asyncio.run(self.__detect())
    
    def startStrangerDet(self):
        if "stranger" not in self.det_task:
            self.last_alert_time = None
            self.det_task.append("stranger")
        if not self.is_detecting:
            self.event.clear()
    
    def stopStrangerDet(self):
        if "stranger" in self.det_task:
            self.det_task.remove("stranger")
        if self.is_detecting and len(self.det_task) == 0:
            self.event.set()
    
    def startPeopleDet(self, websockets):
        self.websockets = websockets
        if "people" not in self.det_task:
            self.det_task.append("people")
        if not self.is_detecting:
            self.event.clear()
            
    def stopPeopleDet(self):
        if "people" in self.det_task:
            self.det_task.remove("people")
        if self.is_detecting and len(self.det_task) == 0:
            self.event.set()
           
    def startAttendanceDet(self, websockets):
        if "attendance" not in self.det_task:
            self.det_task.append("attendance")
        self.attendance_start_time = None
        self.attendance_msg_send = False
        self.attendance_result_buffer.clear()
        self.websockets = websockets
        if not self.is_detecting:
            self.event.clear()
    
    def stopAttendanceDet(self):
        if "attendance" in self.det_task:
            self.det_task.remove("attendance")
        self.attendance_start_time = None
        self.attendance_msg_send = False
        self.attendance_result_buffer.clear()
        if self.is_detecting and len(self.det_task) == 0:
            self.event.set()
            
    def stopAllDet(self):
        if len(self.det_task) > 0:
            self.det_task = []
        self.attendance_start_time = None
        self.attendance_msg_send = False
        self.attendance_result_buffer.clear()
        if self.is_detecting:
            self.event.set()
    
    def recoverDet(self, websockets):
        mode = read_config()["currentMode"]
        if mode == "AssistantMode":
            self.det_task.append("people")
        elif mode == "UnattendedMode":
            self.det_task.append("people")
            self.det_task.append("stranger")
        elif mode == "SentryMode":
            self.det_task.append("stranger")
        else:
            pass
        
        self.websockets = websockets
        if not self.is_detecting:
            self.event.clear()
        

def get_first_available_video_device():
    context = pyudev.Context()
    devices = context.list_devices(subsystem='video4linux')
    for device in devices:
        dev_node = device.device_node
        usb_device = device.find_parent(subsystem='usb', device_type='usb_device')
        if usb_device is not None:
            cap = cv2.VideoCapture(dev_node)
            if cap.isOpened():
                cap.release()
                return dev_node
    

device = get_first_available_video_device()
stranger_detect = strangerIdentify(device)