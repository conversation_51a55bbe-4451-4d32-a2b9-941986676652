# Copyright (c)  2022  Xiaomi Corporation (authors: <PERSON><PERSON>)
#
# See ../../LICENSE for clarification regarding multiple authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: style_check

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/style_check.yaml'
      - 'sherpa-onnx/**'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/style_check.yaml'
      - 'sherpa-onnx/**'
  workflow_dispatch:

concurrency:
  group: style_check-${{ github.ref }}
  cancel-in-progress: true

jobs:
  style_check:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Check style with cpplint
        shell: bash
        working-directory: ${{github.workspace}}
        run: ./scripts/check_style_cpplint.sh
