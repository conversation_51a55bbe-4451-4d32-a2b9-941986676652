import json
import schedule
from log import mylog
from applications.digital_human.common.utils.stranger_rec import stranger_detect
from applications.digital_human.common.utils.qa_service import start_qa_service
from applications.digital_human.common.common_utils import read_config, basic_settings_config_path

mode_dict = {
    "0": "SentryMode",
    "1": "UnattendedMode",
    "2": "AssistantMode"
} 

def update_mode(mode):
    conf = read_config()
    conf["currentMode"] = mode
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)
    

# 进入助理模式: 不进行陌生人识别, 语音服务启动
def enter_assistant_mode(last_mode=None, need_update_file=True):
    need_msg = False
    if need_update_file:
        update_mode("AssistantMode")
    stranger_detect.stopStrangerDet()
    if last_mode == "SentryMode" or last_mode is None:
        need_msg = start_qa_service()
        mylog.info("模式切换: 进入助理模式")
    elif last_mode == "AssistantMode":
        mylog.info("模式切换: 目前已经是助理模式")
    else:
        pass
    return need_msg
        

# 进入无人值守模式: 陌生人识别, 语音服务启动
def enter_unattended_mode(last_mode=None, need_update_file=True):
    need_msg = False
    if need_update_file:
        update_mode("UnattendedMode")
    if last_mode == "AssistantMode":
        stranger_detect.startStrangerDet()
        mylog.info("模式切换: 进入无人值守模式")
    elif last_mode == "SentryMode":
        need_msg = start_qa_service()
        mylog.info("模式切换: 进入无人值守模式")
    elif last_mode is None:
        stranger_detect.startStrangerDet()
        need_msg = start_qa_service()
        mylog.info("模式切换: 进入无人值守模式")
    elif last_mode == "UnattendedMode":
        mylog.info("模式切换: 目前已经是无人值守模式")
    else:
        pass
    return need_msg
            

# 进入宣传模式（无法对话，只能从助理模式和无人值守模式进入）
def enter_propagate_mode(need_update_file=True):
    stranger_detect.stopPeopleDet() # 禁用人脸唤醒功能
    if need_update_file:
        conf = read_config()
        conf["currentStatus"].append("propagate")
        with open(basic_settings_config_path, 'w') as write_f:
            json.dump(conf, write_f, indent=4, ensure_ascii=False)
    mylog.info("模式切换: 进入宣传模式")

# 退出宣传模式
def exit_propagate_mode(websockets_client):
    stranger_detect.startPeopleDet(websockets_client)  # 启用人脸识别功能
    conf = read_config()
    if "propagate" in conf["currentStatus"]:
        conf["currentStatus"].remove("propagate")
        with open(basic_settings_config_path, 'w') as write_f:
            json.dump(conf, write_f, indent=4, ensure_ascii=False)
    mylog.info("模式切换: 退出宣传模式")