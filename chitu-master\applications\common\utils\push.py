from datetime import datetime
import requests
import json
import time
import hmac
import os
import uuid
import socket
import hashlib
import base64
import asyncio
import urllib.parse
from flask import current_app
from applications.extensions import db
from flask_mail import Message
from flask_mail import Mail
from applications.common.utils.mail import get_email_config_by_type
from applications.models  import PushSettings,Alarm_Record,PushTemplate,AlarmPushRecord,DingTalkWebhookSettings,\
Email_Push_Receive,Email_Push_Set,WebChatWebhookSettings,Camera,Algorithm
from log import mylog
from applications.common.utils.mail import check_bounced_emails
def send_wechat(content, topic, webhook_url,template_type):
    # Implement WeChat push logic
    headers = {'Content-Type': 'application/json'}
    
    if template_type == '1':  # 文本类型
        data = {
            "msgtype": "text",
            "text": {
                "content": f"{topic}\n{content}"
            }
        }
    elif template_type == '2':  # 图文类型（Markdown）
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"**{topic}**\n{content}"
            }
        }
    

    response = requests.post(webhook_url, headers=headers, data=json.dumps(data))

    if response.json()['errcode'] == 0:
        return True
    else:
        return False






def generate_sign(secret):
    timestamp = str(round(time.time() * 1000))
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
    return timestamp, sign
def send_dingtalk(content,topic, webhook_url,secret,template_type):
    headers = {'Content-Type': 'application/json'}
  
    timestamp, sign = generate_sign(secret)
    if template_type == '1':  # 文本类型
        data = {
            "msgtype": "text",
            "text": {
                "content": f"{topic}\n{content}"
            }
        }
    elif template_type == '2':  # 图文类型（Markdown）
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "测试",
                "text": f"**{topic}**\n{content}"
            }
        }
    url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
    response = requests.post(url, headers=headers, json=(data))
  
    if response.json()['errcode'] == 0:
        return True
    else:
        return False





#入报警库
def add_alarm_record(data):
    try:
        pic_url = data.get('pic_url')#"pic/xx.jpg"
        pic_path = data.get('raw_pic_name') #"application/xxx/111.jpg"

        # 检查记录总数并删除多余的旧记录，确保总数保持在 1000 条以内
        total_records = Alarm_Record.query.count()
        if total_records >= current_app.config['ALARM_RECORD_MAX_ITERMS']:
            excess_records = total_records - 999  # 保留 999 条，加上新记录正好 1000 条
            oldest_records = Alarm_Record.query.order_by(Alarm_Record.alarm_time).limit(excess_records).all()

            for record in oldest_records:
                pic_name = record.pic_url.split("/")[1]
                pic_path = "applications/algorithm/alarm_imgs/"+pic_name
                # 删除对应的图片文件
                if os.path.exists(pic_path):
                    os.remove(pic_path)
                
                db.session.delete(record)
            db.session.commit()

        # 查找是否有相同 pic_url 的记录
        existing_record = Alarm_Record.query.filter_by(pic_url=pic_url).first()

        if existing_record:
            # 更新现有记录
            existing_record.camera_label = data.get("camera_label")
            existing_record.alarm_type = data.get('alarm_type')
            existing_record.description = data.get('description')
            existing_record.status = '0'
            existing_record.alarm_time = data.get('alarm_time')
            # 提交数据库更改
            db.session.commit()
        else:
            # 添加新记录
            new_record = Alarm_Record(
                camera_label=data.get("camera_label"),
                alarm_type=data.get('alarm_type'),
                pic_url=pic_url,
                description=data.get('description'),
                status='0',
                alarm_time=data.get('alarm_time')
            )
            db.session.add(new_record)
            db.session.commit()
    except Exception as e:
        db.session.rollback()
        mylog.error(f"An error occurred: {e}")



async def send_wechat_async(content, topic, webhook_url, template_type):
    return send_wechat(content, topic, webhook_url, template_type)

async def send_dingtalk_async(content,topic, webhook_url,secret,template_type):
    return send_dingtalk(content,topic, webhook_url,secret,template_type)

async def send_email_task_async(content, topic, recipient_emails, template_type):
    
    if template_type =="1": #文本类型
        return send_email_task(content, topic, recipient_emails, template_type)
    elif template_type =="2": #图文类型
        return send_email_with_embedded_image(content, topic, recipient_emails, template_type)

def get_active_push_settings():
    return PushSettings.query.filter_by(status='1').all()

def get_push_template(push_type):
    return PushTemplate.query.filter_by(push_type=push_type, status='1').first()

async def push_message(push_type, template, alarm_data):



    # temp_content = ":{}\n".join(template.content.split("/"))+":{}"
    # content = temp_content.format(*alarm_data)
    # template_type = template.template_type  # 模板类型
    
    if '1' in push_type  :  # 微信推送

        temp_content = ":{}\n".join(template['1'].content.split("/"))+":{}"
        content = temp_content.format(*alarm_data)
        template_type = template['1'].template_type  # 模板类型
        wechat_webhook = WebChatWebhookSettings.query.first()
        if wechat_webhook:
            success = await send_wechat_async(content, template['1'].topic, wechat_webhook.webhook_url, template_type)
            status = '1' if success else '0'
            target = None
            add_push_record("1", content, target, status)

    if '2'in push_type  :  # 钉钉推送
     
        temp_content = ":{}\n".join(template['2'].content.split("/"))+":{}"
        
        content = temp_content.format(*alarm_data)
        template_type = template['2'].template_type  # 模板类型
        dingtalk_webhook = DingTalkWebhookSettings.query.first()
        if dingtalk_webhook:
            content += "\n"+ "" if not dingtalk_webhook.customize_key_word else dingtalk_webhook.customize_key_word
            secret = dingtalk_webhook.secret_key
            success = await send_dingtalk_async(content,template["2"].topic, dingtalk_webhook.webhook_url,secret,template_type)
            status = '1' if success else '0'
            target = None
            add_push_record("2", content, target, status)

    if '3' in push_type :  # 邮件推送
 
        temp_content = ":{}\n".join(template['3'].content.split("/"))+":{}"
        content = temp_content.format(*alarm_data)
        template_type = template['3'].template_type  # 模板类型

        email_recipients = Email_Push_Receive.query.all()
        recipient_emails = [recipient.receive_email for recipient in email_recipients]
        tmp_topic = template["3"].content.split('/')[0]
        topic = alarm_data[0]+f" ({tmp_topic})"
        for recipient in recipient_emails:
            success   = await send_email_task_async(content, topic, recipient, template_type)

            status = '1' if success else '0'
            
            add_push_record("3", content, recipient, status)
  

                
  

   
    


def send_email_task(content, topic,recipient,template_type):
    # Implement email push logic

    push_email = Email_Push_Set.query.first()   
    email_type = push_email.type
    password = push_email.password
    email_config = get_email_config_by_type(email_type)
    send_email = push_email.send_email

    current_app.config.update(
        MAIL_SERVER=email_config['smtp_server'],
        MAIL_PORT=email_config['smtp_port'],
        MAIL_USE_SSL=email_config['use_ssl'],
        MAIL_USE_TLS=email_config['use_tls'],
        MAIL_USERNAME=send_email,
        MAIL_PASSWORD=password,
        MAIL_DEFAULT_SENDER=send_email
    )
    
    mail = Mail(current_app)
    
    try:
            msg = Message(subject=topic, recipients=[recipient], body=content, sender=send_email)
            mail.send(msg)
    except Exception as e:
           return False
    return True

    
  
   
    




def send_email_with_embedded_image(content, topic,recipient,template_type):
    
    push_email = Email_Push_Set.query.first()   
    email_type = push_email.type
    
    password = push_email.password

    email_config = get_email_config_by_type(email_type)
    send_email = push_email.send_email
    
    img_url = content.split("\n")[-1].split(":")[-1]
    
    formatted_content = content.replace("\n", "<br>")
    formatted_content = "<br>".join(formatted_content.split("<br>")[:4])
    current_app.config.update(
            MAIL_SERVER = email_config['smtp_server'],
            MAIL_PORT = email_config['smtp_port'],
            MAIL_USE_SSL= email_config['use_ssl'],
            MAIL_USE_TLS = email_config['use_tls'],
            MAIL_USERNAME= send_email,
            MAIL_PASSWORD = password,
            MAIL_DEFAULT_SENDER = send_email)


    # 动态创建 HTML 内容
    html_content = "<html><body>"
    html_content += f"<p>{formatted_content}</p>"
    html_content += f"<p></p><img src='cid:image1'>"
    html_content += "</body></html>"
    
    mail = Mail(current_app)
    
    try:
        msg = Message(subject=topic, recipients=[recipient], body=content, sender=send_email)
        msg.html = html_content
        # 读取图片并嵌入到邮件中
        with open(img_url, 'rb') as img:
            msg.attach('image1', 'image/png', img.read(), 'inline', headers=[['Content-ID', '<image1>']])

        mail.send(msg)
        
    except Exception as e:
        return False
    return True
    
   







#content = {} 发生在 {}，视频来源: {}，摄像头描述: {}
#pic_url
def add_push_record(push_type, content, target, status):
    try:
        # 添加新记录
        new_record = AlarmPushRecord(
            push_type=push_type,
            content=content,
            push_target=target,
            status=status,
            create_time=datetime.now()
        )
        db.session.add(new_record)
        db.session.commit()

        # 检查记录总数
        total_records = AlarmPushRecord.query.count()
        if total_records > current_app.config['PUSH_RECORD_MAX_ITERMS']:
            # 删除多余的旧记录，保持总数在 1000 条以内
            excess_records = total_records - current_app.config['PUSH_RECORD_MAX_ITERMS']
            oldest_records = AlarmPushRecord.query.order_by(AlarmPushRecord.create_time).limit(excess_records).all()
            for record in oldest_records:
                db.session.delete(record)
            db.session.commit()

    except Exception as e:
        
        db.session.rollback()  # 确保在发生异常时回滚事务
    # finally:
    #     db.session.close()  # 确保在操作完成后关闭数据库连接



def get_local_ip():  
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)  
    try:  
          
        s.connect(('*******', 1))  
        ip_address = s.getsockname()[0]  
    except Exception:  
        ip_address = '127.0.0.1'  
    finally:  
        s.close()  
    return ip_address  


async def  handle_alarm(data):
    
    camera_id = data.get('camera_id')
    algo_id = data.get("alarm_type")
    pic_url = data.get("pic_url")
    algo_name = Algorithm.query.filter_by(id=algo_id).first().name
    camera_info = Camera.query.filter_by(id=camera_id).first()
    camer_des = camera_info.remark
    camera_label = camera_info.camera_label

    data['camera_label'] = camera_label
    data['description'] = camer_des
    data['alarm_type'] = algo_name
    ip_address = get_local_ip()
    img_name = os.path.basename(pic_url)
    data['pic_url'] = f"pic/{img_name}"
    data['raw_pic_name'] = pic_url

    
    add_alarm_record(data)
    # alarm_data = {
    #     'camera_id': '',
    #     'alarm_type': '',
    #     'pic_url': '',
    #     'alarm_time': ''
    # }

    
    push_data = [
    f"{data.get('alarm_type')}", #检测类型
    f"{data.get('alarm_time', datetime.now()).strftime('%Y-%m-%d %H:%M:%S')}",#检测时间
    f"{camera_label}", #视频来源
    f"{camer_des}",#摄像头描述,
    f"{data.get('raw_pic_name','')}",    
]

    tasks = []
    templates = {}
    active_settings = get_active_push_settings()
    for setting in active_settings:
        template = get_push_template(setting.push_type)#1.文字 2.图文
        if template:
            templates[setting.push_type] = template

    

    task = asyncio.create_task(push_message(list(templates.keys()), templates, push_data))
    tasks.append(task)
    await asyncio.gather(*tasks)

def handle_alarm_sync(data, app):
    with app.app_context():
        #wechat_queue = MessageQueue(20, send_wechat)
        #dingtalk_queue = MessageQueue(20, send_dingtalk)
        #wechat_queue.schedule_combine()
        #dingtalk_queue.schedule_combine()
        asyncio.run(handle_alarm(data))
