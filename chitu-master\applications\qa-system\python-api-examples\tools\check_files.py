#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: check_files.py

Description: This is an check model path Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

from pathlib import Path

def assert_file_exists(filename: str):
    assert Path(filename).is_file(), (
        f"{filename} does not exist!\n"
        "Please refer to "
        "https://k2-fsa.github.io/sherpa/onnx/pretrained_models/index.html to download it"
    )

