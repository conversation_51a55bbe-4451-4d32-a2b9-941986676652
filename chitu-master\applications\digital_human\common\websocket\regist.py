import json
import time
from log import mylog
from applications.digital_human.common.utils.stranger_rec import stranger_detect, basic_settings_config_path
from  applications.digital_human.common.websocket import get_basic_info

async def websocket_regist(websocket, recv_text, clients):
    
    clientType = recv_text["clientType"]
    conf = get_basic_info.read_config()
    
    if recv_text["messageType"] == "regist" and recv_text["clientType"] in ["screen", "algorithm"]:
        
        # 屏幕端注册处理
        if clientType == "screen":
            # 向前端发送基础配置信息
            await websocket.send(str(get_basic_info.get_screen_dirction()))
            await websocket.send(str(get_basic_info.get_back_ground()))
            await websocket.send(str(get_basic_info.get_speaker()))
            await websocket.send(str(get_basic_info.get_interaction()))
            await websocket.send(str(get_basic_info.get_question_guide()))
            await websocket.send(str(get_basic_info.get_propaganda_pics()))
            await websocket.send(str(get_basic_info.get_sleepy_pics()))
            if get_basic_info.read_version_info()["newVersion"]["version"] != "":
                update_info = {
                    "messageType": "devOpsMessage",
                    "messageBody": "version"
                }
                await websocket.send(json.dumps(update_info))
            mode = conf["currentMode"]
            if "propagate" in conf["currentStatus"]:
                modeMsg = {
                    "messageType": "messageToast",
                    "messageBody": {
                        "mode": "5",
                        "code": 200,
                        "text": "我已进入宣传模块。"
                    }
                }
            else:
                if mode == "SentryMode":
                    modeMsg = {
                        "messageType": "messageToast",
                        "messageBody": {
                            "mode": "3",
                            "code": 200,
                            "text": "下班了,我将进入哨兵模式"
                        }
                    }
                elif mode == "AssistantMode":
                    modeMsg = {
                        "messageType": "messageToast",
                        "messageBody": {
                            "mode": "1",
                            "code": 200,
                            "text": "我已进入助理模式"
                        }
                    }
                elif mode == "UnattendedMode":
                    modeMsg = {
                        "messageType": "messageToast",
                        "messageBody": {
                            "mode": "2",
                            "code": 200,
                            "text": "我已进入无人值守模式"
                        }
                    }
                else:
                    pass
                    
            await websocket.send(json.dumps(modeMsg))
            if "safeguard" in conf["currentStatus"]:
                yunwei_msg = {
                    "messageType": "enterDevOps",
                    "messageBody": {
                        "data": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
                    }
                }
                await websocket.send(json.dumps(yunwei_msg))
                
            if "algorithm" not in clients.keys() and mode != "SentryMode":
                warnMsg = {
                    "messageType": "messageToast",
                    "messageBody": {
                        "mode": "7",
                        "code": 300,
                        "text": "服务正在启动..."
                    }
                }
                await websocket.send(json.dumps(warnMsg))

        # 语音算法注册
        elif clientType == "algorithm":
            clients["algorithm"] = websocket
            if "propagate" not in conf["currentStatus"]:
                stranger_detect.startPeopleDet(clients)
                mylog.info("人脸唤醒功能开启...")
            else:
                # 进入宣传模块，发消息给语音算法
                enter_propagate_info = {
                    "messageType": "replyStatus",
                    "propagandaStatus": "1",
                    "propagandaMessage": "进入宣传模式"
                }
                await websocket.send(json.dumps(enter_propagate_info))
                mylog.info("目前处于宣传模块，人脸唤醒功能不开启...")

            info = {
                "messageType": "replyStatus",
                "status": "1",
                "clientType": "algorithm"
            }
            await websocket.send(json.dumps(info))
        else:
            pass
        
        return {"result": True, "clientType": clientType}
    else:
        return {"result": False}