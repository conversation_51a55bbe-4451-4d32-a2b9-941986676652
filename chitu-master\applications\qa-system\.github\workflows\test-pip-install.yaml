name: test-pip-install

on:
  push:
    branches:
      - test-pip-install
  schedule:
    # minute (0-59)
    # hour (0-23)
    # day of the month (1-31)
    # month (1-12)
    # day of the week (0-6)
    # nightly build at 23:50 UTC time every day
    - cron: "50 23 * * *"
  workflow_dispatch:

concurrency:
  group: test-pip-install-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test_pip_install:
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os }} ${{ matrix.python-version }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.7", "3.8", "3.9", "3.10", "3.11", "3.12"]
        exclude:
          - os: macos-latest
            python-version: "3.7"

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install sherpa-onnx
        shell: bash
        run: |
          pip install --verbose sherpa-onnx
          # python3 -m pip install --verbose .

      - name: Test sherpa-onnx
        shell: bash
        run: |
          python3 -c "import sherpa_onnx; print(sherpa_onnx.__file__)"
          python3 -c "import sherpa_onnx; print(sherpa_onnx.__version__)"

          p=$(python3 -c "from pathlib import Path; import sys; print(Path(sys.executable).parent)")
          echo $p
          ls -lh $p
          # ls -lh $p/bin
          # export PATH=$p/bin:$PATH

          # For windows
          export PATH=/c/hostedtoolcache/windows/Python/3.7.9/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.8.10/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.9.13/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.10.11/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.11.8/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.12.2/x64/bin:$PATH

          sherpa-onnx --help
          sherpa-onnx-keyword-spotter --help
          sherpa-onnx-offline --help
          sherpa-onnx-offline-tts --help

          sherpa-onnx-microphone --help
          sherpa-onnx-microphone-offline --help

          sherpa-onnx-offline-websocket-server --help

          sherpa-onnx-online-websocket-server --help
          sherpa-onnx-online-websocket-client --help
