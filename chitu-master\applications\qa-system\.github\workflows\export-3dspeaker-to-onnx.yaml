name: export-3dspeaker-to-onnx

on:
  workflow_dispatch:

concurrency:
  group: export-3dspeaker-to-onnx-${{ github.ref }}
  cancel-in-progress: true

jobs:
  export-3dspeaker-to-onnx:
    if: github.repository_owner == 'k2-fsa' || github.repository_owner == 'csukuangfj'
    name: export 3d-speaker to ONNX
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest]
        python-version: ["3.8"]

    steps:
      - uses: actions/checkout@v4

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Run
        shell: bash
        run: |
          cd scripts/3dspeaker
          ./run.sh

          mv -v *.onnx ../..

      - name: Release
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          file: ./*.onnx
          overwrite: true
          repo_name: k2-fsa/sherpa-onnx
          repo_token: ${{ secrets.UPLOAD_GH_SHERPA_ONNX_TOKEN }}
          tag: speaker-recongition-models
