import os
import gc
import yaml
from .utils_rknn.utils import load_model, letterbox, predict, get_draw_box


class InferenceRknn(object):
    def __init__(self, label, weight_path, model_name, number):

        self.wh = [640, 640]
        self.confidence = 0.45      # 提高到80%，严格减少误检
        self.nms_threshold = 0.5   # 提高到50%，减少重复检测

        self.label = label
        self.judge = True
        self.rknn = load_model(os.path.join(weight_path, "{}.rknn".format(model_name)), number)

        # 添加参数日志
        # print(f"[PLAY_PHONE_INIT] 玩手机检测参数设置:")
        # print(f"[PLAY_PHONE_INIT] 置信度阈值: {self.confidence}")
        # print(f"[PLAY_PHONE_INIT] NMS阈值: {self.nms_threshold}")
        # print(f"[PLAY_PHONE_INIT] 输入尺寸: {self.wh}")

    def run(self, image, gain):
        """ 预测一张图片，预处理保持宽高比 """
        box_src = predict(self.confidence, self.nms_threshold, image, self.wh, self.rknn)
        draw_boxes = get_draw_box(box_src, gain, self.label)
        gc.collect()
        return draw_boxes
