#!/usr/bin/env python3
"""
诊断导入问题的脚本
"""

import sys
import os
import importlib

def diagnose_file_structure():
    """诊断文件结构"""
    print("🔍 诊断文件结构")
    print("=" * 50)
    
    # 检查当前工作目录
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 检查是否在正确的目录结构中
    if 'chitu2-model-framework' in cwd:
        print("✅ 在chitu2-model-framework目录中")
        models_dir = os.path.join(cwd, 'models')
    else:
        print("⚠️ 不在chitu2-model-framework目录中，尝试查找models目录")
        models_dir = 'models'
        if not os.path.exists(models_dir):
            models_dir = 'detect_face_alarm'
    
    print(f"models目录: {models_dir}")
    print(f"models目录存在: {os.path.exists(models_dir)}")
    
    # 检查detect_face_alarm目录
    if 'models' in models_dir:
        face_alarm_dir = os.path.join(models_dir, 'detect_face_alarm')
    else:
        face_alarm_dir = models_dir
    
    print(f"detect_face_alarm目录: {face_alarm_dir}")
    print(f"detect_face_alarm目录存在: {os.path.exists(face_alarm_dir)}")
    
    # 检查v2_0_0目录
    v2_0_0_dir = os.path.join(face_alarm_dir, 'v2_0_0')
    print(f"v2_0_0目录: {v2_0_0_dir}")
    print(f"v2_0_0目录存在: {os.path.exists(v2_0_0_dir)}")
    
    if os.path.exists(v2_0_0_dir):
        # 列出v2_0_0目录的内容
        print(f"v2_0_0目录内容:")
        for item in os.listdir(v2_0_0_dir):
            item_path = os.path.join(v2_0_0_dir, item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
            else:
                print(f"  📄 {item}")
        
        # 检查关键文件
        key_files = ['main.py', 'utils/__init__.py', 'utils/face_recognition.py']
        for file_path in key_files:
            full_path = os.path.join(v2_0_0_dir, file_path)
            exists = os.path.exists(full_path)
            print(f"  {'✅' if exists else '❌'} {file_path}")
    
    return v2_0_0_dir if os.path.exists(v2_0_0_dir) else None

def test_importlib_import(v2_0_0_dir):
    """测试importlib导入"""
    print("\n🔍 测试importlib导入")
    print("=" * 50)
    
    if not v2_0_0_dir:
        print("❌ v2_0_0目录不存在，无法测试")
        return False
    
    try:
        # 添加models目录到sys.path（如果存在）
        models_dir = os.path.dirname(os.path.dirname(v2_0_0_dir))
        if 'models' in models_dir and models_dir not in sys.path:
            sys.path.insert(0, models_dir)
            print(f"添加到sys.path: {models_dir}")
        
        # 尝试importlib导入
        module_name = "detect_face_alarm.v2_0_0.main"
        print(f"尝试导入: {module_name}")
        
        main_module = importlib.import_module(module_name)
        print("✅ importlib导入成功")
        
        if hasattr(main_module, 'load'):
            print("✅ load函数存在")
        else:
            print("❌ load函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ importlib导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_import(v2_0_0_dir):
    """测试直接导入"""
    print("\n🔍 测试直接导入")
    print("=" * 50)
    
    if not v2_0_0_dir:
        print("❌ v2_0_0目录不存在，无法测试")
        return False
    
    try:
        # 添加v2_0_0目录到sys.path
        if v2_0_0_dir not in sys.path:
            sys.path.insert(0, v2_0_0_dir)
            print(f"添加到sys.path: {v2_0_0_dir}")
        
        # 测试导入utils模块
        print("尝试导入utils模块...")
        import utils
        print("✅ utils模块导入成功")
        
        # 测试导入DetectFaceAlarm
        print("尝试导入DetectFaceAlarm...")
        from utils import DetectFaceAlarm
        print("✅ DetectFaceAlarm导入成功")
        
        # 测试导入main模块
        print("尝试导入main模块...")
        import main
        print("✅ main模块导入成功")
        
        if hasattr(main, 'load'):
            print("✅ load函数存在")
        else:
            print("❌ load函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_python_path():
    """检查Python路径"""
    print("\n🔍 检查Python路径")
    print("=" * 50)
    
    print("当前sys.path:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    print(f"\nPython版本: {sys.version}")
    print(f"Python可执行文件: {sys.executable}")

def main():
    """主诊断函数"""
    print("🚀 诊断导入问题")
    print("=" * 60)
    
    # 诊断文件结构
    v2_0_0_dir = diagnose_file_structure()
    
    # 检查Python路径
    check_python_path()
    
    # 测试导入
    tests = [
        ("直接导入测试", lambda: test_direct_import(v2_0_0_dir)),
        ("importlib导入测试", lambda: test_importlib_import(v2_0_0_dir)),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出诊断总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
        print("\n💡 建议:")
        print("1. 确保在正确的目录中运行（chitu2-model-framework）")
        print("2. 检查文件结构是否完整")
        print("3. 清理Python缓存文件")
        print("4. 检查文件权限")

if __name__ == "__main__":
    main()
