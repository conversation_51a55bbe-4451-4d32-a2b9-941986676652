#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: message_struct.py

Description: This is an struct message from qa system Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import json
import time

#问题推送
def question_push_json_message_struct(question):
    question_push_dict = {
                          "clientType":"algorithm",
                          "messageType":"questionPush",
                          "messageBody":{
                                         "text":question,
                                         "createTime":time.time()
                              }
                         }
    return(json.dumps(question_push_dict))

#答案推送
def answer_push_json_message_struct(videoUrl, text, videoDuration, fileType, fileUrl, status=1, answerId=None):
    answer_push_dict = {
                        "clientType":"algorithm",    
                        "messageType":"answerPush",
                        "messageBody":{
                                       "videoUrl": videoUrl,
                                       "text": text,
                                       "duration": videoDuration,
                                       "createTime": time.time(),
                                       "status": status,
                                       "fileType": fileType,
                                       "fileUrl": fileUrl,
                                       "id": answerId
                                      }
                       }
    return(json.dumps(answer_push_dict))

#命令词推送
def command_word_push_json_message_struct(messageType,result,commandWord):
    command_word_push_dict = {
                              "clientType":"algorithm",
                              "messageType": messageType,
                              "messageBody": {
                                              "text":result,
                                              "commandWord":commandWord,
                                              "createTime": time.time()
                                             }
                             }
    return json.dumps(command_word_push_dict)

#气泡消息推送
def qa_empty_message_push_struct(code,text,mode=0,questionGuideList = []):
    qa_empty_push_dict = {
                        "clientType":"algorithm",
                        "messageType":"messageToast",
                        "messageBody":{
                                       "mode": mode, #0、1、2 0：其他命令词；1：无人值守模式； 2：有人值守模式
                                       "code": code, 
                                       "text": text,
                                       "data": questionGuideList,
                                       "createTime":time.time()
                                      }
                       }
    return(json.dumps(qa_empty_push_dict))

#退出对话推送
def face_exit_wake_up_push_struct():
    face_exit_wake_up_push_dict = {
            "messageType": "exitQaService",
            "clientType": "algorithm",
            "messageBody":{"createTime": time.time()}
            }
    return(json.dumps(face_exit_wake_up_push_dict))

#开始对话推送
def start_qa_service_push_struct():
    start_qa_service_push_dict = {
            "messageType":"startQaService",
            "clientType":"algorithm",
            "messageBody":{"createTime": time.time()}
            }
    return(json.dumps(start_qa_service_push_dict))

#关键词推送
def key_word_push_struct(content):
    key_word_push_dict = {
        "messageType": "keyWordPush",
        "clientType":"algorithm",
        "content": content
    }
    return json.dumps(key_word_push_dict)

#重启问答算法
def restart_qa_service_push_struct():
    restart_qa_service_push_dict =  {
            "messageType": "restartQaService",
            "clientType": "algorithm",
            "messageBody":{"createTime": time.time()}
            }
    return(json.dumps(restart_qa_service_push_dict)) 

#退出打开
def exit_attendance_mode_push_struct():
    exit_attendance_mode_push_dict = {
        "messageBody":{"text":"退出考勤打卡模式", "createTime": time.time()},
        "messageType": "sceenExitAttendance",
        "clientType": "algorithm"
        }
    return(json.dumps(exit_attendance_mode_push_dict))

#问题指引推送
def question_guide_push_struct(questionGuideList):
    question_guide_push_dict = {
        "messageType": "questionGuide",
        "clientType": "algorithm",
        "messageBody": questionGuideList
    }
    return(json.dumps(question_guide_push_dict))

#问题推送状态
def question_status_push_struct(questionPushStatus):
    question_status_push_dict =  {
        "messageType": questionPushStatus,
        "clientType": "algorithm",
        "messageBody":{"createTime": time.time()}
    }
    return(json.dumps(question_status_push_dict))
