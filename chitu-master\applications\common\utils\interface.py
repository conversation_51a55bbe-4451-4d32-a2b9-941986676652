import os
import socket
import subprocess
import netifaces
import psutil
import ipaddress
def netmask_to_cidr(netmask):
    return ipaddress.IPv4Network(f'0.0.0.0/{netmask}').prefixlen





#获取所有的网络接口
def get_network_interfaces():
    interfaces = psutil.net_if_addrs()
    gateways = netifaces.gateways()
    default_gateway = gateways.get('default', {}).get(netifaces.AF_INET)
    network_info = []
    lan_id = 1  # 初始 lan_id
    

    

    sorted_interfaces = sorted(interfaces.items())

    for interface_name, interface_addresses in sorted_interfaces:
        if interface_name not in ['eth0', 'eth1']:
            continue

        ipv4_address = None
        ipv6_address = None
        other_address = None

        for address in interface_addresses:
            if address.family == socket.AF_INET:
                ipv4_address = address
            elif address.family == socket.AF_INET6:
                ipv6_address = address
            else:
                other_address = address

        if ipv4_address:
            network_info.append({
                'lan_id': lan_id,
                'lan_name': interface_name,
                'ip': ipv4_address.address,
                'gateway': default_gateway[0] if default_gateway and default_gateway[1] == interface_name else '',
                'subnet_mask': ipv4_address.netmask
            })
        elif ipv6_address:
            network_info.append({
                'lan_id': lan_id,
                'lan_name': interface_name,
                'ip': "",
                'gateway': default_gateway[0] if default_gateway and default_gateway[1] == interface_name else '',
                'subnet_mask': '*************'
            })
        else:
            network_info.append({
                'lan_id': lan_id,
                'lan_name': interface_name,
                'ip': "",
                'gateway': default_gateway[0] if default_gateway and default_gateway[1] == interface_name else '',
                'subnet_mask': '*************'
                })

        lan_id += 1
    network_info.sort(key=lambda x: x['gateway'] == '')

    return network_info



def get_serial_number():
    try:
        # This example assumes a Linux system
        serial_file = "/proc/cpuinfo"
        if os.path.exists(serial_file):
            with open(serial_file, 'r') as f:
                for line in f:
                    if line.startswith('Serial'):
                        return line.split(":")[1].strip()
        # Alternative method using a subprocess to call a system command
        result = subprocess.run(['cat', '/proc/cpuinfo'], stdout=subprocess.PIPE)
        for line in result.stdout.decode().split('\n'):
            if line.startswith('Serial'):
                return line.split(":")[1].strip()
        return "Unknown"
    except Exception as e:
        return str(e)
    
def get_local_ip():  
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)  
    try:  
        
        s.connect(('*******', 1))  
        ip_address = s.getsockname()[0]  
    except Exception:  
        ip_address = '127.0.0.1'  
    finally:  
        s.close()  
    return ip_address  