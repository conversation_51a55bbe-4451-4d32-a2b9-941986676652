import os
import time
import psutil
import subprocess
import re
from flask import current_app
import hashlib
import json
from log import mylog

# # confRequest_topic = 'edgeBoxAiRequestConf'
# config_topic = "edgeBoxAiAlarmConf"
# alarm_topic = "edgeBoxAlarm"
regist_topic = "deviceRegisterTopic"
device_usage_topic = "edgeBoxResource"
# except_topic = "edgeBoxAbnormalReport"
# updateTopic = 'upgradeResultTopic'

NEED_SUBSCRIBE_TOPICS = ["deviceRegisterResponse", "RequestEdgeBoxResource", "digitalAvatarTopic"]

FILE_MOUNTED_ON = current_app.config['FILE_MOUNTED_ON']
SEND_DEVICE_USAGE_INTERVAL = current_app.config["SEND_DEVICE_USAGE_INTERVAL"]
RE_REGIST_INTERVAL = current_app.config['RE_REGIST_INTERVAL']
MINIO_ADDRESS = current_app.config['MINIO_ADDRESS']
MINIO_USERNAME = current_app.config['MINIO_USERNAME']
MINIO_PASSWORD = current_app.config['MINIO_PASSWORD']

DIGITAL_HUMAN_VERSION_PATH = "/mnt/keep/workspace/digital_human_data/version.json"
def get_digital_human_version():
    with open(DIGITAL_HUMAN_VERSION_PATH, 'r', encoding = 'utf-8') as f:
        data = json.load(f)
    return data["currentVersion"]

def encrypt_msg(raw_str):
    serial_hash = hashlib.sha256((raw_str).encode(encoding='UTF-8')).hexdigest()
    serial_salt = serial_hash + 'nx9uwkl230ocna02ln45zGas0p3gf0345'
    target_str = hashlib.sha256(serial_salt.encode(encoding='UTF8')).hexdigest().upper()
    return target_str

def publish_mqtt_msg(client, topic, msg):
    status = client.publish(topic, json.dumps({"plainText": str(msg), "cipherText": encrypt_msg(str(msg))}), qos=0)
    return status[0]

def generate_SN():
    
    # get CPU serial info
    try:
        with open('/proc/cpuinfo',  'r') as f:
            cpu_info = f.readlines()
            cpu_serial = cpu_info[-1].split(':')[-1].strip()
    except:
        cpu_serial = ''

    # get disk serial info
    import subprocess
    disk_lsblk = subprocess.Popen('lsblk -d', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    disk_utf = disk_lsblk.stdout.read().decode('utf-8').split('\n')
    disk_list = []
    for dis in disk_utf:
        disk_one = []
        di = dis.split(' ')
        for d in di:
            if len(d) != 0:
                disk_one.append(d)
        disk_list.append(disk_one)

    name = disk_list[0].index('NAME')
    rm = disk_list[0].index('RM')
    ro = disk_list[0].index('RO')
    size = disk_list[0].index('SIZE')
    disk_target = []
    size_target = []

    for disk_names in disk_list[1:]:
        if len(disk_names) == 0:
            continue
        if disk_names[rm] != '0':
            continue
        if disk_names[ro] != '0':
            continue
        if 'G' not in disk_names[size]:
            continue
        disk_target.append(disk_names[name])
        size_target.append(disk_names[size])

    disk_id = []
    if len(disk_target) >= 1:
        disk_targets = disk_target[disk_target.index(min(disk_target))]
        disk_message = subprocess.Popen('udevadm info --query=all --name={}'.format(disk_targets), shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        disk_messages = disk_message.stdout.read().decode('utf-8').split('\n')
        for message in disk_messages:
            if 'ID_SERIAL=' in message:
                disk_id.append(message.split('=')[1])
    if len(disk_id) >= 1:
        disk_serial = disk_id[0]
    else:
        disk_serial = ''
    
    serial_num = cpu_serial + disk_serial
    
    # md5 加密
    import hashlib
    m = hashlib.md5()        
    m.update(serial_num.encode('utf-8'))
    return m.hexdigest().lower()
    

def get_SN():
    variable_name = 'UUID_CHITU'
    variable_value = os.getenv(variable_name)
    
    if variable_value is None:
        home_dir = os.path.expanduser('~')
        bash_profile_path = os.path.join(home_dir, '.bashrc')
        variable_value = generate_SN()
        with open(bash_profile_path, 'a') as file:
            file.write(f'\nexport {variable_name}={variable_value}\n')
        # os.system(f'source {bash_profile_path}')
    return variable_value


import socket
def get_local_ip():  
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)  
    try:
        s.connect(('*******', 1))  
        ip_address = s.getsockname()[0]  
    except Exception:  
        ip_address = '127.0.0.1'  
    finally:  
        s.close()  
    return ip_address


def send_regist_info(client, sn):
    info = {}
    info["serverType"] = '1'
    info["ip"] = get_local_ip()
    info["serialNumber"] = sn
    info["modelTypes"] = []
    info["registerTime"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    mylog.info("MQTT: 发送注册消息: {}".format(info))
    res = publish_mqtt_msg(client, regist_topic, info)
    if res == 0:
        mylog.info("MQTT: 发送注册信息成功。")
    # client.publish(regist_topic, {"plainText": str(info), "cipherText": jiami_msg(str(info))})


def get_npu_usage():
    cmd = 'echo %s | sudo -S %s' % ("apoidea", "cat /sys/kernel/debug/rknpu/load")
    npu_load = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE).stdout.decode('gbk')
    percentages = re.findall(r'(\d+\.\d+|\d+)%', npu_load)
    percentages = [float(p) for p in percentages]
    average = sum(percentages) / len(percentages)
    return format(average, ".2f")

def get_dir_size(dir):
   size = 0
   for root, dirs, files in os.walk(dir):
      size += sum([os.path.getsize(os.path.join(root, name)) for name in files])
   return int(size / 10000000) / 100


def get_disk_usage():
    output = os.popen("df -h").read()
    disk = []
    for item in output.split("\n"):
        item = item.strip()
        if item.endswith(FILE_MOUNTED_ON):
            item = item.split(" ")
            disk_tmp = []
            for n in item:
                if n.strip():
                    disk_tmp.append(n)
            disk.append(disk_tmp)

    diskSum = 0
    diskRest = 0
    for d in disk:
        if "G" in d[1]:
            diskSum = diskSum + float(d[1][:-1])
        elif "T" in d[1]:
            diskSum = diskSum + float(d[1][:-1]) * 1000
        elif "M" in d[1]:
            diskSum = diskSum + float(d[1][:-1]) / 1000
            
        if "G" in d[3]:
            diskRest = diskRest + float(d[3][:-1])
        elif "T" in d[3]:
            diskRest = diskRest + float(d[3][:-1]) * 1000
        elif "M" in d[3]:
            diskRest = diskRest + float(d[3][:-1]) / 1000
    return [format(1 - (float(diskRest) / float(diskSum)), '.2f'), int(diskRest * 100) / 100]

def send_device_usage(client, sn):
    info = {}
    info["serialNumber"] = sn
    info["cpu"] = str(psutil.cpu_percent(interval=1, percpu=False))
    info["gpu"] = ""
    info["npu"] = str(get_npu_usage())
    info["memory"] = str(psutil.virtual_memory().percent)
    disk_info = get_disk_usage()
    info["disk"] = str(disk_info[0])
    info["restCapacity"] = str(disk_info[1])
    info["usedCapacity"] = str(get_dir_size("/mnt/keep/workspace/digital_human_data/digital_human_videos"))
    # info["usedCapacity"] = str(int(get_dir_size(DIGITAL_AVATAR_VIDEOS_DIR)))
    info["abnormalAlarmType"] = []
    res = publish_mqtt_msg(client, device_usage_topic, info)
    if res == 0:
        mylog.info("MQTT: 发送资源占用信息成功。")
    
def send_device_usage_voluntarily(client, sn):
    while True:
        send_device_usage(client, sn)
        time.sleep(SEND_DEVICE_USAGE_INTERVAL)