<!doctype html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <meta charset="utf-8"></meta>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"></meta>

  <!-- Bootstrap CSS -->
  <link rel="stylesheet"
        href="./css/bootstrap.min.css"
        integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T"
        crossorigin="anonymous">
  </link>
  <link rel="icon"
      type="image/png"
      href="./k2-logo.png">

  <script src="./js/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

  <title>Next-gen <PERSON><PERSON><PERSON> demo</title>
</head>


<body>
  <div id="nav"></div>
  <script>
    $(function(){
      $("#nav").load("nav-partial.html");
    });
  </script>

  <ul class="list-unstyled">
  <li class="media">
    <div class="media-body">
      <h5 class="mt-0 mb-1">Upload</h5>
      <p>Recognition from a selected file</p>
    </div>
  <li>

  <li class="media">
    <div class="media-body">
      <h5 class="mt-0 mb-1">Streaming_Record</h5>
      <p>Recognition from real-time recordings</p>
    </div>
  </li>

  <li class="media">
    <div class="media-body">
      <h5 class="mt-0 mb-1">Offline_Record</h5>
      <p>Recognition from offline recordings</p>
    </div>
  </li>
  </ul>

  Code is available at
  <a href="https://github.com/k2-fsa/sherpa-onnx"> https://github.com/k2-fsa/sherpa-onnx</a>

  <!-- Optional JavaScript -->
  <!-- jQuery first, then Popper.js, then Bootstrap JS -->
  <script src="./js/popper.min.js"
          integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1"
          crossorigin="anonymous">
  </script>

  <script src="./js/bootstrap.min.js"
          integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM"
          crossorigin="anonymous">
  </script>

</body>
</html>
