# 人脸识别算法 v2.0.0

基于chitu-master项目的人脸识别流程，按照人员玩手机算法的标准结构实现。

## 🎯 核心流程

**完整的人脸识别流程：**

1. **图像输入** → 视频帧或图片
2. **人脸检测** → RetinaFace模型检测
3. **特征提取** → 512维特征向量
4. **数据库比对** → FAISS向量搜索
5. **结果输出** → 识别结果和置信度（与人员玩手机算法格式完全一致）

## 🏗️ 算法结构

**完全按照人员玩手机算法的结构实现：**

```
face_recognition/v2_0_0/
├── main.py                    # 算法加载入口
├── config/
│   └── documentation.json     # 算法配置文档
├── i18n/
│   ├── zh.json               # 中文国际化
│   └── en.json               # 英文国际化
├── inference/
│   ├── model_check.py        # 模型检查和加载
│   └── inference_rknn/       # RKNN推理引擎
├── preprocess/               # 预处理模块
├── postprocess/              # 后处理模块
├── task/                     # 任务管理模块
├── utils/
│   ├── face_recognition.py   # 主算法类
│   ├── frame_flow.py         # 视频流处理
│   ├── i18n.py              # 国际化工具
│   └── log.py               # 日志工具
└── weights/
    ├── face.rknn            # 人脸识别模型
    └── retinaface_mob.rknn  # 人脸检测模型
```

## 🎛️ 三种检测模式

### 1. all模式（全部）
- 检测到任何人脸都报警
- 适用于通用监控场景

### 2. authorized模式（涉政人员）
- 只有检测到已知人员才报警
- 适用于授权人员检测

### 3. unauthorized模式（非涉政人员）
- 只有检测到陌生人才报警
- 适用于入侵检测

## 📋 部署要求

### 系统要求
- Ubuntu 18.04+ 或兼容系统
- Python 3.8+
- RKNN Runtime 1.6.0+

### 依赖包
```bash
pip install opencv-python numpy faiss-cpu
```

## 🚀 部署步骤

1. **上传算法包** - 将整个face_recognition目录上传到服务器
2. **启动服务** - 运行chitu2-model-framework服务
3. **验证部署** - 检查算法状态和日志

## 📊 算法状态

- **检测模型**: retinaface_mob.rknn
- **识别模型**: face.rknn
- **人脸库**: FAISS向量索引
- **输出格式**: 与人员玩手机算法完全一致

## 🔧 配置说明

### API参数（标准格式）
- `area`: 检测区域设置（多边形）
- `interval`: 报警间隔（秒）
- `faceDetect`: 检测模式（all/authorized/unauthorized）
- `recvDataType`: 输出类型选择

## 📤 输出格式

**与人员玩手机算法完全一致的JSON格式：**

```json
{
    "modelName": "face_recognition",
    "modelVersion": "v2.0.0",
    "algoName": "face_alarm",
    "dateTime": "2025-07-18 10:30:00",
    "values": [
        {"type": "known_face", "value": "检测到已知人员"},
        {"type": "unknown_face", "value": "检测到陌生人"}
    ],
    "boxes": {
        "known_face": [[x1,y1,x2,y2,conf,name,similarity]],
        "unknown_face": [[x1,y1,x2,y2,conf,"unknown",0.0]]
    },
    "image": "base64_encoded_image",
    "rawImage": "base64_encoded_raw_image"
}
```

## 🔄 与其他算法的兼容性

### ✅ 完全兼容
- **接口一致**: 继承BasePlugin，所有方法签名相同
- **参数格式**: documentation.json格式完全一致
- **输出格式**: JSON结构与人员玩手机算法相同
- **任务管理**: start_task/stop_task/query_task/change_task
- **国际化**: i18n文件结构相同
- **目录结构**: 与detect_play_phone完全一致

### 🎯 核心特性
- **多进程推理**: 3个推理进程并行处理
- **队列管理**: 输入输出队列缓冲
- **报警间隔**: 可配置的报警间隔控制
- **区域检测**: 支持多边形区域设置
- **实时流**: 支持RTSP/USB摄像头/视频文件

## 🧪 测试验证

运行结构测试：
```bash
cd /userdata/chitu-pro/model_repository/15000/chitu2-model-framework
python3 test_face_recognition_structure.py
```

## 📝 开发说明

### 关键文件说明
- **main.py**: 标准的算法加载入口，与detect_play_phone格式一致
- **face_recognition.py**: 主算法类，继承BasePlugin
- **model_check.py**: 自动检测和加载RKNN模型
- **post_process.py**: 处理三种检测模式的报警逻辑
- **documentation.json**: 定义前端配置界面

### 与人员玩手机算法的差异
- **权重文件**: face.rknn (人脸识别) vs playphone.rknn (玩手机检测)
- **检测类别**: 人脸识别 vs 玩手机行为检测
- **业务逻辑**: 三种人脸检测模式 vs 两种玩手机检测模式
- **输出内容**: 人员身份信息 vs 行为检测结果

## 🎉 总结

现在人脸识别算法已经完全按照人员玩手机算法的结构实现，确保了：

1. **结构一致性** - 目录结构、文件命名、类继承关系完全一致
2. **接口兼容性** - 所有公共方法和参数格式完全相同
3. **功能完整性** - 保持三种检测模式和完整的人脸识别功能
4. **部署一致性** - 部署方式和配置方法与其他算法相同

这样确保了算法在系统中的无缝集成和统一管理。

### 性能优化
- 置信度阈值: 0.6（可调整到0.8-0.9减少误报）
- NMS阈值: 0.3
- 输入尺寸: 640x640

## 📝 更新日志

### v2.0.0 (2025-07-17)
- ✅ 修复RKNN推理问题
- ✅ 完善RetinaFace解码逻辑
- ✅ 优化图像预处理流程
- ✅ 添加FAISS降级机制
- ✅ 隐藏视频流不必要参数
- ✅ 完全基于chitu-master参考实现

## 🎉 验证结果

- ✅ 空白图像正确检测0个人脸
- ✅ 置信度范围正常 (0.000-0.015)
- ✅ API调用正常响应
- ✅ 报警逻辑正确工作

