 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: speech_sample_producer.py

Description: This is an check qa library Python script.

Author: songzhimeng
Creation Date: 2024.11.14
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import time
import json

from tools.message_struct import qa_empty_message_push_struct

from tools.extract_table_info import (get_sqlite_table_info,
                                      expand_lists,
                                      expand_question_lists)

async def assert_qa_data(args,client):
    while True:
        try:   
            wakeup_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['awaken_greeting'][0] 
            welcome_greeting = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_greeting'][0]
            welcome_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_video_url'][0]
            welcomeVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcomeVideoDuration'][0]
            if len(wakeup_words) != 0 and len(welcome_greeting) !=0 and len(welcome_video_url) != 0 and len(welcomeVideoDuration) != 0 :
                break
        except Exception as e:
            await client.send_message(qa_empty_message_push_struct(400,"请配置基础配置！"))
            time.sleep(5)
            continue        
    while True:
        try:
            command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
            enterPublicity = "enterPublicity"
            exitPublicity = "exitPublicity"
            enterPublicity_word = command_words_dict[enterPublicity]
            exitPublicity_word = command_words_dict[exitPublicity]
            if len(enterPublicity_word) != 0 and len(exitPublicity_word) !=0:
                break
        except Exception as e:
            await client.send_message(qa_empty_message_push_struct(400,"请配置基础配置！"))
            time.sleep(5)
            continue
    while True:
        try:
            sentences_q = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['question'].tolist()
            similarQuestionList = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['similarQuestionList'].tolist()
            sentences_q = expand_question_lists(similarQuestionList,sentences_q)
            if len(sentences_q) == 0:
                await client.send_message(qa_empty_message_push_struct(400,"请配置问答库！"))
                time.sleep(5)
                continue
            else:
                sentences_q.append(wakeup_words)    
                break
        except Exception as e:
            print(e)
            await client.send_message(qa_empty_message_push_struct(400,"请配置问答库！"))
            time.sleep(5)
            continue
    while True:
        try:
            command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
            muteAssistantMode = "muteAssistantMode"
            muteUnattendedMode = "muteUnattendedMode"
            muteAssistant_word = command_words_dict[muteAssistantMode]
            muteUnattended_word = command_words_dict[muteUnattendedMode]
            #muteAssistantModeReplay = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['muteAssistantModeReplay'][0]
            #muteUnattendedModeReplay = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['muteUnattendedModeReplay'][0]
            if len(muteAssistant_word) != 0 or len(muteUnattended_word) !=0 :
                break
        except:    
            await client.send_message(qa_empty_message_push_struct(400,"请配置基础配置！"))
            time.sleep(5)           
