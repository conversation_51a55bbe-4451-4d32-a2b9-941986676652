#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: dynamic_update_variable.py

Description: This is an dynamic update variable Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import time,json,ast
from tools.extract_table_info import (get_sqlite_table_info,expand_lists,expand_question_lists)


async def update_variable(client, args, tokenizer, model_ort): 
    global sentences_q
    global similarQuestionList
    global wakeup_words
    global welcome_greeting
    global welcome_video_url
    global welcomeVideoDuration
    global attendance_word
    global muteAssistant_word
    global muteAssistantModeReplay
    global muteUnattendedModeReplay
    global muteUnattended_word
    global bottom_video_url
    global bottom_line_reply
    global bottomVideoDuration
    global key_words
    global model_output_1   
    while True:
        if client.get_configurationModificationCode() == "1":
            sentences_q = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['question'].tolist()
            similarQuestionList = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['similarQuestionList'].tolist()
            sentences_q = expand_question_lists(similarQuestionList,sentences_q)
            wakeup_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['awaken_greeting'][0]
            sentences_q.append(wakeup_words)
            welcome_greeting = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_greeting'][0]
            welcome_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_video_url'][0]
            welcomeVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcomeVideoDuration'][0]
            command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
            attendance_messageType = "attendanceMode"
            muteAssistantMode = "muteAssistantMode"
            muteUnattendedMode = "muteUnattendedMode"
            """
            enterPublicity = "enterPublicity"
            exitPublicity = "exitPublicity"
            enterPublicity_word = command_words_dict[enterPublicity]
            exitPublicity_word = command_words_dict[exitPublicity]
            """
            attendance_word = command_words_dict[attendance_messageType]
            muteAssistant_word = command_words_dict[muteAssistantMode]
            muteAssistantModeReplay = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['muteAssistantModeReplay'][0]
            muteUnattendedModeReplay = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['muteUnattendedModeReplay'][0]
            muteUnattended_word = command_words_dict[muteUnattendedMode]

            sentences_a = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['answer'].tolist()
            sentences_a = expand_lists(similarQuestionList,sentences_a)
            qa_video_url = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['interactive_video'].tolist()
            qa_video_url = expand_lists(similarQuestionList,qa_video_url)
            qa_video_duration = get_sqlite_table_info(args.db_path, args.qa_pairs_table_name)['videoDuration'].tolist()
            qa_video_duration = expand_lists(similarQuestionList,qa_video_duration)
            sentences_a.append(welcome_greeting)
            qa_video_url.append(welcome_video_url)
            qa_video_duration.append(welcomeVideoDuration)
            bottom_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_video_url'][0]
            bottom_line_reply = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_line_reply'][0]
            bottomVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottomVideoDuration'][0]
            key_words = ast.literal_eval(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['unattendedModeKeyInfoList'].tolist()[0])
            wakeup_words = "小华小华"
            #print(wakeup_words)
            """
            del command_words_dict["attendanceMode"]
            del command_words_dict["muteAssistantMode"]
            del command_words_dict["muteUnattendedMode"]
            del command_words_dict['enterPublicity']
            del command_words_dict['exitPublicity']
            """ 
            encoded_input_1 = tokenizer(sentences_q, padding=True, truncation=True, return_tensors='pt')
            model_output_1 = model_ort(encoded_input_1["input_ids"],encoded_input_1["token_type_ids"],encoded_input_1["attention_mask"])
            await client.change_configurationModificationCode()
            #print("11111")
            
        else:

            #print(client.get_configurationModificationCode())
            #print("22222")
            time.sleep(3)
