import json
import base64
import os
from minio import Minio
import cv2
import time

try:
    from Crypto.Cipher import PKCS1_v1_5
    from Crypto import Random
    from Crypto.PublicKey import RSA
except ImportError as e:
    os.system('pip install pycryptodome')
    from Crypto.Cipher import PKCS1_v1_5
    from Crypto import Random
    from Crypto.PublicKey import RSA

from applications.mqtt.utils import MINIO_ADDRESS, MINIO_USERNAME, MINIO_PASSWORD, DIGITAL_HUMAN_VERSION_PATH
from urllib3.exceptions import MaxRetryError

SLEEPY_PICS_DIR = "/mnt/keep/workspace/digital_human_data/basicSettings/sleepy_images"
PROPAGANDA_PICS_DIR = "/mnt/keep/workspace/digital_human_data/basicSettings/propaganda_images"

if not os.path.exists(PROPAGANDA_PICS_DIR):
    os.makedirs(PROPAGANDA_PICS_DIR)
if not os.path.exists(SLEEPY_PICS_DIR):
    os.makedirs(SLEEPY_PICS_DIR)

basic_settings_config_path = "/mnt/keep/workspace/digital_human_data/basicSettings/config.json"
def read_config():
    with open(basic_settings_config_path, 'r', encoding = 'utf-8') as f:
        data = json.load(f)
    return data

def read_version_info():
    with open(DIGITAL_HUMAN_VERSION_PATH, 'r', encoding = 'utf-8') as f:
        data = json.load(f)
    return data

def create_rsa_pair(is_save=True):
    '''
    创建rsa公钥私钥对
    :param is_save: default:False
    :return: public_key, private_key
    '''
    f = RSA.generate(2048)
    private_key = f.exportKey("PEM")
    public_key = f.publickey().exportKey()
    if is_save:
        with open("/mnt/keep/workspace/digital_human_data/basicSettings/crypto_private_key.pem", "wb") as f:
            f.write(private_key)
        with open("/mnt/keep/workspace/digital_human_data/basicSettings/crypto_public_key.pem", "wb") as f:
            f.write(public_key)


def read_public_key(file_path="/mnt/keep/workspace/digital_human_data/basicSettings/crypto_public_key.pem"):
    with open(file_path, "rb") as x:
        b = x.read()
        return b


def read_private_key(file_path="/mnt/keep/workspace/digital_human_data/basicSettings/crypto_private_key.pem"):
    with open(file_path, "rb") as x:
        b = x.read()
        return b


def rsa_encryption(text, public_key):
    text = text.encode('utf-8')
    cipher_public = PKCS1_v1_5.new(RSA.importKey(public_key))
    text_encrypted = cipher_public.encrypt(text)
    text_encrypted_base64 = base64.b64encode(text_encrypted).decode()
    return text_encrypted_base64


def rsa_decryption(text_encrypted_base64, private_key):
    text_encrypted_base64 = text_encrypted_base64.encode('utf-8')
    text_encrypted = base64.b64decode(text_encrypted_base64)
    cipher_private = PKCS1_v1_5.new(RSA.importKey(private_key))
    text_decrypted = cipher_private.decrypt(text_encrypted, Random.new().read)
    text_decrypted = text_decrypted.decode()
    return text_decrypted


WAKE_INTERVAL = read_config()["wakeInterval"] * 60
def update_wake_interval(interval):
    global WAKE_INTERVAL
    conf = read_config()
    conf["wakeInterval"] = int(interval)
    with open(basic_settings_config_path, 'w') as write_f:
        json.dump(conf, write_f, indent=4, ensure_ascii=False)
    WAKE_INTERVAL = int(interval) * 60


minioClient = Minio(MINIO_ADDRESS, access_key=MINIO_USERNAME, secret_key=MINIO_PASSWORD, secure=False)
def getMinioPicUrl(frame, sn):
    dir_name_year = time.strftime("%Y", time.localtime())
    dir_name_month = time.strftime("%m", time.localtime())
    dir_name_day = time.strftime("%d", time.localtime())
    
    img_dir = "/mnt/keep/workspace/digital_human_data/face_info/stranger_images/"
    if not os.path.exists(os.path.join(img_dir)):
        os.makedirs(os.path.join(img_dir))
    file_name = "stranger_"  + sn + '_' + time.strftime('%Y%m%d%H%M%S', time.localtime()) + '_' + str(time.time())
    cv2.imwrite(img_dir + file_name + ".jpg", frame)
    try:
        minioClient.fput_object("huayuan-video-platform", dir_name_year + "/" + dir_name_month + "/" + dir_name_day + "/" + file_name + ".jpg", img_dir + file_name + ".jpg", content_type="jpg") 
    except MaxRetryError as err:
        return 1, None
    os.remove(img_dir + file_name + ".jpg")
    picurl = "/huayuan-video-platform/" + dir_name_year + "/" + dir_name_month + "/" + dir_name_day + "/" + file_name + ".jpg"
    return 0, picurl