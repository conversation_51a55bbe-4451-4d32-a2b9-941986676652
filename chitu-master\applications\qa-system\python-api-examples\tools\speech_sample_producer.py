#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: speech_sample_producer.py

Description: This is an speech sample producer Python script.

Author: songzhimeng
Creation Date: 2024.11.14
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""
import sounddevice as sd
import time
#拾音放到队列里
def producer_double(s,samples_per_read,samples_queue): 
    while True:
        samples, _ = s.read(samples_per_read)
        samples = samples[:,0]
        samples = samples.reshape(-1)
        samples_queue.put(samples)

def producer_single(s,samples_per_read,samples_queue):
    while True:
        samples, _ = s.read(samples_per_read)
        #print("sample_1: ",samples)
        samples = samples.reshape(-1)
        samples_queue.put(samples)
"""
def producer_general(s,samples_per_read,samples_queue,channels):
    if channels == 1:
        while True:
            samples, _ = s.read(samples_per_read)
            #print("sample_1: ",samples)
            samples = samples.reshape(-1)
            samples_queue.put(samples)  
    elif channels == 2:    
        while True:
            samples, _ = s.read(samples_per_read)
            samples = samples[:,0]
            samples = samples.reshape(-1)
            samples_queue.put(samples)
    else:
        pass        
"""
#开启麦克风获取采样点
def producer_general(logger,samples_queue):
    while True:
        try:
            devices = sd.query_devices()
            logger.log_message('info',devices)
            if len(devices) == 0:
                print("No microphone devices found")
            for device in devices:
                if "usb" in device['name'].lower() and ("tina" in device['name'].lower() or "audio" in device['name'].lower()):
                    print(device['max_input_channels'],device['default_samplerate'])
                    break
            sd.default.device[0] = device['index']
            #sample_rate = device['default_samplerate']
            sample_rate = 16000
            channels = device['max_input_channels']
            default_input_device_idx = sd.default.device[0]
            samples_per_read = int(sample_rate * 0.1)#100ms
            print(f'Use default device: {devices[default_input_device_idx]["name"]}')
            logger.log_message('info', '麦克风id设置完成。')
            break
        except:
            logger.log_message('error', '麦克风id设置失败。')
    try:    
        with sd.InputStream(channels=channels, dtype="float32", samplerate=sample_rate) as s: 
            logger.log_message('info', '麦克风打开成功。')
            while not samples_queue.empty():
                samples_queue.get()         
            if channels == 1:
                while True:
                    samples, _ = s.read(samples_per_read)
                    #print(samples)
                    samples = samples.reshape(-1)
                    samples_queue.put(samples)  
            elif channels == 2:    
                while True:
                    samples, _ = s.read(samples_per_read)
                    samples = samples[:,0]
                    samples = samples.reshape(-1)
                    samples_queue.put(samples)
            else:
                pass    
    except Exception as e:
        logger.log_message('error','麦克风拾音失败。')            
