# 模型权重文件

本目录存放人脸识别算法所需的模型权重文件。

## 必需的模型文件

### 1. retinaface_mob.rknn
- **功能**: 人脸检测模型
- **描述**: 基于RetinaFace的移动端优化版本，用于检测图像中的人脸
- **输入**: 640x640x3 RGB图像
- **输出**: 人脸边界框和关键点坐标
- **大小**: 约 2-5MB

### 2. face.rknn  
- **功能**: 人脸识别模型
- **描述**: 用于提取人脸特征向量，进行身份识别
- **输入**: 112x112x3 RGB人脸图像
- **输出**: 512维特征向量
- **大小**: 约 3-8MB

### 3. people.rknn (可选)
- **功能**: 人体检测模型
- **描述**: 用于检测图像中的人体，辅助人脸识别
- **输入**: 640x640x3 RGB图像
- **输出**: 人体边界框
- **大小**: 约 5-15MB

## 模型格式支持

当前版本支持以下模型格式：

- **RKNN** (.rknn) - 瑞芯微NPU优化格式 (推荐)
- **PyTorch** (.pt, .pth) - PyTorch原生格式
- **ONNX** (.onnx) - 跨平台标准格式
- **TensorRT** (.engine) - NVIDIA GPU优化格式

## 模型获取

### 方法1: 从原有系统复制
```bash
# 从face_recognition_module复制模型文件
cp face_recognition_module/weights/*.rknn face_recognition_v2/v2_0_0/weights/
```

### 方法2: 模型转换
如果您有其他格式的模型，可以使用相应工具进行转换：

```bash
# PyTorch转RKNN (需要RKNN-Toolkit2)
python convert_pytorch_to_rknn.py --input model.pth --output model.rknn

# ONNX转RKNN
python convert_onnx_to_rknn.py --input model.onnx --output model.rknn
```

## 模型验证

使用以下命令验证模型文件：

```bash
# 检查模型文件完整性
python -c "
import os
weights_dir = 'face_recognition_v2/v2_0_0/weights'
required_files = ['retinaface_mob.rknn', 'face.rknn']
for file in required_files:
    path = os.path.join(weights_dir, file)
    if os.path.exists(path):
        size = os.path.getsize(path) / (1024*1024)
        print(f'✓ {file}: {size:.1f}MB')
    else:
        print(f'✗ {file}: 文件不存在')
"
```

## 性能参数

### RetinaFace检测模型
- **精度**: mAP > 95%
- **速度**: ~20ms (RK3588)
- **内存**: ~200MB

### 人脸识别模型  
- **精度**: 准确率 > 99.5%
- **速度**: ~10ms (RK3588)
- **内存**: ~100MB

## 注意事项

1. **文件权限**: 确保模型文件具有读取权限
2. **存储空间**: 预留至少100MB存储空间
3. **版本兼容**: 使用与当前算法版本匹配的模型
4. **硬件要求**: RKNN模型需要瑞芯微NPU支持

## 故障排除

### 模型加载失败
```
RuntimeError: Failed to load model
```
**解决方案**:
- 检查文件是否存在
- 验证文件完整性
- 确认硬件兼容性

### 内存不足
```
RuntimeError: Cannot allocate memory
```
**解决方案**:
- 检查可用内存
- 减少并发推理数量
- 使用更小的模型

### 推理速度慢
**优化建议**:
- 使用RKNN格式模型
- 启用NPU加速
- 调整输入图像尺寸

## 更新日志

- **v2.0.0**: 支持多推理引擎，优化模型加载机制
- **v1.0.0**: 初始版本，支持RKNN模型
