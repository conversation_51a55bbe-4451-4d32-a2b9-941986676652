from applications.extensions import ma
from marshmallow import fields


class digital_human_interactiveOutSchema(ma.<PERSON>):
    id = fields.Integer()
    question = fields.Str()
    answer = fields.Str()
    interactive_video = fields.Str()
    questionId = fields.Str()
    enabled = fields.Str()
    similarQuestionList = fields.Str()
    videoDuration = fields.Str()
    fileType = fields.Str()
    fileUrl = fields.Str()
    # successList = fields.Str()
    # errorList = fields.Str()