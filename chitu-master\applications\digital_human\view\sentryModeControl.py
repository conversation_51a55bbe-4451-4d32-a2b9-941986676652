import schedule
from datetime import datetime
from log import mylog
from applications.digital_human.common.common_utils import read_config
from applications.digital_human.view.enter_sentry_mode import enter_sentry_mode, exit_sentry_mode
from applications.digital_human.common.utils.mode_change import enter_assistant_mode, enter_unattended_mode, enter_propagate_mode

# 读取哨兵模式的时间
def get_sentry_time():
    conf = read_config()
    status = conf.get("sentryModeStatus", "0")
    if status == "1":
        '''
        结果说明：
            第一个字段：
                0： 开启和关闭时间间隔小于0分钟；
                1： 哨兵模式开启和关闭时间间隔大于24小时，将无法切换至其它模式；
                2： 开启时间和关闭时间正常
            第二个字段：
                True：需要立马进入哨兵模式
                False：不需要立马进入哨兵模式
            第三/四个字段：进入/退出哨兵模式的时间
        '''
        start_time = conf["sentryModeStartTime"]
        stop_time = conf["sentryModeStopTime"]
        if start_time == stop_time:
            return 0, False, None, None
        
        [start_time_hour, start_time_minite] = start_time.split(":")
        [stop_time_hour, stop_time_minite] = stop_time.split(":")
        
        new_stop_time_hour = stop_time_hour if int(stop_time_hour) < 24 else int(stop_time_hour) - 24
        new_stop_time = str(new_stop_time_hour).rjust(2, '0') + ":" + stop_time_minite.rjust(2, '0')
        new_start_time = start_time_hour.rjust(2, '0') + ":" + start_time_minite.rjust(2, '0')
        
        __start = datetime.strptime(new_start_time, "%H:%M").time()
        __end = datetime.strptime(new_stop_time, "%H:%M").time()
        flag = False
        cur_time = datetime.now().time()
        if cur_time >= __start:
            if int(stop_time_hour) >= 24 or (int(stop_time_hour) < 24 and cur_time < __end):
                flag = True

        if start_time_hour > stop_time_hour:
            return 0, False, None, None
        elif start_time_hour == stop_time_hour:
            if start_time_minite > stop_time_minite:
                return 0, False, None, None
            elif start_time_minite < stop_time_minite:
                return 2, flag, new_start_time, new_stop_time
        else:
            if int(stop_time_hour) - int(start_time_hour) > 24:
                return 1, flag, new_start_time, None
            elif int(stop_time_hour) - int(start_time_hour) == 24:
                if int(start_time_minite) < int(stop_time_minite):
                    return 1, flag, new_start_time, None
                elif start_time_minite == stop_time_minite:
                    return 1, flag, new_start_time, None
                else:
                    return 2, flag, new_start_time, new_stop_time
            else:
                return 2, flag, new_start_time, new_stop_time
    elif status == "0":
        '''
        结果说明：
            返回的第一个值： -1：哨兵模式关闭
            返回的第二个值：True/False：当前处于/不处于哨兵模式
        '''
        if conf.get("currentMode") == "SentryMode":
            return -1, True, None, None
        else:
            return -1, False, None, None
    else:
        return -2, None, None, None


def update_sentry_schedule(app):
    try:
        schedule.clear("sentry_schedule_start")
        schedule.clear("sentry_schedule_stop")
    except Exception:
        pass
        
    status, flag, start_time, stop_time = get_sentry_time()
    if status == -1:
        if flag:
            exit_sentry_mode()
    elif status == 2:
        if flag:
            enter_sentry_mode()
        schedule.every().day.at(start_time).do(enter_sentry_mode).tag("sentry_schedule_start")
        schedule.every().day.at(stop_time).do(exit_sentry_mode).tag("sentry_schedule_stop")
    elif status == 1:
        if flag:
            enter_sentry_mode()
        schedule.every().day.at(start_time).do(enter_sentry_mode, None, True).tag("sentry_schedule_start")
        mylog.info("哨兵模式开启, 但是开启和关闭时间间隔大于24小时，将无法切换至其它模式。")
    elif status == 0:
        mylog.info("哨兵模式开启失败, 开启和关闭时间间隔小于0小时。")
    else:
        mylog.info("哨兵模式开启失败, 哨兵模式状态值不正确。")


# 初始化哨兵模式    
def init_mode(app):
    status, flag, start_time, stop_time = get_sentry_time()
    if status == -1:
        if flag:
            exit_sentry_mode()
    elif status == 2:
        if flag:
            enter_sentry_mode()
        schedule.every().day.at(start_time).do(enter_sentry_mode).tag("sentry_schedule_start")
        schedule.every().day.at(stop_time).do(exit_sentry_mode).tag("sentry_schedule_stop")
    elif status == 1:
        if flag:
            enter_sentry_mode()
        schedule.every().day.at(start_time).do(enter_sentry_mode, None, True).tag("sentry_schedule_start")
        mylog.info("哨兵模式开启, 但是开启和关闭时间间隔大于24小时，将无法切换至其它模式。")
    elif status == 0:
        mylog.info("哨兵模式开启失败, 开启和关闭时间间隔小于0小时。")
    else:
        mylog.info("哨兵模式开启失败, 哨兵模式状态值不正确。")

# from datetime import datetime
# def is_time_in_range():
#     now = datetime.now().time()
#     status, start_time, stop_time = get_sentry_time()

#     start = datetime.strptime(start_time, "%H:%M").time()
#     end = datetime.strptime(stop_time, "%H:%M").time()
    
#     # 判断时间是否在范围内
#     if start <= end:
#         return start <= now <= end
#     else:
#         return now >= start or now <= end


conf = read_config()
status = conf["currentStatus"]
if "propagate" in status:
    enter_propagate_mode(need_update_file=False)
mode = conf["currentMode"]
if mode == "SentryMode":
    enter_sentry_mode()
elif mode == "AssistantMode":
    _ = enter_assistant_mode(need_update_file=False)
elif mode == "UnattendedMode":
    _ = enter_unattended_mode(need_update_file=False)
else:
    pass