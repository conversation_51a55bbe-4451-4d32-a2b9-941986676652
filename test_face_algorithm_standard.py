#!/usr/bin/env python3
"""
基于detect_gloves标准框架重构后的人脸算法测试脚本
验证重构后的算法是否符合标准框架规范
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
from pathlib import Path

# 添加算法路径
sys.path.append('detect_face_alarm/v2_0_0')
sys.path.append('.')

# 设置环境变量避免导入问题
os.environ['PYTHONPATH'] = os.pathsep.join([
    'detect_face_alarm/v2_0_0',
    '.',
    os.environ.get('PYTHONPATH', '')
])

def create_test_image(width=640, height=480):
    """创建测试图像"""
    # 创建一个简单的测试图像
    img = np.ones((height, width, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 绘制一些简单的形状作为"人脸"
    cv2.rectangle(img, (100, 100), (200, 200), (255, 255, 255), -1)  # 白色矩形
    cv2.rectangle(img, (400, 200), (500, 300), (255, 255, 255), -1)  # 另一个白色矩形
    
    # 添加一些细节
    cv2.circle(img, (150, 150), 20, (0, 0, 0), -1)  # 黑色圆圈
    cv2.circle(img, (450, 250), 20, (0, 0, 0), -1)  # 黑色圆圈
    
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    return img_base64

def test_standard_framework_compliance():
    """测试标准框架合规性"""
    print("=" * 60)
    print("测试1: 标准框架合规性检查")
    print("=" * 60)

    try:
        # 检查主类结构
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        from utils.face_recognition import DetectFaceAlarm

        # 检查是否有必要的方法
        required_methods = ['infer', 'start_task', 'stop_task', 'get_task_list']

        # 不实际创建实例，只检查类是否存在
        print(f"✅ DetectFaceAlarm 类导入成功")

        # 检查方法是否存在
        for method in required_methods:
            if hasattr(DetectFaceAlarm, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 缺失")
                return False

        print("✅ 主类结构符合标准框架")
        return True

    except Exception as e:
        print(f"❌ 标准框架合规性检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preprocessing_standard():
    """测试预处理标准化"""
    print("\n" + "=" * 60)
    print("测试2: 预处理标准化")
    print("=" * 60)

    try:
        # 检查预处理文件是否存在
        preprocess_files = [
            'detect_face_alarm/v2_0_0/preprocess/detect_preprocess.py',
            'detect_face_alarm/v2_0_0/preprocess/utils/preprocess.py',
            'detect_face_alarm/v2_0_0/preprocess/utils/utils.py'
        ]

        for file_path in preprocess_files:
            if Path(file_path).exists():
                print(f"✅ 文件存在: {file_path}")
            else:
                print(f"❌ 文件缺失: {file_path}")
                return False

        # 尝试导入预处理模块
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        from preprocess.detect_preprocess import DetectPreProcess

        print(f"✅ DetectPreProcess 类导入成功")

        # 检查必要的方法
        if hasattr(DetectPreProcess, 'get_data'):
            print("✅ get_data 方法存在")
        else:
            print("❌ get_data 方法缺失")
            return False

        print("✅ 预处理模块结构正确")
        return True

    except Exception as e:
        print(f"❌ 预处理标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inference_standard():
    """测试推理标准化"""
    print("\n" + "=" * 60)
    print("测试3: 推理标准化")
    print("=" * 60)

    try:
        # 检查推理文件是否存在
        inference_files = [
            'detect_face_alarm/v2_0_0/inference/inference_rknn/inference_rknn.py',
            'detect_face_alarm/v2_0_0/inference/inference_rknn/utils_rknn/utils.py'
        ]

        for file_path in inference_files:
            if Path(file_path).exists():
                print(f"✅ 文件存在: {file_path}")
            else:
                print(f"❌ 文件缺失: {file_path}")
                return False

        # 检查utils.py文件内容
        utils_path = Path('detect_face_alarm/v2_0_0/inference/inference_rknn/utils_rknn/utils.py')
        if utils_path.exists():
            with open(utils_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'def predict(' in content and 'def get_draw_box(' in content:
                    print("✅ 标准工具函数存在")
                else:
                    print("❌ 标准工具函数缺失")
                    return False

        print("✅ 推理模块结构正确")
        return True

    except Exception as e:
        print(f"❌ 推理标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_postprocessing_standard():
    """测试后处理标准化"""
    print("\n" + "=" * 60)
    print("测试4: 后处理标准化")
    print("=" * 60)

    try:
        # 检查后处理文件是否存在
        postprocess_files = [
            'detect_face_alarm/v2_0_0/postprocess/post_process.py',
            'detect_face_alarm/v2_0_0/postprocess/utils/boxes_iou.py',
            'detect_face_alarm/v2_0_0/postprocess/utils/utils.py'
        ]

        for file_path in postprocess_files:
            if Path(file_path).exists():
                print(f"✅ 文件存在: {file_path}")
            else:
                print(f"❌ 文件缺失: {file_path}")
                return False

        # 检查后处理文件内容
        postprocess_path = Path('detect_face_alarm/v2_0_0/postprocess/post_process.py')
        if postprocess_path.exists():
            with open(postprocess_path, 'r', encoding='utf-8') as f:
                content = f.read()
                required_methods = ['disassemble_recv_data_type', 'disassemble_algo_type',
                                  'disassemble_advset_value', 'run_process']

                for method in required_methods:
                    if f'def {method}(' in content:
                        print(f"✅ 方法 {method} 存在")
                    else:
                        print(f"❌ 方法 {method} 缺失")
                        return False

        print("✅ 后处理模块结构正确")
        return True

    except Exception as e:
        print(f"❌ 后处理标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utils_standard():
    """测试工具函数标准化"""
    print("\n" + "=" * 60)
    print("测试5: 工具函数标准化")
    print("=" * 60)

    try:
        # 检查utils.py文件内容
        utils_path = Path('detect_face_alarm/v2_0_0/inference/inference_rknn/utils_rknn/utils.py')
        if not utils_path.exists():
            print("❌ utils.py文件不存在")
            return False

        with open(utils_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查关键函数
        required_functions = ['predict', 'get_draw_box', 'load_model', 'xywh2xyxy', 'nms_boxes']

        for func in required_functions:
            if f'def {func}(' in content:
                print(f"✅ 函数 {func} 存在")
            else:
                print(f"❌ 函数 {func} 缺失")
                return False

        # 测试简单的坐标转换逻辑
        if 'get_draw_box' in content and 'gain[0]' in content:
            print("✅ 坐标转换逻辑正确")
        else:
            print("❌ 坐标转换逻辑有问题")
            return False

        print("✅ 工具函数结构正确")
        return True

    except Exception as e:
        print(f"❌ 工具函数标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_standard():
    """测试配置文件标准化"""
    print("\n" + "=" * 60)
    print("测试6: 配置文件标准化")
    print("=" * 60)
    
    try:
        config_path = Path("detect_face_alarm/v2_0_0/config/documentation.json")
        
        if not config_path.exists():
            print("❌ 配置文件不存在")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = ['modelName', 'modelVersion', 'algoSet']
        for key in required_keys:
            if key in config:
                print(f"✅ 配置项 {key} 存在")
            else:
                print(f"❌ 配置项 {key} 缺失")
                return False
        
        # 检查算法配置
        if len(config['algoSet']) > 0:
            algo = config['algoSet'][0]
            if 'faceDetect' in str(algo):
                print("✅ 人脸检测配置存在")
            else:
                print("❌ 人脸检测配置缺失")
                return False
        
        print("✅ 配置文件符合标准")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 基于detect_gloves标准框架的人脸算法测试")
    print("=" * 60)
    
    # 检查算法文件是否存在
    algo_path = Path("detect_face_alarm/v2_0_0")
    if not algo_path.exists():
        print("❌ 算法路径不存在，请确保在正确的目录下运行测试")
        return
    
    print(f"✅ 算法路径存在: {algo_path.absolute()}")
    
    # 运行各项测试
    tests = [
        ("标准框架合规性", test_standard_framework_compliance),
        ("预处理标准化", test_preprocessing_standard),
        ("推理标准化", test_inference_standard),
        ("后处理标准化", test_postprocessing_standard),
        ("工具函数标准化", test_utils_standard),
        ("配置文件标准化", test_configuration_standard),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！算法已成功标准化")
        print("📋 重构完成的内容:")
        print("  ✅ 主类结构与detect_gloves完全一致")
        print("  ✅ 预处理流程标准化")
        print("  ✅ 推理引擎使用标准predict和get_draw_box函数")
        print("  ✅ 后处理支持区域检测和多种检测模式")
        print("  ✅ 工具函数与标准框架一致")
        print("  ✅ 配置文件适配人脸检测需求")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
