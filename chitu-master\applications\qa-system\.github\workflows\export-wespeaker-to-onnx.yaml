name: export-wespeaker-to-onnx

on:
  workflow_dispatch:

concurrency:
  group: export-wespeaker-to-onnx-${{ github.ref }}
  cancel-in-progress: true

jobs:
  export-wespeaker-to-onnx:
    if: github.repository_owner == 'k2-fsa' || github.repository_owner == 'csukuangfj'
    name: export wespeaker
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        python-version: ["3.8"]

    steps:
      - uses: actions/checkout@v4

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Python dependencies
        shell: bash
        run: |
          pip install kaldi-native-fbank numpy onnx onnxruntime

      - name: Run
        shell: bash
        run: |
          cd scripts/wespeaker
          ./run.sh

          mv -v *.onnx ../..

      - name: Release
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          file: ./*.onnx
          overwrite: true
          repo_name: k2-fsa/sherpa-onnx
          repo_token: ${{ secrets.UPLOAD_GH_SHERPA_ONNX_TOKEN }}
          tag: speaker-recongition-models
