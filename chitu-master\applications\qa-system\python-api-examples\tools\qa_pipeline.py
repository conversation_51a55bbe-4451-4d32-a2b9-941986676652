import random
import logging
import time
import numpy as np
import multiprocessing
import re,json,ast

from tools.extract_table_info import (get_sqlite_table_info,
                                      expand_lists,
                                      expand_question_lists)
from tools.message_struct import (answer_push_json_message_struct,
                                  question_push_json_message_struct,
                                  command_word_push_json_message_struct,
                                  qa_empty_message_push_struct,
                                  face_exit_wake_up_push_struct,
                                  start_qa_service_push_struct,
                                  key_word_push_struct,
                                  restart_qa_service_push_struct,
                                  exit_attendance_mode_push_struct,
                                  question_status_push_struct,
                                  question_guide_push_struct)
from tools.speech_sample_producer import producer_general


#async def rec_pipeline(recognizer, model_ort, tokenizer, model_output_1, client, args, samples_queue,logger,Global,GlobalVarsUpdate):
async def rec_pipeline(recognizer, model_ort, tokenizer, client, args, samples_queue,logger, GlobalVarsUpdate):
    #创建识别音频输入流
    sample_rate = 16000
    samples_per_read = int(0.1 * sample_rate)  # 0.1 second = 100 ms
    stream = recognizer.create_stream()
    #从数据库中读取问答对、兜底话术、敏感词、命令词配置
    try:  
        sentence_embeddings_1 = GlobalVarsUpdate.sentence_embeddings_1
        sentences_q = GlobalVarsUpdate.sentences_q
        sentences_a = GlobalVarsUpdate.sentences_a
        qa_video_url = GlobalVarsUpdate.qa_video_url
        qa_ids = GlobalVarsUpdate.qa_ids
        qa_video_duration = GlobalVarsUpdate.qa_video_duration
        fileTypeList = GlobalVarsUpdate.fileTypeList
        fileUrlList = GlobalVarsUpdate.fileUrlList
        command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
        """
        bottom_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_video_url'][0]
        bottom_line_reply = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_line_reply'][0]
        bottomVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottomVideoDuration'][0]
        """
        #bottom_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_video_url'].tolist()[0]
        bottom_line_list = ast.literal_eval(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottom_line_list'].tolist()[0])
        print(bottom_line_list)
        #bottomVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['bottomVideoDuration'].tolist()[0]
        attendance_word = command_words_dict["attendanceMode"]
        exit_attendance_word = command_words_dict["exitAttendanceMode"]
        muteAssistant_word = command_words_dict["muteAssistantMode"]
        muteUnattended_word = command_words_dict["muteUnattendedMode"]
        enterPublicity_word = command_words_dict["enterPublicity"]
        exitPublicity_word = command_words_dict["exitPublicity"]
      
        del command_words_dict["attendanceMode"]
        del command_words_dict["exitAttendanceMode"]
        del command_words_dict["muteAssistantMode"]
        del command_words_dict["muteUnattendedMode"]
        del command_words_dict["enterPublicity"]
        del command_words_dict["exitPublicity"]
     
        logger.log_message('info', '从数据库中读取问答对、兜底话术、命令词配置成功。')
    except:
        logger.log_message('error', '从数据库中读取问答对、兜底话术、命令词配置失败。')
    try:
        sensitive_words_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words_video_url'].tolist()[0]
        sensitive_words_reply = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words_reply'].tolist()[0]
        sensitiveWordsVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitiveWordsVideoDuration'][0]
        sensitive_words = ast.literal_eval(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['sensitive_words'].tolist()[0])
        logger.log_message('info', '从数据库中读取敏感词配置成功。')
    except:
        sensitive_words = ""
        logger.log_message('error', '从数据库中读取敏感词配置失败。')
    try:
        key_words = ast.literal_eval(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['unattendedModeKeyInfoList'].tolist()[0])
        logger.log_message('info', '从数据库中读取关键词配置成功。')
    except:
        key_words = ""
        logger.log_message('error', '从数据库中读取关键词配置失败。')        
    last_result = ""
    segment_id = 0
    #sentence_embeddings_1 = model_output_1[0][:, 0]
    rec_time = time.time()
    init_array = np.array([])
    #多轮对话循环
    bottom_count = 1
    while True:
        if client.get_status() == "1":    
            try:
                producer_process.terminate()
                producer_process.join()
            except Exception as e:
                pass
            producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
            producer_process.start()
            sample_time = time.time()
            while True:
                if client.get_operationsStatus() == "1":
                    try:
                        producer_process.terminate()
                        producer_process.join()
                    except Exception as e:
                        pass
                    await client.change_status_pick()
                    return
                else:
                    pass
                if samples_queue.empty():
                    no_sample_dur = time.time() - sample_time
                    if no_sample_dur > 3:
                        try:
                            if producer_process.is_alive():
                                producer_process.terminate()
                                producer_process.join()
                        except Exception as e:
                            pass        
                        await client.change_status_pick()
                        await client.send_message(restart_qa_service_push_struct())
                        return
                    else:    
                        continue
                else:
                    sample_time = time.time()
                    samples = samples_queue.get()
                stream.accept_waveform(sample_rate, samples)
                while recognizer.is_ready(stream):
                    recognizer.decode_stream(stream)
                is_endpoint = recognizer.is_endpoint(stream)
                result = recognizer.get_result(stream)
                
                if result and (last_result != result):
                    result = re.sub(r'[A-Z]', '', result)
                    last_result = result
                    if client.get_propaganda_status() != "1" and client.get_attendanceStatus() != "1":
                        if result and (1 < len(result)):
                            await client.send_message(question_push_json_message_struct(result))
                            print("\r{}:{}".format(segment_id, result), end="", flush=True)
                    else:
                        pass
                if is_endpoint:
                    #print("\r{}:{}".format(segment_id, result), flush=True)
                    if result and (1 < len(result) < 21):
                        if producer_process.is_alive():
                            producer_process.terminate()
                            producer_process.join()
                        rec_time = time.time()
                        found = False
                        #await client.send_message(question_status_push_struct("questionPushEnd"))
                        segment_id += 1
                        recognizer.reset(stream)
                        if client.get_propaganda_status() != "1" and client.get_attendanceStatus() != "1":                 
                            #敏感词回复
                            if key_words == "":
                                pass
                            else:
                                for word in key_words:
                                    if word in result:
                                        await client.send_message(question_status_push_struct("questionPushEnd"))
                                        await client.send_message(key_word_push_struct(result))
                            if sensitive_words=="":
                                pass
                            else:
                                for word in sensitive_words:
                                    if word in result:
                                        found = True
                                        try:
                                            await client.send_message(question_push_json_message_struct(result.replace(word,"*"*len(word))))
                                            await client.send_message(question_status_push_struct("questionPushEnd"))
                                            await client.send_message(answer_push_json_message_struct(sensitive_words_video_url,sensitive_words_reply, sensitiveWordsVideoDuration,"",""))
                                            rec_time = time.time() + float(sensitiveWordsVideoDuration) / 1000
                                            await client.change_status()
                                            logger.log_message('info', '敏感词回复成功。')
                                            break
                                        except:
                                            logger.log_message('error', '敏感词回复失败。')                
                            if found:
                                pass
                            else:
                                #命令词回复
                                for messageType,word in command_words_dict.items():
                                    if word in result:
                                        found = True
                                        try:
                                            await client.send_message(command_word_push_json_message_struct(messageType,result,word))
                                            await client.send_message(question_status_push_struct("questionPushEnd"))
                                            await client.send_message(qa_empty_message_push_struct(200,word))
                                            logger.log_message('info', '命令词回复成功。')
                                            break
                                        except:
                                            #send_alarm_message('命令词回复失败。')
                                            logger.log_message('error', '命令词回复失败。')

                            if found:
                                pass
                            else:
                                if muteAssistant_word in result:
                                    found = True
                                    try:
                                        await client.send_message(question_status_push_struct("questionPushEnd"))
                                        await client.send_message(qa_empty_message_push_struct(200,"我已进入助理模式。",2))
                                        await client.send_message(command_word_push_json_message_struct("muteAssistantMode",result,muteAssistant_word))
                                        logger.log_message('info', '切换助理模式成功。')
                                    except:
                                        logger.log_message('error', '切换助理模式失败。')
                                elif muteUnattended_word in result:
                                    found = True
                                    try:
                                        await client.send_message(question_status_push_struct("questionPushEnd"))
                                        await client.send_message(qa_empty_message_push_struct(200,"我已进入无人值守模式。",1))
                                        await client.send_message(command_word_push_json_message_struct("muteUnattendedMode",result,muteUnattended_word))
                                        logger.log_message('info', '切换无人值守模式成功。')
                                    except:
                                        logger.log_message('error', '切换无人值守模式失败。')
                                elif attendance_word in result:
                                    found = True
                                    try:
                                        await client.send_message(question_status_push_struct("questionPushEnd"))
                                        await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                                        await client.send_message(command_word_push_json_message_struct("attendanceMode", result, attendance_word))
                                        await client.change_enterAttendanceStatus()
                                        logger.log_message('info', '往赤兔发送打卡命令词发送成功。')
                                    except:
                                        logger.log_message('error', '往赤兔发送打卡命令词发送失败。')
                                elif exit_attendance_word in result:
                                    found = True
                                    await client.send_message(question_status_push_struct("questionPushEnd"))
                                    await client.send_message(qa_empty_message_push_struct(200,"已退出打卡模式"))
                                    await client.send_message(exit_attendance_mode_push_struct())
                                    await client.send_message(command_word_push_json_message_struct("exitAttendanceMode",result,exit_attendance_word))
                                    await client.change_exitAttendanceStatus()  
                            if found:
                                pass
                            else:            
                                if enterPublicity_word in result:
                                    await client.send_message(question_status_push_struct("questionPushEnd"))
                                    await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))                                
                                    await client.change_enter_propaganda_status()
                                    return
                                elif exitPublicity_word in result:
                                    await client.send_message(question_status_push_struct("questionPushEnd"))
                                    await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
                                    await client.change_exit_propaganda_status()
                                    found = True            

                            if found:
                                pass
                            else:
                                #用户问答对相似度匹配
                                try:
                                    sentences_2 = [result]
                                    encoded_input_2 = tokenizer(sentences_2, padding=False, truncation=True, return_tensors='pt')
                                    model_output_2 = model_ort(encoded_input_2["input_ids"],encoded_input_2["token_type_ids"],encoded_input_2["attention_mask"])
                                    # Perform pooling. In this case, cls pooling.
                                    sentence_embeddings_2 = model_output_2[0][:, 0]
                                    scores = sentence_embeddings_1 @ sentence_embeddings_2.T
                                    max_value = max(scores.view(-1).tolist())
                                    logger.log_message('info', '用户问题 bge embedding 成功。')
                                except:
                                    logger.log_message('error', '用户问题 bge embedding 失败。')

                                if max_value > 30:
                                    await client.send_message(question_status_push_struct("questionPushEnd"))
                                    try:
                                        max_index = max(enumerate(scores), key=lambda x: x[1][0])[0]
                                        #await client.send_message(question_push_json_message_struct(result))
                                        await client.send_message(answer_push_json_message_struct(qa_video_url[max_index], sentences_a[max_index], qa_video_duration[max_index],fileTypeList[max_index],fileUrlList[max_index],answerId=qa_ids[max_index]))
                                        rec_time = time.time() + float(qa_video_duration[max_index]) / 1000
                                        await client.change_status()
                                        logger.log_message('info', '智能问答回复成功。')
                                    except:
                                        logger.log_message('error', '智能问答回复失败。')
                                else:
                                    await client.send_message(question_status_push_struct("questionPushEnd"))
                                    #兜底话术回复 
                                    if len(scores) > 3:
                                        sorted_index = sorted(range(len(scores)), key=lambda k: scores[k], reverse=True)[:3]
                                        questionGuideList = [sentences_q[i] for i in sorted_index]
                                    else:
                                        questionGuideList = [sentences_q[i] for i in range(len(scores))]
                                    bottom_index = random.randint(0,len(bottom_line_list)-1)  
                                    #bottomVideoDuration =  bottom_line_list[bottom_index]["bottomVideoDuration"]
                                    #print(bottomVideoDuration)
                                    print(bottom_line_list[bottom_index])
                                    bottom_video_url = bottom_line_list[bottom_index]["bottomLineVideoUrl"]
                                    bottom_line_reply = bottom_line_list[bottom_index]["bottom_line_reply"]
                                    bottomVideoDuration = bottom_line_list[bottom_index]["bottomVideoDuration"]
                                    if bottom_count % 3 != 0:
                                        try:
                                            #await client.send_message(question_push_json_message_struct(result))
                                            await client.send_message(answer_push_json_message_struct(bottom_video_url, bottom_line_reply, bottomVideoDuration, "",""))
                                            await client.send_message(qa_empty_message_push_struct(200,"",11,questionGuideList))
                                            rec_time = time.time() + float(bottomVideoDuration) / 1000
                                            await client.change_status()
                                            logger.log_message('info', '兜底话术回复成功。')
                                        except:
                                            logger.log_message('error', '兜底话术回复失败。')
                                    else:
                                        await client.send_message(question_push_json_message_struct(result))
                                        #await client.send_message(qa_empty_message_push_struct(200,bottom_line_reply,0)) 
                                        await client.send_message(qa_empty_message_push_struct(200,"你可以参考问题指引向我提问。",11,questionGuideList))    
                                    bottom_count += 1            
            
                        elif client.get_propaganda_status() == "1" and client.get_attendanceStatus() != "1":
                            if enterPublicity_word in result:
                                await client.send_message(question_status_push_struct("questionPushEnd"))
                                await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))
                                await client.change_enter_propaganda_status()
                            elif exitPublicity_word in result:
                                await client.send_message(question_status_push_struct("questionPushEnd"))
                                await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
                                await client.change_exit_propaganda_status()
                            elif attendance_word in result:
                                await client.send_message(question_status_push_struct("questionPushEnd"))
                                await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                                await client.send_message(command_word_push_json_message_struct( "attendanceMode", result, attendance_word))
                                await client.change_enterAttendanceStatus()
                                logger.log_message('info', '往赤兔发送打卡命令词发送成功。')

                        elif client.get_attendanceStatus() == "1": 
                            """
                            if attendance_word in result:
                                await client.send_message(qa_empty_message_push_struct(200,attendance_word))
                                await client.send_message(command_word_push_json_message_struct( "attendanceMode", result, attendance_word))
                                await client.change_enterAttendanceStatus()
                            """
                            if exit_attendance_word in result:
                                await client.send_message(question_status_push_struct("questionPushEnd"))
                                await client.send_message(qa_empty_message_push_struct(200,"已退出打卡模式"))
                                await client.send_message(exit_attendance_mode_push_struct())
                                await client.send_message(command_word_push_json_message_struct("exitAttendanceMode",result,exit_attendance_word))
                                await client.change_exitAttendanceStatus()     
                        sample_time = time.time()
                        break
                    elif len(result) > 20 or (0 < len(result) < 2):
                        try:
                            producer_process.terminate()
                            producer_process.join()
                        except Exception as e:
                            pass    
                        rec_time = time.time()
                        await client.send_message(question_status_push_struct("questionPushRetract"))
                        recognizer.reset(stream)
                        segment_id += 1
                        break
                    else:
                        if time.time() - rec_time > 30:
                            print("rec end")
                            try:
                                producer_process.terminate()
                                producer_process.join()
                            except Exception as e:
                                pass
                            await client.change_status_pick()
                            return
                        else:
                            continue
                else:
                    if time.time() - rec_time > 30:
                        print("rec end")
                        try:
                            producer_process.terminate()
                            producer_process.join()
                        except Exception as e:
                            pass
                        await client.change_status_pick()
                        return
                    else:
                        continue

        elif client.get_status() != "1":
            rec_time  = time.time()
            continue
