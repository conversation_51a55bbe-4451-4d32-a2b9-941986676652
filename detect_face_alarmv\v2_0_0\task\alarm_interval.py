import time

class AlarmInterval(object):
    def __init__(self, interval):
        self.time = 0
        self.interval = interval

    def contrast_time(self):
        time_now = time.time()
        if time_now - self.time > self.interval:
            self.time = time.time()
            return True
        else:
            return False

    def run_interval(self, post_bool):
        """
        与其他算法保持一致的报警间隔方法
        Args:
            post_bool: 是否检测到目标
        Returns:
            bool: 是否应该发送报警
        """
        if post_bool:
            return self.contrast_time()
        else:
            return False
