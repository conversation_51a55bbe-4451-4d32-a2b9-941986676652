{"name": "sherpa-onnx", "version": "SHERPA_ONNX_VERSION", "description": "Speech-to-text and text-to-speech using Next-gen <PERSON><PERSON><PERSON> without internet connection", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/k2-fsa/sherpa-onnx.git"}, "keywords": ["speech to text", "text to speech", "transcription", "real-time speech recognition", "without internet connection", "embedded systems", "open source", "zipformer", "asr", "tts", "stt", "c++", "onnxruntime", "onnx", "ai", "next-gen kaldi", "offline", "privacy", "open source", "streaming speech recognition", "speech", "recognition", "WebAssembly", "wasm"], "author": "The next-gen <PERSON><PERSON><PERSON> team", "license": "Apache-2.0", "bugs": {"url": "https://github.com/k2-fsa/sherpa-onnx/issues"}, "homepage": "https://github.com/k2-fsa/sherpa-onnx#readme", "dependencies": {}}