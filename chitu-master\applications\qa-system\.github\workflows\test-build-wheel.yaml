name: test-build-wheel

on:
  push:
    branches:
      - master
    paths:
      - 'setup.py'
      - '.github/workflows/test-build-wheel.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/python/**'
  pull_request:
    branches:
      - master
    paths:
      - 'setup.py'
      - '.github/workflows/test-build-wheel.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/python/**'

  workflow_dispatch:

concurrency:
  group: test-build-wheel-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test-build-wheel:
    name: ${{ matrix.os }} ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-${{ matrix.python_version }}

      - name: Install python dependencies
        shell: bash
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install wheel twine setuptools

      - name: Build
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          export SHERPA_ONNX_MAKE_ARGS="VERBOSE=1 -j"

          python3 setup.py bdist_wheel
          ls -lh dist

      - name: Display wheel
        shell: bash
        run: |
          ls -lh dist

      - name: Install wheel
        shell: bash
        run: |
          pip install --verbose ./dist/*.whl

      - name: Test
        shell: bash
        run: |
          # For windows
          export PATH=/c/hostedtoolcache/windows/Python/3.7.9/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.8.10/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.9.13/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.10.11/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.11.8/x64/bin:$PATH
          export PATH=/c/hostedtoolcache/windows/Python/3.12.2/x64/bin:$PATH

          which sherpa-onnx
          sherpa-onnx --help
