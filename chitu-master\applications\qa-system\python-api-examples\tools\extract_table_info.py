#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: extract_table_info.py

Description: This is an get table info from mysql Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import sqlite3
import pandas as pd
import ast


#从数据库中提取表格内容
def get_sqlite_table_info(db_path, table_name):
    """  
    获取SQLite数据库中指定表格的列信息。  
  
    :param db_path: SQLite数据库文件的路径  
    :param table_name: 要查询的表格名称  
    :return: 一个包含列信息的列表，每个元素是一个包含列名和数据类型的元组  
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        if table_name == "admin_digital_human_interactive":
            query = f"SELECT * FROM {table_name} WHERE enabled = 0"
        else:
            query = f"SELECT * FROM {table_name}"
        df = pd.read_sql_query(query,conn)
        conn.close()
        return df

    except sqlite3.Error as e:
        pass
        #print(f"数据库错误: {e}")

    finally:
        conn.close()

#从xls的表格获取问题
def qa_pairs_extract(qa_file):
    file_path = qa_file
    df = pd.read_excel(file_path)
    column_data = df['问题'].tolist()
    return column_data

#根据相似问题扩充回复信息列表
def expand_lists(list1, list2):
    result = []
    for i in range(len(list1)):
        sub_str = list1[i]
        try:
            sub_list = ast.literal_eval(sub_str)
            for _ in range(len(sub_list)+1):
                result.append(list2[i])
        except SyntaxError:
            pass
    return result

#根据相似问题扩充问题列表
def expand_question_lists(list1, list2):
    new_list =[]
    for i in range(len(list1)):
        sub_str = list1[i]
        try:
            sub_list = ast.literal_eval(sub_str)
            num_to_add = len(sub_list)
            new_list.append(list2[i])
            for j in range(num_to_add):
                new_list.append(sub_list[j])
        except SyntaxError:
            pass
    return new_list
