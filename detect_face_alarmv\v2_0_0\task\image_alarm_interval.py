import time

def contrast_time(interval_image, transaction_number, interval):
    """
    对比时间间隔，控制报警频率
    Args:
        interval_image: 时间间隔字典
        transaction_number: 事务编号
        interval: 时间间隔（秒）
    Returns:
        alarm_bool: 是否触发报警
        interval_image: 更新后的时间间隔字典
    """
    try:
        current_time = time.time()
        
        if transaction_number in interval_image:
            # 检查时间间隔
            last_time = interval_image[transaction_number]
            if current_time - last_time >= interval:
                # 超过间隔时间，可以报警
                interval_image[transaction_number] = current_time
                return True, interval_image
            else:
                # 未超过间隔时间，不报警
                return False, interval_image
        else:
            # 第一次检测，直接报警
            interval_image[transaction_number] = current_time
            return True, interval_image
            
    except Exception as e:
        print(f"时间间隔对比失败: {e}")
        return True, interval_image  # 出错时默认允许报警
