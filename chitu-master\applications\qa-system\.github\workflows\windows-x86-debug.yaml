name: windows-x86-debug

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/windows-x86-debug.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - '.github/scripts/test-online-ctc.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/windows-x86-debug.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - '.github/scripts/test-online-ctc.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'

  workflow_dispatch:

concurrency:
  group: windows-x86-debug-${{ github.ref }}
  cancel-in-progress: true

jobs:
  windows_x86_debug:
    name: Windows x86 ${{ matrix.build_type }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        shared_lib: [OFF]
        build_type: [Debug, MinSizeRel, RelWithDebInfo]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure CMake
        shell: bash
        run: |
          mkdir build
          cd build
          cmake -A Win32 -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} -D BUILD_SHARED_LIBS=${{ matrix.shared_lib }}  -D CMAKE_INSTALL_PREFIX=./install ..

      - name: Build sherpa-onnx for windows
        shell: bash
        run: |
          cd build
          cmake --build . --config ${{ matrix.build_type }} -- -m:2
          cmake --build . --config ${{ matrix.build_type }} --target install -- -m:2

          ls -lh ./bin/${{ matrix.build_type }}/sherpa-onnx.exe

      - name: Test online CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin/${{ matrix.build_type }}:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-ctc.sh
