import datetime
from applications.extensions import db

class AlarmPushRecord(db.Model):
    __tablename__ = 'admin_alarm_push_record'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键Id')
    push_type = db.Column(db.String(1), nullable=False, comment='推送方式 1：微信 2：钉钉 3：邮箱')
    content = db.Column(db.String(64), nullable=False, comment='推送内容')
    push_target = db.Column(db.String(32), nullable=True, comment='推送目标')
    status = db.Column(db.String(1), nullable=False, comment='推送状态 0：失败 1：成功')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.datetime.now, comment='推送时间')

    def json(self):
        return {
            'id': self.id,
            'push_type': self.push_type,
            'content': self.content,
            'push_target': self.push_target,
            'status': self.status,
            'create_time': self.create_time.isoformat()
        }