import os
import yaml
import numpy as np
import torch
from .models.yolo import attempt_load
from .utils_torch.load_model import DetectMultiBackend
from .utils_torch.nms import non_max_suppression
from .utils_torch.utils import letterbox, get_draw_box
os.environ['CUDA_VISIBLE_DEVICES'] = '0'


class InferenceTorch(object):
    def __init__(self, label, weight_path, model_name):
        cfg_path = "config/algorithm_inference.yaml"
        if os.path.exists(cfg_path):  # 判断文件是否存在，不存在就直接使用默认参数
            with open(cfg_path, "rb") as f:
                cfg = yaml.load(f, yaml.FullLoader)
            cfg_key = cfg.keys()
            if "transform_wh" in cfg_key:
                self.wh = cfg["transform_wh"]
            else:
                self.wh = [640, 640]

            if "confidence" in cfg_key:
                self.confidence = cfg["confidence"]
            else:
                self.confidence = 0.6

            if "nms_threshold" in cfg_key:
                self.nms_threshold = cfg["nms_threshold"]
            else:
                self.nms_threshold = 0.45
        else:
            self.wh = [640, 640]
            self.confidence = 0.6
            self.nms_threshold = 0.45

        self.label = label
        self.device = torch.device('cuda:0')
        model_load = attempt_load('weight/{}.pt'.format(model_name), map_location=self.device)
        self.model = DetectMultiBackend(model_load, self.device)
        self.model.warmup(imgsz=(1, 3, *self.wh))

    def run(self, img_dict, gain, original_image_shape=None):
        """兼容新接口的run方法"""
        # 如果传入的是单个图像而不是字典，转换为字典格式
        if not isinstance(img_dict, dict):
            img_dict = {"default": img_dict}

        imgkeys = img_dict.keys()
        img_gain = {}
        image_list = None
        for key in imgkeys:
            image, gain = letterbox(img_dict[key], self.wh)
            img_gain[key] = gain

            img = image.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
            img = np.ascontiguousarray(img)
            imag = img[np.newaxis, :, :, :]
            if image_list is None:
                image_list = imag
            else:
                image_list = np.vstack((image_list, imag))

        im = torch.from_numpy(image_list).to(self.device)
        im = im.half() if self.model.fp16 else im.float()  # uint8 to fp16/32
        im /= 255  # 0 - 255 to 0.0 - 1.0
        if len(im.shape) == 3:
            im = im[None]  # expand for batch dim

        #前传
        pred = self.model(im, augment=False, visualize=False)

        #NMS
        pred_torch = non_max_suppression(pred, self.confidence, self.nms_threshold)

        #draw box
        draw_boxes_all = {}
        index = 0
        for key in imgkeys:
                draw_boxes = get_draw_box(pred_torch[index], img_gain[key], self.label)
                draw_boxes_all[key] = draw_boxes
                index += 1

        # 如果输入是单个图像，返回单个结果
        if "default" in draw_boxes_all:
            return draw_boxes_all["default"]
        return draw_boxes_all