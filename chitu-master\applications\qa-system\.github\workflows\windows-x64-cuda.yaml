name: windows-x64-cuda

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/windows-x64-cuda.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/windows-x64-cuda.yaml'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-online-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'

  workflow_dispatch:

concurrency:
  group: windows-x64-cuda-${{ github.ref }}
  cancel-in-progress: true

jobs:
  windows_x64_cuda:
    name: Windows x64 CUDA
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure CMake
        shell: bash
        run: |
          mkdir build
          cd build
          cmake -A x64 -D CMAKE_BUILD_TYPE=Release -D BUILD_SHARED_LIBS=ON -DCMAKE_INSTALL_PREFIX=./install -DSHERPA_ONNX_ENABLE_GPU=ON ..

      - name: Build sherpa-onnx for windows
        shell: bash
        run: |
          cd build
          cmake --build . --config Release -- -m:2
          cmake --build . --config Release --target install -- -m:2

          ls -lh ./bin/Release/sherpa-onnx.exe

      - name: Test spoken language identification
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline-language-identification.exe

          .github/scripts/test-spoken-language-identification.sh

      - name: Test online CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-ctc.sh

      - name: Test offline TTS
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline-tts.exe

          .github/scripts/test-offline-tts.sh

      - name: Test online paraformer for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-paraformer.sh

      - name: Test offline Whisper for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-whisper.sh

      - name: Test offline CTC for windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-ctc.sh

      - name: Test offline transducer for Windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx-offline.exe

          .github/scripts/test-offline-transducer.sh

      - name: Test online transducer for Windows x64
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=sherpa-onnx.exe

          .github/scripts/test-online-transducer.sh

      - name: Test online transducer (C API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin/Release:$PATH
          export EXE=decode-file-c-api.exe

          .github/scripts/test-online-transducer.sh

      - name: Copy files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-win-x64-cuda
          mkdir $dst

          cp -a build/install/bin $dst/
          cp -a build/install/lib $dst/
          cp -a build/install/include $dst/

          tar cjvf ${dst}.tar.bz2 $dst

      - name: Release pre-compiled binaries and libs for Windows x64 CUDA
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*-win-x64-cuda.tar.bz2
