from transformers import AutoTokenizer, AutoModel
import torch
import time
import onnxruntime
import numpy as np
# Sentences we want sentence embeddings for
sentences_1 = ['厕所在哪里？','中国的首都是哪里？', '美国的总统是谁？','研发经理是谁？','产品副总监是谁？','销售经理是谁？','我是来面试的','我预约的岗位是前端开发岗','我预约的岗位是后端开发岗','我预约的岗位是销售经理岗','我预约的岗位是产品的经理岗位','面试地点在哪里']
sentences_2 = ["研发负责人是谁"]
# Load model from HuggingFace Hub
model = AutoModel.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
tokenizer = AutoTokenizer.from_pretrained('/mnt/keep/workspace/digital_human_data/speech_files/bge-small-zh-v1.5')
model.eval()
print(time.time())
start_time = time.time()
# Tokenize sentences
encoded_input_1 = tokenizer(sentences_1, padding=True, truncation=True, return_tensors='pt')
encoded_input_2 = tokenizer(sentences_2, padding=True, truncation=True, return_tensors='pt')
print(type(encoded_input_1))
# for s2p(short query to long passage) retrieval task, add an instruction to query (not add instruction for passages)

# Compute token embeddings
with torch.no_grad():
    model_output_1 = model(**encoded_input_1)
    model_output_2 = model(**encoded_input_2)

    # Perform pooling. In this case, cls pooling.

    sentence_embeddings_1 = model_output_1[0][:, 0]
    sentence_embeddings_2 = model_output_2[0][:, 0]
    scores = sentence_embeddings_1 @ sentence_embeddings_2.T
    print(scores)
    print(time.time()-start_time)

"""
# for s2p(short query to long passage) retrieval task, add an instruction to query (not add instruction for passages)
# encoded_input = tokenizer([instruction + q for q in queries], padding=True, truncation=True, return_tensors='pt')
#print(encoded_input)
# Compute token embeddingsa

    model_output_1 = model(**encoded_input_1)
    model_output_2 = model(**encoded_input_2)
    # Perform pooling. In this case, cls pooling.
    sentence_embeddings_1 = model_output_1[0][:, 0]
    sentence_embeddings_2 = model_output_2[0][:, 0]
# normalize embeddings
#sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
#print("Sentence embeddings:", sentence_embeddings)
scores = sentence_embeddings_1 @ sentence_embeddings_2.T
print(scores)
print(time.time()-start_time)
"""
