import yaml
import os
import sys
from ..utils.log import mylog

class ModelCheck(object):
    """人脸识别算法推理部分统一接口 - 按照标准算法格式"""

    def __init__(self, label, model_name, number):
        current_path = os.path.dirname(os.path.abspath(__file__))
        weight_path = os.path.join(current_path.replace('inference', ''), 'weights')
        weight_list = sorted(os.listdir(weight_path))
        model_type = ''

        # 人脸识别算法的权重文件映射
        weight_mapping = {
            'detect_face_alarm': 'face.rknn',  # 主要的人脸识别模型
            'retinaface_mob': 'retinaface_mob.rknn',  # 人脸检测模型
            'people': 'people.rknn'  # 人员检测模型
        }

        # 首先检查映射表
        if model_name in weight_mapping:
            target_file = weight_mapping[model_name]
            if target_file in weight_list:
                model_type = target_file.split('.')[-1]
                model_name = target_file.split('.')[0]
        else:
            # 原有的匹配逻辑
            for w in weight_list:  # 多个模型，取匹配到的第一个名字相同的模型
                weight_name_type = w.split('.')
                weight_name = weight_name_type[0]
                weight_type = weight_name_type[1]
                if model_name == weight_name:
                    model_type = weight_type

        # 初始化推理引擎
        if model_type == 'pt':
            from .inference_torch.inference_torch import InferenceTorch
            self.model = InferenceTorch(label, weight_path, model_name, number)
        elif model_type == 'engine':
            from .inference_trt.inference_trt import InferenceTrt
            self.model = InferenceTrt(label, weight_path, model_name, number)
        elif model_type == 'rknn':
            from .inference_rknn.inference_rknn import InferenceRknn
            self.model = InferenceRknn(label, weight_path, model_name, number)
        else:
            mylog.warning(f'未找到模型文件，使用模拟推理: {model_name}')
            self.model = None

        mylog.info('加载人脸识别推理模块')

    def run(self, image, gain, original_image_shape=None):
        """
        运行人脸识别推理 - 按照标准算法格式
        Args:
            image: 预处理后的图像
            gain: 缩放参数
            original_image_shape: 原始图像形状（可选）
        Returns:
            face_boxes: 人脸检测结果列表，格式为 [x1,y1,x2,y2,conf,person_name,similarity]
        """
        try:
            if self.model is None:
                # 模拟人脸检测结果用于测试
                return self._simulate_face_detection(image, gain, original_image_shape)

            # 检查模型是否支持新的接口
            if hasattr(self.model, 'run') and hasattr(self.model.run, '__code__'):
                # 检查run方法的参数数量
                arg_count = self.model.run.__code__.co_argcount
                if arg_count >= 4:  # self, image, gain, original_image_shape
                    return self.model.run(image, gain, original_image_shape)
                else:
                    # 兼容旧接口
                    return self.model.run(image, gain)
            else:
                return self.model.run(image, gain)
        except Exception as e:
            mylog.error(f"推理失败: {e}")
            return []

    def _simulate_face_detection(self, image, gain, original_image_shape=None):
        """模拟人脸检测结果用于测试"""
        import random
        import numpy as np

        # 模拟检测到0-2个人脸
        num_faces = random.randint(0, 2)
        face_boxes = []

        # 使用原始图像形状或letterbox图像形状
        if original_image_shape is not None:
            h, w = original_image_shape[:2]
        elif image is not None and len(image.shape) >= 2:
            h, w = image.shape[:2]
        else:
            h, w = 480, 640  # 默认尺寸

        mylog.info(f"[SIMULATE] 模拟人脸检测，图像尺寸: {w}x{h}, 生成{num_faces}个人脸")

        for i in range(num_faces):
            # 随机生成人脸框（确保在图像范围内）
            x1 = random.randint(0, max(1, w//2))
            y1 = random.randint(0, max(1, h//2))
            x2 = random.randint(x1 + 50, min(x1 + 200, w-1))
            y2 = random.randint(y1 + 50, min(y1 + 200, h-1))
            conf = random.uniform(0.7, 0.95)

            # 随机决定是否为已知人员
            if random.random() > 0.5:
                person_name = random.choice(["张三", "李四", "王五"])
                similarity = random.uniform(0.8, 0.95)
            else:
                person_name = "unknown"
                similarity = random.uniform(0.3, 0.6)

            face_boxes.append([x1, y1, x2, y2, conf, person_name, similarity])
            mylog.info(f"[SIMULATE] 生成人脸{i+1}: [{x1},{y1},{x2},{y2}], {person_name}, 置信度:{conf:.3f}")

        return face_boxes
