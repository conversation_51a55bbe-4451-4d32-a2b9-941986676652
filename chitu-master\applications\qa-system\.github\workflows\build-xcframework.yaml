name: build-xcframework

on:
  push:
    branches:
      - master
    paths:
      - './build-ios.sh'
      - '.github/workflows/build-xcframework.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
    tags:
      - '*'
  pull_request:
    branches:
      - master
    paths:
      - './build-ios.sh'
      - '.github/workflows/build-xcframework.yaml'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'

  workflow_dispatch:

concurrency:
  group: build-xcframework-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_xcframework:
    name: tts-${{ matrix.with_tts }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest]
        with_tts: [ON, OFF]

    steps:
      - uses: actions/checkout@v4

      - name: Build iOS
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          ./build-ios.sh

      - name: Build iOS (No tts)
        if: matrix.with_tts == 'OFF'
        shell: bash
        run: |
          ./build-ios-no-tts.sh

      - name: Display artifacts
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          brew install tree
          tree -L 2 ./build-ios

      - name: Display artifacts
        if: matrix.with_tts == 'OFF'
        shell: bash
        run: |
          brew install tree
          tree -L 2 ./build-ios-no-tts

      - name: Package artifacts
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)
          echo "SHERPA_ONNX_VERSION=$SHERPA_ONNX_VERSION" >> "$GITHUB_ENV"

          rm -rf build-ios/build
          rm -rf build-ios/install
          rm -rf build-ios/ios-onnxruntime/.git

          tree build-ios

          filename=sherpa-onnx-${SHERPA_ONNX_VERSION}-ios.tar.bz2

          tar cjvf $filename ./build-ios

          ls -lh

      - name: Package artifacts
        if: matrix.with_tts == 'OFF'
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)
          echo "SHERPA_ONNX_VERSION=$SHERPA_ONNX_VERSION" >> "$GITHUB_ENV"

          rm -rf build-ios-no-tts/build
          rm -rf build-ios-no-tts/install
          rm -rf build-ios-no-tts/ios-onnxruntime/.git

          tree build-ios-no-tts

          filename=sherpa-onnx-${SHERPA_ONNX_VERSION}-ios-no-tts.tar.bz2

          tar cjvf $filename ./build-ios-no-tts

          ls -lh

      - uses: actions/upload-artifact@v4
        if: matrix.with_tts == 'ON'
        with:
          name: sherpa-onnx-ios-libs
          path: ./build-ios

      - uses: actions/upload-artifact@v4
        if: matrix.with_tts == 'OFF'
        with:
          name: sherpa-onnx-ios-libs-no-tts
          path: ./build-ios-no-tts

      # https://huggingface.co/docs/hub/spaces-github-actions
      - name: Publish to huggingface
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && (github.event_name == 'push' || github.event_name == 'workflow_dispatch')
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v3
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            GIT_LFS_SKIP_SMUDGE=1 git clone https://huggingface.co/csukuangfj/sherpa-onnx-libs huggingface

            cd huggingface
            git lfs pull

            cp -v ../sherpa-onnx-*.tar.bz2 ./

            git status
            git lfs track "*.bz2"

            git add .

            git commit -m "upload sherpa-onnx-${SHERPA_ONNX_VERSION}-ios.tar.bz2"

            git push https://csukuangfj:$<EMAIL>/csukuangfj/sherpa-onnx-libs main

      - name: Release xcframework
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*.tar.bz2
