name: wasm-simd-hf-space-zh-en-asr-paraformer

on:
  release:
    types:
      - published

  workflow_dispatch:

concurrency:
  group: wasm-simd-hf-space-zh-en-asr-paraformer-${{ github.ref }}
  cancel-in-progress: true

jobs:
  wasm-simd-hf-space-zh-en-asr-paraformer:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install emsdk
        uses: mymindstorm/setup-emsdk@v14

      - name: View emsdk version
        shell: bash
        run: |
          emcc -v
          echo "--------------------"
          emcc --check

      - name: Download model files
        shell: bash
        run: |
          cd wasm/asr/assets
          ls -lh
          echo "----------"

          wget https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-streaming-paraformer-bilingual-zh-en.tar.bz2
          tar xvf sherpa-onnx-streaming-paraformer-bilingual-zh-en.tar.bz2
          rm sherpa-onnx-streaming-paraformer-bilingual-zh-en.tar.bz2

          mv sherpa-onnx-streaming-paraformer-bilingual-zh-en/encoder.int8.onnx encoder.onnx
          mv sherpa-onnx-streaming-paraformer-bilingual-zh-en/decoder.int8.onnx decoder.onnx
          mv sherpa-onnx-streaming-paraformer-bilingual-zh-en/tokens.txt ./

          rm -rf sherpa-onnx-streaming-paraformer-bilingual-zh-en

          ls -lh

          cd ../

          sed -i.bak s/"type = 0"/"type = 1"/g ./sherpa-onnx-asr.js
          sed -i.bak s/Zipformer/Paraformer/g ./index.html

          git diff

      - name: Build sherpa-onnx for WebAssembly (ASR)
        shell: bash
        run: |
          ./build-wasm-simd-asr.sh

      - name: collect files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          dst=sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-zh-en-asr-paraformer
          mv build-wasm-simd-asr/install/bin/wasm/asr $dst
          ls -lh $dst
          tar cjfv ${dst}.tar.bz2 ./${dst}

      - name: Upload wasm files
        uses: actions/upload-artifact@v4
        with:
          name: sherpa-onnx-wasm-simd-zh-en-asr-paraformer
          path: ./sherpa-onnx-wasm-simd-*.tar.bz2

      - name: Publish to ModelScope
        # if: false
        env:
          MS_TOKEN: ${{ secrets.MODEL_SCOPE_GIT_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf ms
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://www.modelscope.cn/studios/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en-paraformer.git ms
            cd ms
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-*/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://oauth2:${MS_TOKEN}@www.modelscope.cn/studios/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en-paraformer.git

      - name: Publish to huggingface
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/spaces/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en-paraformer huggingface
            cd huggingface
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-*/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://csukuangfj:$<EMAIL>/spaces/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en-paraformer main
