import sys
import os
sys.path.append(os.path.dirname(__file__))

from flask import current_app
from applications.extensions import db
from applications.algorithm.utils.algo_utils import truelist
from applications.algorithm.utils.videocap import VideoCap
from applications.algorithm.utils.start_detect import StartupDetect
from applications.models.admin_algorithm import Algorithm
import threading

def change_camera_alarm_status(status, url, app):

    camera = str(status[0]["camera_id"])
    
    if camera not in list(truelist.trueL.keys()):
        truelist.addUrl(camera)
    
    open = []
    for item in status:
        if str(item['status']) == "1":
            open.append(item['algo_id'])
    
    if len(open):
        if truelist.trueL[camera]["thread"] is None:
            truelist.addSDThread(camera, StartupDetect('', '', '', app))
            
        if truelist.trueL[camera]["videoCap"] is None:
            truelist.addVideoCap(camera, VideoCap(url, camera, app))
        
        truelist.addThreadpool(camera)
        truelist.updateAlarmType(camera, open)
        
        tmp_used_weight_id = []
        for item in db.session.query(Algorithm).filter(Algorithm.id.in_(open)).group_by(Algorithm.weight).all():
            tmp_used_weight_id.append(item.weight)
        
        start_t = truelist.trueL[camera]["thread"]
        start_t.reStartDet(truelist, camera, tmp_used_weight_id, app)
    
    else:
        
        if camera in truelist.trueL.keys():
            start_t = truelist.trueL[camera]["thread"]
            videocap = truelist.trueL[camera]["videoCap"]
            if videocap is not None:
                truelist.delAlarmType(camera)
                truelist.delThreadPools(camera)
                videocap.stopDrawframe()
                start_t.stopDet()
                del truelist.trueL[camera]
        
    return {"msg": "success"}