from .utils.face_recognition import DetectFaceAlarm
import json
import time
import os

def load():
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config/documentation.json'), 'r', encoding='utf-8') as file:
        docu = json.load(file)
    model_name = docu["modelName"]
    model_version = docu["modelVersion"]
    algo_name = docu["algoSet"][0]["algoName"]
    return {model_name+ ":" + model_version + ":" + algo_name: DetectFaceAlarm(model_name, model_version, algo_name)}


