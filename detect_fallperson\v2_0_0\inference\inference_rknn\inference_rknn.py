import os
import gc
import yaml
from .utils_rknn.utils import load_model, letterbox, predict, get_draw_box


class InferenceRknn(object):
    def __init__(self, label, weight_path, model_name, number):

        self.wh = [640, 640]
        self.confidence = 0.45      # 提高到80%，严格减少误检
        self.nms_threshold = 0.5   # 提高到50%，减少重复检测

        self.label = label
        self.judge = True
        self.rknn = load_model(os.path.join(weight_path, "{}.rknn".format(model_name)), number)


    def run(self, image, gain):
        """ 预测一张图片，预处理保持宽高比 """
        box_src = predict(self.confidence, self.nms_threshold, image, self.wh, self.rknn)
        draw_boxes = get_draw_box(box_src, gain, self.label)
        gc.collect()
        return draw_boxes
