import cv2
import time
import sys


class VideoFile(object):
    def __init__(self, rtsp, spaces):
        self.spaces = spaces
        self.cap = cv2.VideoCapture(rtsp)
        self.size_wh = [int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))]
        if self.cap.isOpened():
            self.cap_status = True
        else:
            print('视频文件不可用，请重新加载')

    def get_image(self):
        if self.cap_status:
            for _ in range(self.spaces):
                succ = self.cap.grab()
                if not succ:
                    self.cap_status = False
                    self.cap.release()
                    break
            success, im = self.cap.retrieve()
            if success:
                image = im
            else:
                image = []
            return image
        else:
            return None
            

    def __iter__(self):
        return self