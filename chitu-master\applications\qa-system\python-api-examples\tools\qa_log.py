#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File Name: qa_log.py

Description: This is an qa system log Python script.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import logging
from datetime import datetime
from logging import handlers
import os

#问答算法的log
class AppLogger:
    def __init__(self, log_file_path='/mnt/keep/workspace/digital_human_data/logs/speech_logs/qa.log', log_level=logging.DEBUG):
        if not os.path.exists(os.path.dirname(os.path.abspath(log_file_path))):
            os.makedirs(os.path.dirname(os.path.abspath(log_file_path)))
        self.logger = self.setup_logger(log_file_path, log_level)
    #设置log格式
    def setup_logger(self, log_file_path, log_level):
        logger = logging.getLogger(__name__)
        logger.setLevel(log_level)

        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')

        handler = logging.handlers.TimedRotatingFileHandler(log_file_path, when='midnight', interval=1, backupCount=7)
        handler.setFormatter(formatter)

        logger.addHandler(handler)
        return logger
    #写入log
    def log_message(self, level, message):
        if level not in ['info', 'error', 'debug']:
            raise ValueError(f"Invalid log level: {level}. Must be one of 'info', 'error', 'debug'.")

        if level == 'info':
            self.logger.info(f'{message}')
        elif level == 'error':
            self.logger.error(f'{message}')
        elif level == 'debug':
            self.logger.debug(f'{message}')

