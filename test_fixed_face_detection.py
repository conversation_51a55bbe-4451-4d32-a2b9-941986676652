#!/usr/bin/env python3
"""
测试修复后的人脸检测
"""

import sys
import os
import cv2
import numpy as np
import base64
import json
import time

def create_test_face_image():
    """创建测试人脸图像"""
    img = np.ones((480, 640, 3), dtype=np.uint8) * 200
    
    # 绘制人脸
    cv2.ellipse(img, (320, 240), (80, 100), 0, 0, 360, (220, 200, 180), -1)
    cv2.ellipse(img, (290, 220), (15, 8), 0, 0, 360, (50, 50, 50), -1)
    cv2.ellipse(img, (350, 220), (15, 8), 0, 0, 360, (50, 50, 50), -1)
    cv2.circle(img, (290, 220), 5, (0, 0, 0), -1)
    cv2.circle(img, (350, 220), 5, (0, 0, 0), -1)
    cv2.ellipse(img, (320, 240), (8, 15), 0, 0, 360, (200, 180, 160), -1)
    cv2.ellipse(img, (320, 270), (20, 8), 0, 0, 360, (150, 100, 100), -1)
    
    return img

def image_to_base64(img):
    """将图像转换为base64"""
    _, buffer = cv2.imencode('.jpg', img)
    return base64.b64encode(buffer).decode('utf-8')

def test_inference_rknn_directly():
    """直接测试InferenceRknn类"""
    print("🔍 测试InferenceRknn类")
    print("=" * 50)
    
    try:
        # 添加路径
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from inference.inference_rknn.inference_rknn import InferenceRknn
        
        # 创建实例
        print("  - 创建InferenceRknn实例...")
        rknn_inference = InferenceRknn(['face'], 'detect_face_alarm/v2_0_0/weights', 'retinaface_mob', 0)
        
        print(f"  - 检测模型状态: {'已加载' if rknn_inference.detection_model else '未加载'}")
        print(f"  - 置信度阈值: {rknn_inference.confidence}")
        print(f"  - NMS阈值: {rknn_inference.nms_threshold}")
        
        # 创建测试图像
        test_img = create_test_face_image()
        gain = [1.0, 0, 0]
        
        print("  - 开始推理...")
        result = rknn_inference.run(test_img, gain, test_img.shape)
        
        print(f"  - 推理结果: {len(result)} 个检测框")
        
        for i, detection in enumerate(result):
            if len(detection) >= 7:
                x1, y1, x2, y2, conf, name, sim = detection
                print(f"    检测框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}, 人员: {name}, 相似度: {sim:.3f}")
            elif len(detection) >= 5:
                x1, y1, x2, y2, conf = detection[:5]
                print(f"    检测框{i+1}: [{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}], 置信度: {conf:.3f}")
        
        return len(result) > 0
        
    except Exception as e:
        print(f"  ❌ InferenceRknn测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_interface():
    """测试算法接口"""
    print("\n🔍 测试算法接口")
    print("=" * 50)
    
    try:
        # 添加路径
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        
        from utils.face_recognition import DetectFaceAlarm
        
        # 创建算法实例
        print("  - 创建DetectFaceAlarm实例...")
        detector = DetectFaceAlarm("detect_face_alarm", "v2.0.0", "face_alarm")
        
        print("  ✅ 算法实例创建成功")
        
        # 创建测试数据
        test_data = {
            "transactionNumber": f"test_fixed_{int(time.time())}",
            "businessData": {
                "image": image_to_base64(create_test_face_image()),
                "imageType": "base64",
                "advsetValue": {
                    "recvDataType": ["infer_results", "draw_image", "infer_boxes"],
                    "interval": 5
                }
            }
        }
        
        print("  - 调用推理接口...")
        result = detector.infer(test_data, "models.model_manager")
        print(f"  - 推理调用结果: {result}")
        
        # 等待推理完成
        print("  - 等待推理完成...")
        time.sleep(3)
        
        # 检查输出队列
        if not detector.out_que.empty():
            back_json, post_bool, data, model_way = detector.out_que.get()
            print(f"  ✅ 获取到推理结果:")
            print(f"    - 是否触发报警: {post_bool}")
            
            if back_json:
                print(f"    - 结果类型: {type(back_json)}")
                if isinstance(back_json, dict):
                    boxes = back_json.get('boxes', {})
                    values = back_json.get('values', [])
                    print(f"    - 检测框数量: {sum(len(box_list) for box_list in boxes.values())}")
                    print(f"    - 消息数量: {len(values)}")
                    
                    # 详细输出检测框
                    for box_type, box_list in boxes.items():
                        print(f"    - {box_type}检测框: {len(box_list)} 个")
                        for i, box in enumerate(box_list[:3]):
                            print(f"      框{i+1}: {box}")
                    
                    # 详细输出消息
                    for i, value in enumerate(values[:3]):
                        print(f"    - 消息{i+1}: {value}")
            
            return True
        else:
            print("  ⚠️ 输出队列为空")
            return False
        
    except Exception as e:
        print(f"  ❌ 算法接口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_modes():
    """测试不同的检测模式"""
    print("\n🔍 测试不同检测模式")
    print("=" * 50)
    
    try:
        sys.path.insert(0, 'detect_face_alarm/v2_0_0')
        from utils.face_recognition import DetectFaceAlarm
        
        detector = DetectFaceAlarm("detect_face_alarm", "v2.0.0", "face_alarm")
        
        # 测试不同模式
        modes = ["authorized", "unauthorized"]
        
        for mode in modes:
            print(f"\n  - 测试模式: {mode}")
            
            test_data = {
                "transactionNumber": f"test_{mode}_{int(time.time())}",
                "businessData": {
                    "image": image_to_base64(create_test_face_image()),
                    "imageType": "base64",
                    "advsetValue": {
                        "faceAlarm": mode,
                        "recvDataType": ["infer_results", "draw_image", "infer_boxes"],
                        "interval": 5
                    }
                }
            }
            
            result = detector.infer(test_data, "models.model_manager")
            print(f"    推理调用: {result}")
            
            time.sleep(2)
            
            if not detector.out_que.empty():
                back_json, post_bool, data, model_way = detector.out_que.get()
                print(f"    报警状态: {post_bool}")
                if back_json and isinstance(back_json, dict):
                    values = back_json.get('values', [])
                    if values:
                        print(f"    检测结果: {[v.get('value', 'unknown') for v in values]}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 不同模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 测试修复后的人脸检测")
    print("=" * 70)
    
    tests = [
        ("InferenceRknn直接测试", test_inference_rknn_directly),
        ("算法接口测试", test_algorithm_interface),
        ("不同模式测试", test_with_different_modes),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results[test_name] = False
    
    # 输出总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 人脸检测修复成功！")
        print("\n📋 现在可以:")
        print("  ✅ 正常检测人脸")
        print("  ✅ 触发报警机制")
        print("  ✅ 返回检测结果")
    else:
        print("⚠️ 部分功能仍有问题")

if __name__ == "__main__":
    main()
