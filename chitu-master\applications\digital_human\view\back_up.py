import os
import tarfile
import shutil
from log import mylog

backup_path = "/mnt/keep/workspace/digital_human_data/digital_human_package/backup"

digital_human_path = "/mnt/keep/workspace/chitu/applications/digital_human"
qa_system_path = "/mnt/keep/workspace/chitu/applications/qa-system"
digitalHuman_web_path = "/mnt/keep/workspace/chitu-web/chitu-digital-human/dist"
need_backup_folders = [digital_human_path, qa_system_path, digitalHuman_web_path]

digital_human_data_path = "/mnt/keep/workspace/digital_human_data"
discard_folders = ["digital_human_package", "digital_human_videos", "logs", "version.json", "speech_files"]
# digital_human_data_backup_folders = [os.path.join(digital_human_data_path, p) for p in (set(os.listdir(digital_human_data_path)) - set(discard_folders))]

database_path = "/mnt/keep/workspace/chitu/pear.db"

def compress_folders_to_tar(zip_filename, folders, discard=[]):
    temp_tar_filename = zip_filename + '.temp'
    with tarfile.open(temp_tar_filename, 'w:gz') as tarf:
        for folder in folders:
            for root, dirs, files in os.walk(folder):
                if len(discard) > 0 and root.startswith(tuple(discard)):
                    continue
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, start=os.path.dirname(folder))
                    tarf.add(file_path, arcname=arcname)
                    tarf.fileobj.flush()
                    os.fsync(tarf.fileobj.fileno())
    os.rename(temp_tar_filename, zip_filename)

def copy_with_real_time_disk_write(src, dst, buffer_size=1024 * 1024):
    with open(src, 'rb') as src_file:
        with open(dst, 'wb') as dst_file:
            while True:
                chunk = src_file.read(buffer_size)
                if not chunk:
                    break
                dst_file.write(chunk)
                dst_file.flush()
                os.fsync(dst_file.fileno())

def backup():
    try:
        if not os.path.exists(backup_path):
            os.makedirs(backup_path)
        else:
            shutil.rmtree(backup_path)
            os.makedirs(backup_path)
            
        # 备份前后端以及语音算法代码
        compress_folders_to_tar(os.path.join(backup_path, "backup.tar.gz"), need_backup_folders)

        # 备份数字人历史数据
        discard = [os.path.join(digital_human_data_path, p) for p in discard_folders]
        compress_folders_to_tar(os.path.join(backup_path, "digital_human_data.tar.gz"), [digital_human_data_path], discard)
        
        # 备份数据库数据
        copy_with_real_time_disk_write(database_path, os.path.join(backup_path, "pear.db"))
        return True
    except Exception as e:
        mylog.info(e)
        return False