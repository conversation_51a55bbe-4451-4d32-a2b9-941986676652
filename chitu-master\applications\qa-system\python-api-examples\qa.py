#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Name: qa.py

Description: This is an Real-time qa system Python script.
Real-time speech recognition from a microphone with sherpa-onnx Python API
with endpoint detection.

Author: songzhimeng
Creation Date: August 22, 2024
Version: 1.0

Copyright Notice: [Copyright owner information if applicable]
"""

import argparse
import sys,os
import re
import json
from pathlib import Path
import numpy as np
import soundfile as sf
import multiprocessing
from concurrent.futures import ThreadPoolExecutor

from tools.websocket_client import WebSocketClient
from tools.extract_table_info import (get_sqlite_table_info,
                                      expand_lists,
                                      expand_question_lists)
from tools.qa_log import AppLogger
from src.qa_model import create_recognizer
from tools.message_struct import (answer_push_json_message_struct,
                                  question_push_json_message_struct,
                                  command_word_push_json_message_struct,
                                  qa_empty_message_push_struct,
                                  face_exit_wake_up_push_struct,
                                  start_qa_service_push_struct,
                                  key_word_push_struct,
                                  restart_qa_service_push_struct,
                                  exit_attendance_mode_push_struct) 
from tools.dynamic_update_variable import update_variable
from tools.check_qa_library import assert_qa_data
from tools.speech_sample_producer import producer_general
from tools.qa_pipeline import rec_pipeline
from tools.global_vars import VarsUpdate

import time
import asyncio

#配置参数
def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--tokens",
        type=str,
        required=True,
        help="Path to tokens.txt",
    )

    parser.add_argument(
        "--encoder",
        type=str,
        required=True,
        help="Path to the encoder model",
    )

    parser.add_argument(
        "--decoder",
        type=str,
        required=True,
        help="Path to the decoder model",
    )

    parser.add_argument(
        "--joiner",
        type=str,
        required=True,
        help="Path to the joiner model",
    )

    parser.add_argument(
        "--decoding-method",
        type=str,
        default="greedy_search",
        help="Valid values are greedy_search and modified_beam_search",
    )

    parser.add_argument(
        "--provider",
        type=str,
        default="cpu",
        help="Valid values: cpu, cuda, coreml",
    )
    
    parser.add_argument(
        "--db_path",
        type=str,
        #required=True,
        default="./../../../pear.db",
        help="Path to the pear.db",
    )   

    parser.add_argument(
        "--basic_setting_table_name",
        type=str,
        #required=True,
        default="admin_digital_human_basic_setting",
        help="This table contains some basic settings for digital humans.",
    )

    parser.add_argument(
        "--qa_pairs_table_name",
        type=str,
        #required=True,
        default="admin_digital_human_interactive",
        help="This table contains some QA pairs for digital humans.",
    )

    parser.add_argument(
        "--hotwords-file",
        type=str,
        default="",
        help="""
        The file containing hotwords, one words/phrases per line, and for each
        phrase the bpe/cjkchar are separated by a space. For example:

        ▁HE LL O ▁WORLD
        你 好 世 界
        """,
    )
    
    parser.add_argument(
        "--hotwords-score",
        type=float,
        default=1.5,
        help="""
        The hotword score of each token for biasing word/phrase. Used only if
        --hotwords-file is given.
        """,
    )

    parser.add_argument(
        "--blank-penalty",
        type=float,
        default=0.0,
        help="""
        The penalty applied on blank symbol during decoding.
        Note: It is a positive value that would be applied to logits like
        this `logits[:, 0] -= blank_penalty` (suppose logits.shape is
        [batch_size, vocab] and blank id is 0).
        """,
    )

    return parser.parse_args()


#更新数据库
async def dymic_update_global_vars(client,GlobalVarsUpdate,args):
    while True:
        try:
            if client.get_configurationModificationCode() == "1":
                GlobalVarsUpdate.update_sentences_embedding(args)
                await client.change_configurationModificationCode()
            else:
                time.sleep(3)
        except Exception as e:
            pass            


os.environ["TOKENIZERS_PARALLELISM"] = "false"
logger = AppLogger()

#校验sounddevice
try:
    import sounddevice as sd
except ImportError:
    print("Please install sounddevice first. You can use")
    print()
    print("  pip install sounddevice")
    print()
    print("to install it")
    sys.exit(-1)

#问答主进程            
async def main():
    args = get_args()
 
    recognizer,model_ort,tokenizer = create_recognizer(args)
    #与赤兔建立WebSocket连接
    client = WebSocketClient("ws://localhost:8765", logger)
    client.connect()
    await assert_qa_data(args,client)    
    
    GlobalVarsUpdate = VarsUpdate(model_ort,tokenizer)
    GlobalVarsUpdate.update_sentences_embedding(args)
    import threading
    update_thread = threading.Thread(target=lambda: asyncio.run(dymic_update_global_vars(client, GlobalVarsUpdate, args)))
    update_thread.daemon = True
    update_thread.start()
    #创建识别音频输入流格式
    stream = recognizer.create_stream()
    last_result = ""
    segment_id = 0
    sample_rate = 16000
    time.sleep(1)
    await client.send_message(qa_empty_message_push_struct(200,"问答启动成功",8))
    samples_queue = multiprocessing.Queue() 

    try:
        #打开麦克风
        producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
        producer_process.start()
        print("Started! Please speak")
    except:
        logger.log_message('error', '开启麦克风拾音失败。')

    #唤醒循环
    while True:
        #基础配置
        wakeup_words = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['awaken_greeting'][0]
        welcome_greeting = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_greeting'][0]
        welcome_video_url = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcome_video_url'][0]
        welcomeVideoDuration = get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['welcomeVideoDuration'][0]
        command_words_dict = json.loads(get_sqlite_table_info(args.db_path, args.basic_setting_table_name)['command_words'].tolist()[0])
        attendance_word = command_words_dict["attendanceMode"]
        exit_attendance_word = command_words_dict["exitAttendanceMode"]
        muteUnattended_word = command_words_dict["muteUnattendedMode"]
        muteAssistant_word = command_words_dict["muteAssistantMode"]
        enterPublicity_word = command_words_dict["enterPublicity"]
        exitPublicity_word = command_words_dict["exitPublicity"]
        #同一人人脸第一次唤醒
        if client.get_face_status() == "1" and client.get_propaganda_status() != "1":
            await client.send_message(answer_push_json_message_struct(welcome_video_url,welcome_greeting,welcomeVideoDuration,"","", 0))
            await client.send_message(qa_empty_message_push_struct(200,"",10))
            await client.send_message(qa_empty_message_push_struct(201,"您可以跟我对话啦！"))
            await client.change_face_status()
            await client.change_status()
            #关闭麦克风
            producer_process.terminate()
            producer_process.join()
            logger.log_message('info', '唤醒成功。')
            #进入多轮对话
            await rec_pipeline(recognizer, model_ort, tokenizer, client, args, samples_queue, logger, GlobalVarsUpdate)
            await client.send_message(face_exit_wake_up_push_struct())   
            producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
            producer_process.start()

        #同一人人脸第二次唤醒
        elif client.get_face_status() == "2" and client.get_propaganda_status() != "1":
            await client.change_face_status()
            producer_process.terminate()
            producer_process.join()
            await client.send_message(qa_empty_message_push_struct(200,"",10))
            await client.send_message(qa_empty_message_push_struct(202,"您可以跟我对话啦！"))
            logger.log_message('info', '唤醒成功。')
            await rec_pipeline(recognizer, model_ort, tokenizer, client, args, samples_queue, logger, GlobalVarsUpdate)
            await client.send_message(face_exit_wake_up_push_struct())               
            producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
            producer_process.start()

        #语音唤醒
        if client.get_status() == "1":
            sample_time = time.time()   
            #判断是否有音频
            if samples_queue.empty():
                no_sample_dur = time.time() - sample_time
                if no_sample_dur > 3:
                    await client.send_message(restart_qa_service_push_struct())
                else:
                    pass
                continue
            else:
                samples = samples_queue.get()
                sample_time = time.time()
                
            stream.accept_waveform(sample_rate, samples)
            while recognizer.is_ready(stream):
                recognizer.decode_stream(stream)
            is_endpoint = recognizer.is_endpoint(stream)
            result = recognizer.get_result(stream) 
            #去除识别结果中的英文
            if result and (last_result != result):
                result = re.sub(r'[A-Z]', '', result)
                last_result = result
                print("\r{}:{}".format(segment_id, result), end="", flush=True)
            if is_endpoint:
                if result:
                    if len(result) > 1:
                        segment_id += 1
                        await client.send_message(qa_empty_message_push_struct(200,"",10))
                        print("\r{}:{}".format(segment_id, result), flush=True)

                    if client.get_propaganda_status() != "1" and client.get_attendanceStatus() != "1":
                        #唤醒回复
                        if wakeup_words in result:
                            try:
                                producer_process.terminate()
                                producer_process.join()
                                await client.send_message(start_qa_service_push_struct())
                                await client.send_message(question_push_json_message_struct(result))
                                await client.send_message(answer_push_json_message_struct(welcome_video_url,welcome_greeting,welcomeVideoDuration,"",""))
                                await client.send_message(qa_empty_message_push_struct(201,"您可以跟我对话啦！"))
                                await client.change_status()
                                logger.log_message('info', '唤醒成功。')
                            except:
                                logger.log_message('error', '唤醒失败。')
                            await rec_pipeline(recognizer, model_ort, tokenizer, client, args, samples_queue, logger, GlobalVarsUpdate)
                            await client.send_message(face_exit_wake_up_push_struct())              
                            producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
                            producer_process.start()

                        #进入宣传
                        elif enterPublicity_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))
                            await client.change_enter_propaganda_status()   
                        #退出宣传        
                        elif exitPublicity_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
                            await client.change_exit_propaganda_status()       
                        #进入打卡                                  
                        elif attendance_word in result:                         
                            await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                            await client.send_message(command_word_push_json_message_struct("attendanceMode",result,attendance_word))
                            await client.change_enterAttendanceStatus()
                            logger.log_message('info', '往赤兔发送打卡命令词发送成功。')
                        #退出打卡    
                        elif exit_attendance_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已退出打卡模式"))
                            await client.send_message(exit_attendance_mode_push_struct())
                            await client.send_message(command_word_push_json_message_struct("exitAttendanceMode",result,exit_attendance_word))
                            await client.change_exitAttendanceStatus()  
                        #进入助理模式            
                        elif muteAssistant_word in result:
                            try:
                                await client.send_message(qa_empty_message_push_struct(200,"已进入助理模式",2))
                                logger.log_message('info', '切换助理模式成功。')
                            except:
                                logger.log_message('error', '切换助理模式失败。')
                        #进入无人值守模式        
                        elif muteUnattended_word in result:
                            try:
                                await client.send_message(qa_empty_message_push_struct(200,"已进入无人值守模式",1))
                                logger.log_message('info', '切换无人值守模式成功。')
                            except:
                                logger.log_message('error', '切换无人值守模式失败。')

                    elif client.get_propaganda_status() == "1" and client.get_attendanceStatus() != "1":
                        if enterPublicity_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已进入宣传模式",5))
                            await client.change_enter_propaganda_status()
                        elif exitPublicity_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已退出宣传模式",6))
                            await client.change_exit_propaganda_status()
                        elif attendance_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已进入打卡模式"))
                            await client.send_message(command_word_push_json_message_struct("attendanceMode",result,attendance_word))
                            await client.change_enterAttendanceStatus()
                            logger.log_message('info', '往赤兔发送打卡命令词发送成功。')

                    elif client.get_attendanceStatus() == "1":
                        if exit_attendance_word in result:
                            await client.send_message(qa_empty_message_push_struct(200,"已退出打卡模式"))
                            await client.send_message(command_word_push_json_message_struct("exitAttendanceMode",result,exit_attendance_word))
                            await client.send_message(exit_attendance_mode_push_struct())
                            await client.change_exitAttendanceStatus()
                
                recognizer.reset(stream)    

        elif client.get_status() != "1":
            try:
                producer_process.terminate()
                producer_process.join()
            except Exception as e:
                pass     
            producer_process = multiprocessing.Process(target=producer_general,args=(logger,samples_queue))
            producer_process.start()

if __name__=="__main__":
    asyncio.run(main())
