import json
from log import mylog
from applications.common.value_dispatch import value_dispatch
from applications.digital_human.view.utils import ws_send_screen_msg, ws_send_instruct_algo
from applications.digital_human.view import recv_basic_settings
from applications.digital_human.common.common_utils import basic_settings_config_path, read_config
from applications.digital_human.view import recv_face_info
from applications.digital_human.common.websocket import get_basic_info

respond_topic = "digitalHumanTopic"

@value_dispatch
def digital_human_dispatch_tasks(messageType):
    mylog.info("MQTT: messageType is error.")

# 接收平台端下发的数字员工背景
@digital_human_dispatch_tasks.register("backGround")
def recv_backGround(*args):
    messageType, client, msg, SN, app = args
    # 背景路径固定，背景的图片名称固定
    back_ground_img_path = "/mnt/keep/workspace/digital_human_data/basicSettings/background.jpg"
    recv_basic_settings.recv_backGround_or_speaker(client, msg, SN, respond_topic, back_ground_img_path, app)
    # 背景修改之后要通知前端，并将新的背景发送到前端
    ws_send_screen_msg(str(get_basic_info.get_back_ground()))

# 接收平台端下发的数字员工形象，逻辑同下发背景一样
@digital_human_dispatch_tasks.register("speaker")
def recv_speaker(*args):
    messageType, client, msg, SN, app = args
    speaker_img_path = "/mnt/keep/workspace/digital_human_data/basicSettings/speaker.jpg"
    recv_basic_settings.recv_backGround_or_speaker(client, msg, SN, respond_topic, speaker_img_path, app)
    ws_send_screen_msg(str(get_basic_info.get_speaker()))

# 接收平台端下发的交互信息
@digital_human_dispatch_tasks.register("interaction")
def recv_interaction(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_intersection(client, msg, SN, respond_topic)
    ws_send_screen_msg(str(get_basic_info.get_interaction()))

# 接收平台端下发的问题指引
@digital_human_dispatch_tasks.register("questionGuidance")
def recv_questionGuidance(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_question_guidance(client, msg, SN, respond_topic)
    ws_send_screen_msg(str(get_basic_info.get_question_guide()))

# 接收平台端下发的数字员工的有效期时间
@digital_human_dispatch_tasks.register("servicePeriod")
def recv_servicePeriod(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_service_period(client, msg, SN, respond_topic)

# 接收平台端下发的欢迎语视频
@digital_human_dispatch_tasks.register("awakenGreeting")
def recv_awaken_Greeting(*args):
    messageType,client, msg, SN, app = args
    recv_basic_settings.recv_awakenGreeting(client, msg, SN, respond_topic, app)

# 接收平台端配置的敏感词回复视频
@digital_human_dispatch_tasks.register("sensitiveWords")
def recv_sensitive_Words(*args):
    messageType,client, msg, SN, app = args
    recv_basic_settings.recv_sensitiveWords(client, msg, SN, respond_topic, app)

# 接收平台端下发的问答库
@digital_human_dispatch_tasks.register("requestQuestionVideo")
def recv_request_QuestionVideo(*args):
    messageType,client, msg, SN, app = args
    recv_basic_settings.recv_requestQuestionVideo(client, msg, SN, respond_topic, app)

# 接收平台端修改问答对状态的命令
@digital_human_dispatch_tasks.register("requestQuestionStatus")
def recv_request_QuestionStatus(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_requestQuestionStatus(client, msg, SN, respond_topic, app)

# 接收兜底话术回复视频
@digital_human_dispatch_tasks.register("commonReplyAudio")
def recv_common_ReplyAudio(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_commonReplyAudio(client, msg, SN, respond_topic, app)

# 增加人脸信息
@digital_human_dispatch_tasks.register("addFacePictureMessage")
def recv_addFacePicture(*args):
    messageType, client, msg, SN, app = args
    recv_face_info.recv_face_add(client, msg, SN, respond_topic, app)

# 修改人脸信息
@digital_human_dispatch_tasks.register("updateFacePictureMessage")
def recv_updateFacePicture(*args):
    messageType, client, msg, SN, app = args
    recv_face_info.recv_face_update(client, msg, SN, respond_topic, app)

# 删除人脸信息
@digital_human_dispatch_tasks.register("deletefaceMessage")
def recv_deleteFacePicture(*args):
    messageType, client, msg, SN, app = args
    recv_face_info.recv_face_delete(client, msg, SN, respond_topic, app)

# 删除人脸库
@digital_human_dispatch_tasks.register("deletefaceLibrary")
def recv_deleteFaceLib(*args):
    messageType, client, msg, SN, app = args
    recv_face_info.delete_face_lib(client, msg, SN, respond_topic, app)

# 更新人脸库 
@digital_human_dispatch_tasks.register("updateFaceLibrary")
def recv_updateFaceLibrary(*args):
    messageType, client, msg, SN, app = args
    recv_face_info.update_face_library(client, msg, SN, respond_topic, app)

# 修改哨兵模式相关信息
@digital_human_dispatch_tasks.register("serviceMode")  
def recv_serviceModeCommand(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_service_mode_control(client, msg, SN, respond_topic, app)

# 处理钉钉发来的指令消息，转发就可以
@digital_human_dispatch_tasks.register("robotMessage")
def recv_robotMessage(*args):
    messageType, client, msg, SN, app = args
    try:
        conf = read_config()
        if conf["currentMode"] != "SentryMode" and "safeguard" not in conf["currentStatus"]:
            if msg["messageBody"]["robotMessageType"] == "1":
                msg_res = {
                    "messageType": "messageToast",
                    "messageBody": {
                        "mode": "4",
                        "code": 300,
                        "text": msg["messageBody"]["robotMessageInfo"]
                    }
                }
                ws_send_screen_msg(str(json.dumps(msg_res)))
            elif msg["messageBody"]["robotMessageType"] == "0":
                msg_res = {
                    "messageType": "robotMessage",
                    "robotMessageInfo": msg["messageBody"]["robotMessageInfo"].split("#")[1].strip()
                }
                ws_send_instruct_algo(str(json.dumps(msg_res)))
        else:
            mylog.info("MQTT：接收到机器人发来的指令，但是处于哨兵模式，因此不转发。")
            
        res_code = 200
        res_msg = "成功"
    except Exception as e:
        mylog.info(e)
        res_code = 500
        res_msg = "失败"
    recv_basic_settings.recv_robot_message_return(res_code, res_msg, SN, respond_topic, msg)

# 接收平台端发送的宣传模式/休眠模式图片    
@digital_human_dispatch_tasks.register("publicityRelease")  
def recv_propagandaImages(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_minio_images(client, msg, SN, respond_topic, app)
    if msg["messageBody"]["publicityType"] == "0":
        ws_send_screen_msg(str(get_basic_info.get_propaganda_pics()))
    elif msg["messageBody"]["publicityType"] == "1":
        ws_send_screen_msg(str(get_basic_info.get_sleepy_pics()))
    else:
        pass

# 系统更新
@digital_human_dispatch_tasks.register("packageUpdateSend")  
def recv_packageUpdateMsg(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_system_update(client, msg, SN, respond_topic, app)

# 问答库同步
@digital_human_dispatch_tasks.register("syncQuestionAnswer")  
def recv_syncQuestionAnswerMsg(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_sync_question_answer(client, msg, SN, respond_topic, app)

# 删除问答对
@digital_human_dispatch_tasks.register("deleteQuestionAnswer")
def deleteQuestionAnswerMsg(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_delete_question_answer(client, msg, SN, respond_topic, app)

# 横竖屏设置
@digital_human_dispatch_tasks.register("screenInfo")
def recv_screenInfo(*args):
    messageType, client, msg, SN, app = args
    recv_basic_settings.recv_screen_info(client, msg, SN, respond_topic)
    ws_send_screen_msg(str(get_basic_info.get_screen_dirction()))