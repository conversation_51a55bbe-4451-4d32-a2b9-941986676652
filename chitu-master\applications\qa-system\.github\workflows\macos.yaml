name: macos

on:
  push:
    branches:
      - master
    tags:
      - '*'
    paths:
      - '.github/workflows/macos.yaml'
      - '.github/scripts/test-kws.sh'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - '.github/scripts/test-online-ctc.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/macos.yaml'
      - '.github/scripts/test-kws.sh'
      - '.github/scripts/test-online-transducer.sh'
      - '.github/scripts/test-online-paraformer.sh'
      - '.github/scripts/test-offline-transducer.sh'
      - '.github/scripts/test-offline-ctc.sh'
      - '.github/scripts/test-offline-tts.sh'
      - '.github/scripts/test-online-ctc.sh'
      - 'CMakeLists.txt'
      - 'cmake/**'
      - 'sherpa-onnx/csrc/*'
      - 'sherpa-onnx/c-api/*'

  workflow_dispatch:

concurrency:
  group: macos-${{ github.ref }}
  cancel-in-progress: true

jobs:
  macos:
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.build_type }} ${{ matrix.lib_type }} tts-${{ matrix.with_tts }}
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest]
        build_type: [Release, Debug]
        lib_type: [static, shared]
        with_tts: [ON, OFF]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ matrix.os }}-${{ matrix.build_type }}-${{ matrix.lib_type }}-tts-${{ matrix.with_tts }}

      - name: Configure CMake
        shell: bash
        run: |
          export CMAKE_CXX_COMPILER_LAUNCHER=ccache
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          cmake --version

          mkdir build
          cd build
          lib_type=${{ matrix.lib_type }}
          if [[ $lib_type == "static" ]]; then
            BUILD_SHARED_LIBS=OFF
          else
            BUILD_SHARED_LIBS=ON
          fi

          cmake -DSHERPA_ONNX_ENABLE_TTS=${{ matrix.with_tts }} -D BUILD_SHARED_LIBS=$BUILD_SHARED_LIBS -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} -DCMAKE_OSX_ARCHITECTURES='arm64;x86_64' -DCMAKE_INSTALL_PREFIX=./install ..

      - name: Build sherpa-onnx for macos
        shell: bash
        run: |
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"

          cd build
          make -j2
          make install

          ls -lh lib
          ls -lh bin

          file ./bin/sherpa-onnx

      - name: Display dependencies of sherpa-onnx for macos
        shell: bash
        run: |
          file bin/sherpa-onnx
          otool -L build/bin/sherpa-onnx
          otool -l build/bin/sherpa-onnx

      - name: Test C API
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export SLID_EXE=spoken-language-identification-c-api
          export SID_EXE=speaker-identification-c-api

          .github/scripts/test-c-api.sh

      - name: Test spoken language identification (C++ API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline-language-identification

          .github/scripts/test-spoken-language-identification.sh

      - name: Test transducer kws
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-keyword-spotter

          .github/scripts/test-kws.sh

      - name: Test online CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-ctc.sh

      - name: Test offline TTS
        if: matrix.with_tts == 'ON'
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline-tts

          .github/scripts/test-offline-tts.sh

      - name: Test online paraformer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-paraformer.sh

      - name: Test offline Whisper
        if: matrix.build_type != 'Debug'
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          .github/scripts/test-offline-whisper.sh

      - name: Test offline CTC
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          .github/scripts/test-offline-ctc.sh

      - name: Test offline transducer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx-offline

          .github/scripts/test-offline-transducer.sh

      - name: Test online transducer
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=sherpa-onnx

          .github/scripts/test-online-transducer.sh

      - name: Test online transducer (C API)
        shell: bash
        run: |
          export PATH=$PWD/build/bin:$PATH
          export EXE=decode-file-c-api

          .github/scripts/test-online-transducer.sh

      - name: Copy files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          if [[ ${{ matrix.with_tts }} ]]; then
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-osx-universal2-${{ matrix.lib_type }}
          else
            dst=sherpa-onnx-${SHERPA_ONNX_VERSION}-osx-universal2-${{ matrix.lib_type }}-no-tts
          fi
          mkdir $dst

          cp -a build/install/bin $dst/
          cp -a build/install/lib $dst/
          cp -a build/install/include $dst/

          brew install tree
          tree $dst

          tar cjvf ${dst}.tar.bz2 $dst

      - name: Release pre-compiled binaries and libs for macOS
        if: (github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa') && github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@v2
        with:
          file_glob: true
          overwrite: true
          file: sherpa-onnx-*osx-universal2*.tar.bz2
