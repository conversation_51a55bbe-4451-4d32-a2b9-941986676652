import os
import requests

from log import mylog
from applications.mqtt.mqtt_connect import mqtt_client
from applications.digital_human.models import DigitalHumanFaceBase
from applications.digital_human.common.utils.face import face_recognition

from applications.extensions import db

def get_minio_url(url, app):
    return "http://" + app.config['MINIO_ADDRESS'] + url

def get_face_fassi_id(img_url, person_id, lib_id, name, operate_type):
    face_img_dir = "/mnt/keep/workspace/digital_human_data/face_info/face_images"
    if not os.path.exists(face_img_dir):
        os.makedirs(face_img_dir)

    jpg_name = img_url.split('/')[-1]
    frame = requests.get(img_url)
    img_path = os.path.join(face_img_dir, jpg_name)
    with open(img_path, mode="wb") as f:
        f.write(frame.content)
    
    if operate_type == "add":
        faiss_id = face_recognition.add_face_to_index(img_path, person_id, lib_id, name)
        os.remove(img_path)
        return faiss_id
    elif operate_type == "update":
        result = face_recognition.update_face_in_index(img_path, person_id, lib_id, name)
        os.remove(img_path)
        return result

# 获取人脸特征，入人脸库和人脸特征向量库
def recv_face_add(client, msg, SN, respond_topic, app):
    messageType = msg["messageType"]
    faces = []
    res_code = 200
    res_msg = "成功"
    for info in msg["messageBody"]:
        for item in info["faceMessages"]:
            faiss_id = get_face_fassi_id(get_minio_url(item["facePicUrl"], app), item["idNumber"], info["faceLibraryId"], item["name"], "add")
            if faiss_id is None:
                mylog.info("获取人脸特征id失败")
                res_code = 500
                res_msg = "失败"
                break
            else:
                face = DigitalHumanFaceBase(
                    idNumber = item["idNumber"],
                    name = item["name"],
                    remark = item["remark"] if "remark" in item.keys() else None,
                    faceLibraryId = info["faceLibraryId"],
                    faceLibraryName = info["faceLibraryName"],
                    sex = item["sex"] if "sex" in item.keys() else "2",
                    phone = item["phone"] if "phone" in item.keys() else None,
                    faissId = faiss_id
                )
                faces.append(face)
                
        if res_code == 500:
            break
    
    if res_code == 200:
        with app.app_context():
            try:
                db.session.add_all(faces)
                db.session.commit()
            except:
                res_code = 500
                res_msg = "失败"
    
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    # publish_mqtt_msg(client, respond_topic, respond_info)
    mqtt_client.publish(respond_topic, respond_info)

# 获取人脸特征，更新人脸库和人脸特征向量库   
def recv_face_update(client, msg, SN, respond_topic, app):
    messageType = msg["messageType"]
    res_code = 200
    res_msg = "成功"
    with app.app_context():
        for info in msg["messageBody"]:
            for item in info["faceMessages"]:
                if "facePicUrl" in item.keys():
                    result = get_face_fassi_id(get_minio_url(item["facePicUrl"], app), item["idNumber"], info["faceLibraryId"], item["name"], "update")
                    if result is None:
                        mylog.info("更新人脸特征失败")
                        res_code = 500
                        res_msg = "失败"
                        break
                if res_code == 200:
                    try:
                        DigitalHumanFaceBase.query.filter_by(idNumber=item["idNumber"], faceLibraryId=info["faceLibraryId"]).update({
                            "name": item["name"],
                            "remark": item["remark"] if "remark" in item.keys() else None,
                            "sex": item["sex"] if "sex" in item.keys() else "2",
                            "phone": item["phone"] if "phone" in item.keys() else None
                        })
                        db.session.commit()
                    except:
                        res_code = 500
                        res_msg = "失败"
                        break
            if res_code == 500:
                break
                
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    # publish_mqtt_msg(client, respond_topic, respond_info)
    mqtt_client.publish(respond_topic, respond_info)
    
# 删除人脸信息
def recv_face_delete(client, msg, SN, respond_topic, app):
    messageType = msg["messageType"]
    res_code = 200
    res_msg = "成功"
    with app.app_context():
        for info in msg["messageBody"]:
            for item in info["idNumbers"]:
                res = face_recognition.delete_face_from_index(item, info["faceLibraryId"])
                if res is None:
                    mylog.info("删除人脸库特征失败")
                    res_code = 500
                    res_msg = "失败"
                    break
                else:
                    try:
                        DigitalHumanFaceBase.query.filter_by(idNumber=item, faceLibraryId=info["faceLibraryId"]).delete()
                        db.session.commit()
                    except:
                        res_code = 500
                        res_msg = "失败"
                        break
            if res_code == 500:
                break

    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    # publish_mqtt_msg(client, respond_topic, respond_info)
    mqtt_client.publish(respond_topic, respond_info)
    
# 删除人脸库信息    
def delete_face_lib(client, msg, SN, respond_topic, app):
    messageType = msg["messageType"]
    res_code = 200
    res_msg = "成功"
    with app.app_context():
        for info in msg["messageBody"]:
            all_face = DigitalHumanFaceBase.query
            all_person_id = db.session.query(DigitalHumanFaceBase).filter_by(faceLibraryId=info["faceLibraryId"]).all()
            for person_id in all_person_id:
                res = face_recognition.delete_face_from_index(person_id.idNumber, info["faceLibraryId"])
                if res is None:
                    mylog.info("删除人脸库特征失败")
                    res_code = 500
                    res_msg = "失败"
                    break
            if res_code == 500: 
                break
            else:  
                try:
                    DigitalHumanFaceBase.query.filter_by(faceLibraryId=info["faceLibraryId"]).delete()
                    db.session.commit()
                except:
                    res_code = 500
                    res_msg = "失败"
                    break

    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    # publish_mqtt_msg(client, respond_topic, respond_info)
    mqtt_client.publish(respond_topic, respond_info)
    
# 更新人脸库信息    
def update_face_library(client, msg, SN, respond_topic, app):
    messageType = msg["messageType"]
    res_code = 200
    res_msg = "成功"
    with app.app_context():
        for info in msg["messageBody"]:
            try:
                DigitalHumanFaceBase.query.filter_by(faceLibraryId=info["faceLibraryId"]).update({"faceLibraryName": info["faceLibraryName"]})
                db.session.commit()
            except:
                res_code = 500
                res_msg = "失败"
                break
    respond_info = {}
    respond_info["code"] = res_code
    respond_info["data"] = {
        "messageType": msg["messageType"],
        "messageBody": SN
    }
    respond_info["msg"] = res_msg
    # publish_mqtt_msg(client, respond_topic, respond_info)
    mqtt_client.publish(respond_topic, respond_info)