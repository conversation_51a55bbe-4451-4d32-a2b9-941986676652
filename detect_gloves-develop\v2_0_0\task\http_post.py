import requests
import time
import copy
import json
from  threading import Thread
from ..utils.log import mylog


class HttpPost(object):
    def __init__(self, post_way):
        self.data = []
        self.post_url = None
        self.post_way = post_way
        http_run = Thread(target=self.post_message, daemon=True)
        http_run.start()  
        mylog.info('加载算法告警模块')      
    
    def post_message(self):
        """上传告警和图片"""
        while True:
            try:
                if len(self.data) == 0:
                    time.sleep(1)
                    continue
                number = len(self.data)
                for _ in range(number):
                    if len(self.data) >= 20:  # 防止内存爆炸
                        self.data.pop(0)
                for data in copy.deepcopy(self.data):
                    if self.post_way == 'base64':
                        # data = json.dumps(data, ensure_ascii=False)
                        requests.post(self.post_url, json=data, timeout=10)
                        # print('---------post message , time ', time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
                        mylog.info('发送告警成功')
                    if self.post_way == 'byte':
                        requests.post(self.post_url, files=data, timeout=10)
                        # print('---------post message , time ', time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
                        mylog.info('发送告警成功')
                    self.data.pop(0)  # 这里删除上传完成的信息部分
            except Exception as e:
                mylog.error(f'上传告警错误: {e} \n')
                time.sleep(60)