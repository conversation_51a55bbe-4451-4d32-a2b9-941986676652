import datetime
from applications.extensions import db


class Mail(db.Model):
    __tablename__ = 'admin_email'
    id = db.<PERSON>umn(db.Integer, primary_key=True, autoincrement=True, comment='邮件编号')
    send_email = db.Column(db.String(64), comment='发件邮箱地址')
    password = db.Column(db.String(64), comment='发件邮箱登录密码')
    type = db.Column(db.String(2), comment='发件邮箱类型')
    type_name = db.Column(db.String(16), comment='发件邮箱类型名称')
    smtp_server = db.Column(db.String(32), comment='SMTP服务器')
    # subject = db.Column(db.String(128), comment='邮件主题')
    # content = db.Column(db.Text(), comment='邮件正文')
    send_id = db.Column(db.Integer, comment='发件邮箱id')
   