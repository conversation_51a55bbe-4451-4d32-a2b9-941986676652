name: wasm-simd-hf-space-zh-en-asr-zipformer

on:
  release:
    types:
      - published

  workflow_dispatch:

concurrency:
  group: wasm-simd-hf-space-zh-en-asr-zipformer-${{ github.ref }}
  cancel-in-progress: true

jobs:
  wasm-simd-hf-space-zh-en-asr-zipformer:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install emsdk
        uses: mymindstorm/setup-emsdk@v14

      - name: View emsdk version
        shell: bash
        run: |
          emcc -v
          echo "--------------------"
          emcc --check

      - name: Download model files
        shell: bash
        run: |
          cd wasm/asr/assets
          ls -lh
          echo "----------"
          wget -q https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20.tar.bz2
          tar xvf sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20.tar.bz2
          rm sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20.tar.bz2
          mv sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/encoder-epoch-99-avg-1.int8.onnx encoder.onnx
          mv sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/decoder-epoch-99-avg-1.onnx decoder.onnx
          mv sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.int8.onnx joiner.onnx
          mv sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/tokens.txt ./
          rm -rf sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/

          ls -lh

      - name: Build sherpa-onnx for WebAssembly (ASR)
        shell: bash
        run: |
          ./build-wasm-simd-asr.sh

      - name: collect files
        shell: bash
        run: |
          SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

          dst=sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-zh-en-asr-zipformer
          mv build-wasm-simd-asr/install/bin/wasm/asr $dst
          ls -lh $dst
          tar cjfv ${dst}.tar.bz2 ./${dst}

      - name: Upload wasm files
        uses: actions/upload-artifact@v4
        with:
          name: sherpa-onnx-wasm-simd-zh-en-asr-zipformer
          path: ./sherpa-onnx-wasm-simd-*.tar.bz2

      - name: Publish to ModelScope
        # if: false
        env:
          MS_TOKEN: ${{ secrets.MODEL_SCOPE_GIT_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf ms
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://www.modelscope.cn/studios/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en.git ms
            cd ms
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-*/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://oauth2:${MS_TOKEN}@www.modelscope.cn/studios/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en.git

      - name: Publish to huggingface
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        uses: nick-fields/retry@v2
        with:
          max_attempts: 20
          timeout_seconds: 200
          shell: bash
          command: |
            SHERPA_ONNX_VERSION=v$(grep "SHERPA_ONNX_VERSION" ./CMakeLists.txt  | cut -d " " -f 2  | cut -d '"' -f 2)

            git config --global user.email "<EMAIL>"
            git config --global user.name "Fangjun Kuang"

            rm -rf huggingface
            export GIT_LFS_SKIP_SMUDGE=1

            git clone https://huggingface.co/spaces/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en huggingface
            cd huggingface
            rm -fv *.js
            rm -fv *.data
            git fetch
            git pull
            git merge -m "merge remote" --ff origin main

            cp -v ../sherpa-onnx-wasm-simd-${SHERPA_ONNX_VERSION}-*/* .

            git status
            git lfs track "*.data"
            git lfs track "*.wasm"
            ls -lh

            git add .
            git commit -m "update model"
            git push https://csukuangfj:$<EMAIL>/spaces/k2-fsa/web-assembly-asr-sherpa-onnx-zh-en main
