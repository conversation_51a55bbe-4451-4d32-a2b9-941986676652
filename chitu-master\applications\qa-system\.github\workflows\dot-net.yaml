name: release-nuget-package

on:
  workflow_dispatch:

concurrency:
  group: release-nuget-package
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  release-nuget-package:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: |
            6.0.x
            7.0.x

      - name: Check dotnet
        run: dotnet --info

      - name: Build
        shell: bash
        run: |
          cd scripts/dotnet
          ./run.sh

          ls -lh /tmp/packages

      - name: publish .Net packages to nuget.org
        if: github.repository_owner == 'csukuangfj' || github.repository_owner == 'k2-fsa'
        shell: bash
        env:
          API_KEY: ${{ secrets.NUGET_API_KEY }}
        run: |
          # API_KEY is valid until 2024.05.02
          cd /tmp/packages
          dotnet nuget push ./org.k2fsa.sherpa.onnx.*.nupkg --skip-duplicate --api-key $API_KEY --source https://api.nuget.org/v3/index.json
