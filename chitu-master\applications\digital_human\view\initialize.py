import os
import shutil
from applications.extensions import db

from applications.digital_human.models import DigitalHumanFaceBase, digital_human_interactive, digital_human_basic_setting

digital_human_data_path = "/mnt/keep/workspace/digital_human_data"
face_file_path = os.path.join(digital_human_data_path, "face_info/face_files")
face_files = ["stranger_face_index.faiss", "stranger_id_time.pkl", "face_index.faiss", "id_name_map.pkl"]

video_path = os.path.join(digital_human_data_path, "digital_human_videos")
default_setting_path = os.path.join(digital_human_data_path, "basicSettings/default_setting")
def digital_human_sys_init():
    # 人脸库清理
    for face_file in face_files:
        if os.path.exists(os.path.join(face_file_path, face_file)):
            os.remove(os.path.join(face_file_path, face_file))
            
    db.session.query(DigitalHumanFaceBase).delete()
    db.session.commit()
    
    # 问答库清理
    if os.path.exists(os.path.join(video_path, "qa_videos")):
        shutil.rmtree(os.path.join(video_path, "qa_videos"))
    os.makedirs(os.path.join(video_path, "qa_videos"))
    
    db.session.query(digital_human_interactive).delete()
    db.session.commit()
    
    # 基础配置库清理
    if os.path.exists(os.path.join(video_path, "wsb_videos")):
        shutil.rmtree(os.path.join(video_path, "wsb_videos"))
    os.makedirs(os.path.join(video_path, "wsb_videos"))
    
    db.session.query(digital_human_basic_setting).delete()
    db.session.commit()
    
    # 背景形象恢复默认
    shutil.copy(os.path.join(default_setting_path, "background_default.jpg"), "/mnt/keep/workspace/digital_human_data/basicSettings/background.jpg")
    shutil.copy(os.path.join(default_setting_path, "speaker_default.jpg"), "/mnt/keep/workspace/digital_human_data/basicSettings/speaker.jpg")
    
    # 删除掉下发的宣传
    if os.path.exists(os.path.join(digital_human_data_path, "basicSettings/propaganda_images")):
        shutil.rmtree(os.path.join(digital_human_data_path, "basicSettings/propaganda_images"))
    if os.path.exists(os.path.join(digital_human_data_path, "basicSettings/sleepy_images")):
        shutil.rmtree(os.path.join(digital_human_data_path, "basicSettings/sleepy_images"))
    
    # 修改定时任务
    
    # 修改配置恢复到初始
    
    # 进入助理模式
    
    # 