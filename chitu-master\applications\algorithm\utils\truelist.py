from concurrent.futures import ThreadPoolExecutor

class TrueList:

    def __init__(self):
        self.trueL = {}
    def addUrl(self, key):
        self.trueL[key] = {"thread": None,
                           "videoCap": None,
                           "threadPool": None,
                           "alarmType": []}
    def addThreadpool(self, key):
        if self.trueL[key]["threadPool"] is None:
            self.trueL[key]["threadPool"] = ThreadPoolExecutor(max_workers=3)
    
    def addSDThread(self, key, stThread):
        self.trueL[key]["thread"] = stThread
    
    def delThreadPools(self, key):
        if self.trueL[key]["threadPool"] is not None:
            self.trueL[key]["threadPool"].shutdown(wait=False)
            self.trueL[key]["threadPool"] = None
            
    def addVideoCap(self, key, videocap):
        if self.trueL[key]["videoCap"] is None:
            self.trueL[key]["videoCap"] = videocap
    
    def updateAlarmType(self, key, alarmType):
        self.trueL[key]["alarmType"] = alarmType
   
    def delAlarmType(self, key):
        self.trueL[key]["alarmType"] = []